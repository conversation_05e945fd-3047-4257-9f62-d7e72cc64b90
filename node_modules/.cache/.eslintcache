[{"/home/<USER>/Desktop/On-boarding_Form_App/src/index.tsx": "1", "/home/<USER>/Desktop/On-boarding_Form_App/src/App.tsx": "2", "/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx": "3"}, {"size": 363, "mtime": 1756544447958, "results": "4", "hashOfConfig": "5"}, {"size": 534, "mtime": 1756544911995, "results": "6", "hashOfConfig": "5"}, {"size": 76349, "mtime": 1757419943128, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jajov7", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/Desktop/On-boarding_Form_App/src/index.tsx", [], [], "/home/<USER>/Desktop/On-boarding_Form_App/src/App.tsx", [], [], "/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx", ["17"], [], {"ruleId": "18", "severity": 1, "message": "19", "line": 687, "column": 21, "nodeType": "20", "endLine": 691, "endColumn": 23}, "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement"]