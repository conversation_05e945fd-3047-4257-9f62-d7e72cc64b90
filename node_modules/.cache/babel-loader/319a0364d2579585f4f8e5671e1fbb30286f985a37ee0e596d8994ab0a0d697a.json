{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OnboardingForm = () => {\n  _s();\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [errorMessage, setErrorMessage] = useState(null);\n  const [showJsonPreview, setShowJsonPreview] = useState(false);\n  const [jsonPreviewData, setJsonPreviewData] = useState('');\n  const {\n    token\n  } = useParams();\n  const [personalDetails, setPersonalDetails] = useState({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n  const [languageSkills, setLanguageSkills] = useState([{\n    language: '',\n    speak: false,\n    read: false,\n    write: false\n  }]);\n  const [documentDetails, setDocumentDetails] = useState({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n  const [familyMembers, setFamilyMembers] = useState([{\n    name: '',\n    relationship: '',\n    dateOfBirth: '',\n    qualification: '',\n    occupation: '',\n    organisationAndPosition: ''\n  }]);\n  const [physicalDetails, setPhysicalDetails] = useState({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n  const [previousEmployment, setPreviousEmployment] = useState({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n  const [references, setReferences] = useState({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n  const [workExperience, setWorkExperience] = useState([{\n    employerName: '',\n    address: '',\n    duration: '',\n    fromDate: '',\n    toDate: '',\n    lastPosition: '',\n    designation: '',\n    natureOfDuties: '',\n    immediateSuperior: '',\n    grossEmoluments: '',\n    lastDrawn: '',\n    basicSalary: '',\n    fixedSalary: '',\n    variableSalary: '',\n    grossSalary: ''\n  }]);\n  const [education, setEducation] = useState([{\n    examination: '',\n    specialisation: '',\n    schoolCollege: '',\n    university: '',\n    fullTimePartTime: '',\n    duration: '',\n    monthYearPassing: '',\n    gradeMarks: '',\n    distinctions: '',\n    certificateFile: null\n  }]);\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n  const handlePersonalDetailsChange = (field, value) => {\n    setPersonalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleDocumentDetailsChange = (field, value) => {\n    setDocumentDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handlePhysicalDetailsChange = (field, value) => {\n    setPhysicalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Helper function to convert file to base64\n  const fileToBase64 = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  };\n  const handleFileUpload = (file, section, field, index) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = {\n          ...newEducation[index],\n          [field]: file\n        };\n        return newEducation;\n      });\n    }\n  };\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, {\n      language: '',\n      speak: false,\n      read: false,\n      write: false\n    }]);\n  };\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, {\n      name: '',\n      relationship: '',\n      dateOfBirth: '',\n      qualification: '',\n      occupation: '',\n      organisationAndPosition: ''\n    }]);\n  };\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n  const getStepTitle = step => {\n    const titles = ['', 'PERSONAL DETAILS', 'FAMILY DATA', 'HEALTH DATA', 'EDUCATION DETAILS', 'WORK HISTORY DATA', 'GENERAL DATA', 'CRIMINAL RECORDS', 'DECLARATION FORM'];\n    return titles[step] || '';\n  };\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    var _personalDetails$firs, _personalDetails$last, _personalDetails$date, _personalDetails$gend, _personalDetails$nati, _personalDetails$cont, _personalDetails$emer;\n    const requiredFields = [(_personalDetails$firs = personalDetails.firstName) === null || _personalDetails$firs === void 0 ? void 0 : _personalDetails$firs.trim(), (_personalDetails$last = personalDetails.lastName) === null || _personalDetails$last === void 0 ? void 0 : _personalDetails$last.trim(), (_personalDetails$date = personalDetails.dateOfBirth) === null || _personalDetails$date === void 0 ? void 0 : _personalDetails$date.trim(), (_personalDetails$gend = personalDetails.gender) === null || _personalDetails$gend === void 0 ? void 0 : _personalDetails$gend.trim(), (_personalDetails$nati = personalDetails.nationality) === null || _personalDetails$nati === void 0 ? void 0 : _personalDetails$nati.trim(), (_personalDetails$cont = personalDetails.contactNumber) === null || _personalDetails$cont === void 0 ? void 0 : _personalDetails$cont.trim(), (_personalDetails$emer = personalDetails.emergencyContactNo) === null || _personalDetails$emer === void 0 ? void 0 : _personalDetails$emer.trim()];\n    return requiredFields.every(Boolean);\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n    try {\n      // Convert all files to base64\n      const processedPersonalDetails = {\n        ...personalDetails\n      };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = await fileToBase64(personalDetails.passportPhoto);\n      }\n      const processedDocumentDetails = {\n        ...documentDetails\n      };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = await fileToBase64(documentDetails.passportFile);\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = await fileToBase64(documentDetails.panFile);\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = await fileToBase64(documentDetails.aadharFile);\n      }\n      const processedEducation = await Promise.all(education.map(async edu => {\n        const processedEdu = {\n          ...edu\n        };\n        if (edu.certificateFile) {\n          processedEdu.certificateFile = await fileToBase64(edu.certificateFile);\n        }\n        return processedEdu;\n      }));\n\n      // Create the complete JSON object\n      const completeFormData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Log the complete JSON for debugging\n      console.log(\"Complete Form Data JSON:\", JSON.stringify(completeFormData, null, 2));\n\n      // Send to API\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      await axios.post(url, completeFormData, {\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      setSuccessMessage(\"Form submitted successfully!\");\n      setErrorMessage(null);\n\n      // Optional: Download JSON file locally\n      downloadJsonFile(completeFormData);\n    } catch (error) {\n      console.error(\"Form submission error:\", error);\n      setErrorMessage(\"Failed to submit the form. Please try again later.\");\n      setSuccessMessage(null);\n    }\n  };\n\n  // Function to download JSON file locally\n  const downloadJsonFile = data => {\n    const jsonString = JSON.stringify(data, null, 2);\n    const blob = new Blob([jsonString], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `onboarding-form-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  // Function to preview current form data as JSON\n  const previewFormData = async () => {\n    try {\n      // Convert files to base64 for preview\n      const processedPersonalDetails = {\n        ...personalDetails\n      };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = `[FILE: ${personalDetails.passportPhoto.name}]`;\n      }\n      const processedDocumentDetails = {\n        ...documentDetails\n      };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = `[FILE: ${documentDetails.passportFile.name}]`;\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = `[FILE: ${documentDetails.panFile.name}]`;\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = `[FILE: ${documentDetails.aadharFile.name}]`;\n      }\n      const processedEducation = education.map(edu => {\n        const processedEdu = {\n          ...edu\n        };\n        if (edu.certificateFile) {\n          processedEdu.certificateFile = `[FILE: ${edu.certificateFile.name}]`;\n        }\n        return processedEdu;\n      });\n      const previewData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Show JSON in console and modal\n      const jsonString = JSON.stringify(previewData, null, 2);\n      console.log(\"Form Data Preview:\", jsonString);\n      setJsonPreviewData(jsonString);\n      setShowJsonPreview(true);\n\n      // Optional: Download preview JSON\n      downloadJsonFile(previewData);\n    } catch (error) {\n      console.error(\"Preview error:\", error);\n      alert(\"Error generating preview\");\n    }\n  };\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"PERSONAL DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"photo-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"photo-placeholder\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"photo-frame\",\n                children: personalDetails.passportPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(personalDetails.passportPhoto),\n                  alt: \"Passport Photo\",\n                  className: \"uploaded-photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"placeholder-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"40\",\n                    height: \"40\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"7\",\n                      r: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"photo-label\",\n                children: \"Uploaded Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"upload-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"upload-text\",\n                  children: \"Please upload a image less than 100kb\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"upload-button\",\n                  children: [\"Choose a file\", /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"image/*\",\n                    required: true,\n                    onChange: e => {\n                      var _e$target$files;\n                      return handleFileUpload(((_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0]) || null, 'personal', 'passportPhoto');\n                    },\n                    className: \"hidden-file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 19\n                }, this), personalDetails.passportPhoto && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"file-info\",\n                  children: personalDetails.passportPhoto.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"First Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.firstName,\n                onChange: e => handlePersonalDetailsChange('firstName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Middle Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.middleName,\n                onChange: e => handlePersonalDetailsChange('middleName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Last Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.lastName,\n                onChange: e => handlePersonalDetailsChange('lastName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date of Birth *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                required: true,\n                value: personalDetails.dateOfBirth,\n                onChange: e => handlePersonalDetailsChange('dateOfBirth', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Gender *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                required: true,\n                value: personalDetails.gender,\n                onChange: e => handlePersonalDetailsChange('gender', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Birth Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.birthPlace,\n                onChange: e => handlePersonalDetailsChange('birthPlace', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Age\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: personalDetails.age,\n                onChange: e => handlePersonalDetailsChange('age', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marriage Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: personalDetails.marriageDate,\n                onChange: e => handlePersonalDetailsChange('marriageDate', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marital Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: personalDetails.maritalStatus,\n                onChange: e => handlePersonalDetailsChange('maritalStatus', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Single\",\n                  children: \"Single\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Married\",\n                  children: \"Married\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Divorced\",\n                  children: \"Divorced\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Widowed\",\n                  children: \"Widowed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Nationality *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.nationality,\n                onChange: e => handlePersonalDetailsChange('nationality', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Religion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.religion,\n                onChange: e => handlePersonalDetailsChange('religion', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Native State\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.nativeState,\n                onChange: e => handlePersonalDetailsChange('nativeState', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Contact Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.contactNumber,\n                onChange: e => handlePersonalDetailsChange('contactNumber', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Emergency Contact No. *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.emergencyContactNo,\n                onChange: e => handlePersonalDetailsChange('emergencyContactNo', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"State of Domicile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.stateOfDomicile,\n                onChange: e => handlePersonalDetailsChange('stateOfDomicile', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                required: true,\n                value: personalDetails.email,\n                onChange: e => handlePersonalDetailsChange('email', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.presentAddress,\n                onChange: e => handlePersonalDetailsChange('presentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.permanentAddress,\n                onChange: e => handlePersonalDetailsChange('permanentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.presentPin,\n                onChange: e => handlePersonalDetailsChange('presentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.permanentPin,\n                onChange: e => handlePersonalDetailsChange('permanentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Document Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.passportNo,\n                  onChange: e => handleDocumentDetailsChange('passportNo', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Issue Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.passportIssueDate,\n                  onChange: e => handleDocumentDetailsChange('passportIssueDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Upto Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.validUptoDate,\n                  onChange: e => handleDocumentDetailsChange('validUptoDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Country of Issue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.countryOfIssue,\n                  onChange: e => handleDocumentDetailsChange('countryOfIssue', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Visa Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.validVisaDetails,\n                  onChange: e => handleDocumentDetailsChange('validVisaDetails', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.panNumber,\n                  onChange: e => handleDocumentDetailsChange('panNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files2;\n                    return handleFileUpload(((_e$target$files2 = e.target.files) === null || _e$target$files2 === void 0 ? void 0 : _e$target$files2[0]) || null, 'document', 'panFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 19\n                }, this), documentDetails.panFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.panFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 839,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.aadharNumber,\n                  onChange: e => handleDocumentDetailsChange('aadharNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files3;\n                    return handleFileUpload(((_e$target$files3 = e.target.files) === null || _e$target$files3 === void 0 ? void 0 : _e$target$files3[0]) || null, 'document', 'aadharFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 19\n                }, this), documentDetails.aadharFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.aadharFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files4;\n                    return handleFileUpload(((_e$target$files4 = e.target.files) === null || _e$target$files4 === void 0 ? void 0 : _e$target$files4[0]) || null, 'document', 'passportFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 19\n                }, this), documentDetails.passportFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.passportFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Language Known\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 15\n            }, this), languageSkills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"language-skill-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: skill.language,\n                  onChange: e => {\n                    const newSkills = [...languageSkills];\n                    newSkills[index].language = e.target.value;\n                    setLanguageSkills(newSkills);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 887,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.speak,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].speak = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 23\n                  }, this), \"Speak\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.read,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].read = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 23\n                  }, this), \"Read\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.write,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].write = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 23\n                  }, this), \"Write\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addLanguageSkill,\n              className: \"btn-add\",\n              children: \"Add Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"FAMILY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Family Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 15\n            }, this), familyMembers.map((member, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"family-member-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 954,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.name,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].name = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Relationship\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.relationship,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].relationship = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 967,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date of Birth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 978,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: member.dateOfBirth,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].dateOfBirth = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 979,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 977,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Qualification\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 990,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.qualification,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].qualification = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Occupation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.occupation,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].occupation = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Organisation and Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1014,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.organisationAndPosition,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].organisationAndPosition = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addFamilyMember,\n              className: \"btn-add\",\n              children: \"Add Family Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 948,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"HEALTH DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Physical Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Height\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.height,\n                  onChange: e => handlePhysicalDetailsChange('height', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1043,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Weight\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.weight,\n                  onChange: e => handlePhysicalDetailsChange('weight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1053,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Blood Group\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: physicalDetails.bloodGroup,\n                  onChange: e => handlePhysicalDetailsChange('bloodGroup', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Blood Group\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1065,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A+\",\n                    children: \"A+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1066,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A-\",\n                    children: \"A-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1067,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B+\",\n                    children: \"B+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1068,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B-\",\n                    children: \"B-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1069,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB+\",\n                    children: \"AB+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB-\",\n                    children: \"AB-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O+\",\n                    children: \"O+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O-\",\n                    children: \"O-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightRight,\n                  onChange: e => handlePhysicalDetailsChange('eyesightRight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1078,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightLeft,\n                  onChange: e => handlePhysicalDetailsChange('eyesightLeft', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Physical Disability\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1093,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.physicalDisability,\n                  onChange: e => handlePhysicalDetailsChange('physicalDisability', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Identification Mark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1101,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.identificationMark,\n                  onChange: e => handlePhysicalDetailsChange('identificationMark', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1102,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1037,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"EDUCATION DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1116,\n            columnNumber: 13\n          }, this), education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"education-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Education \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Examination Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1123,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.examination,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].examination = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1124,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Specialisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.specialisation,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].specialisation = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1136,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"School/College/Institution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.schoolCollege,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].schoolCollege = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"University/Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.university,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].university = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1160,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Full Time/Part Time/Correspondence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: edu.fullTimePartTime,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].fullTimePartTime = e.target.value;\n                    setEducation(newEducation);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1180,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time\",\n                    children: \"Full Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time\",\n                    children: \"Part Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1182,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Correspondence\",\n                    children: \"Correspondence\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1183,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1172,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Duration of Course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1187,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.duration,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].duration = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Month & Year of Passing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.monthYearPassing,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].monthYearPassing = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Grade/% Marks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.gradeMarks,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].gradeMarks = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1212,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Distinctions/Scholarships/Prizes Won\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1223,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: edu.distinctions,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].distinctions = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1224,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1222,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Certificate/Document Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files5;\n                    return handleFileUpload(((_e$target$files5 = e.target.files) === null || _e$target$files5 === void 0 ? void 0 : _e$target$files5[0]) || null, 'education', 'certificateFile', index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1235,\n                  columnNumber: 21\n                }, this), edu.certificateFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: edu.certificateFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1242,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1241,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1121,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1119,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addEducation,\n            className: \"btn-add\",\n            children: \"Add Education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 11\n        }, this);\n      case 5:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"WORK HISTORY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1258,\n            columnNumber: 13\n          }, this), workExperience.map((experience, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"work-experience-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Work Experience \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1262,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Employer's Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1265,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.employerName,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].employerName = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.address,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].address = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1278,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"From Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1288,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.fromDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fromDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1289,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"To Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1300,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.toDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].toDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Position Held\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.lastPosition,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastPosition = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Designation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.designation,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].designation = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nature of Duties\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.natureOfDuties,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].natureOfDuties = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1337,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Name & Designation of Immediate Superior\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.immediateSuperior,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].immediateSuperior = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Emoluments (Rs. Per month at joining)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1359,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossEmoluments,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossEmoluments = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Drawn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.lastDrawn,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastDrawn = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1372,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Basic Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.basicSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].basicSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1384,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fixed Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1395,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.fixedSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fixedSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1396,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Variable Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1407,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.variableSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].variableSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1408,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1420,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1418,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1263,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1261,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addWorkExperience,\n            className: \"btn-add\",\n            children: \"Add Work Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1257,\n          columnNumber: 11\n        }, this);\n      case 6:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"GENERAL DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1442,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Previous Interview with MH Group\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Have you ever been interviewed by any of the MH Group of Companies?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"true\",\n                      checked: previousEmployment.hasBeenInterviewed === true,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1451,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"false\",\n                      checked: previousEmployment.hasBeenInterviewed === false,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1461,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1460,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1449,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1447,\n                columnNumber: 17\n              }, this), previousEmployment.hasBeenInterviewed && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date/Year\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1475,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewDate,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewDate: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1476,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1474,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1483,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewPosition,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewPosition: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1484,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Company\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1491,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewCompany,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewCompany: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1492,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1490,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1446,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"References & Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1504,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relatives/Acquaintance in MH Group - Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1507,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.name,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      name: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1508,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relationship\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1518,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.relationship,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      relationship: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1519,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1517,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1529,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.position,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      position: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1530,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1528,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Company & Phone No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1540,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.companyAndPhone,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      companyAndPhone: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1541,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1539,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"How did you come to know of this position?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1551,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.howDidYouKnow,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    howDidYouKnow: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1552,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1550,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Are you engaged in any personal business?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1558,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"true\",\n                      checked: references.personalBusiness === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1561,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1560,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"false\",\n                      checked: references.personalBusiness === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1571,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1570,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1559,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1557,\n                columnNumber: 17\n              }, this), references.personalBusiness && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, indicate nature of business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.businessNature,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    businessNature: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1585,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Do you have any contract/bond with your previous employer?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1593,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"true\",\n                      checked: references.contractWithPreviousEmployer === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1596,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1595,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"false\",\n                      checked: references.contractWithPreviousEmployer === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1606,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1605,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1594,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1592,\n                columnNumber: 17\n              }, this), references.contractWithPreviousEmployer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, Give Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1619,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.contractDetails,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    contractDetails: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1620,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1618,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If selected, when can you join?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1628,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: references.whenCanJoin,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    whenCanJoin: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1629,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1627,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1505,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1441,\n          columnNumber: 11\n        }, this);\n      case 7:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"CRIMINAL RECORDS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been involved in any criminal proceedings?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1647,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenInvolved === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1650,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1649,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenInvolved === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1660,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1659,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1648,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1646,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenInvolved && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1673,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.details,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  details: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1674,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1672,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been convicted of any offence?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1682,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenConvicted === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1685,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenConvicted === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1695,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1694,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1683,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1681,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenConvicted && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1708,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.convictionDetails,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  convictionDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1709,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1707,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1645,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1642,\n          columnNumber: 11\n        }, this);\n      case 8:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"DECLARATION FORM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1722,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-text\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"I hereby declare that:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1727,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1727,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1726,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1725,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: declaration.notConnectedToDirectors,\n                  onChange: e => setDeclaration(prev => ({\n                    ...prev,\n                    notConnectedToDirectors: e.target.checked\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1733,\n                  columnNumber: 19\n                }, this), \"I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1732,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1731,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"OR I am a partner or relative of a Director of the Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"true\",\n                    checked: declaration.isPartnerOrRelative === true,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1746,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1745,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"false\",\n                    checked: declaration.isPartnerOrRelative === false,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1756,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1755,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1744,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1742,\n              columnNumber: 15\n            }, this), declaration.isPartnerOrRelative && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, provide details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1770,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: declaration.partnerRelativeDetails,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  partnerRelativeDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1771,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1769,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1779,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: declaration.place,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  place: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1780,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1778,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1788,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: declaration.date,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  date: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1789,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1787,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Applicant's Signature\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1797,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Type your full name as signature\",\n                value: declaration.applicantSignature,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  applicantSignature: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1798,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-note\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"em\", {\n                    children: \"Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1808,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1808,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1807,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1806,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1724,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1721,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Step \", currentStep]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1816,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"onboarding-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Employee Onboarding Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1829,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-fill\",\n          style: {\n            width: `${currentStep / totalSteps * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1831,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1830,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Step \", currentStep, \" of \", totalSteps, \": \", getStepTitle(currentStep)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1836,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1828,\n      columnNumber: 7\n    }, this), (successMessage || errorMessage) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: successMessage ? \"success-message\" : \"error-message\",\n          children: successMessage || errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1844,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1847,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1843,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1842,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: renderStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1852,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-left\",\n        children: currentStep > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: prevStep,\n          className: \"btn-secondary\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1859,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1857,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: previewFormData,\n          className: \"btn-preview\",\n          children: \"Preview JSON\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1866,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1865,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-right\",\n        children: currentStep < totalSteps ? /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: nextStep,\n          className: \"btn-primary\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1873,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => {\n            const form = document.querySelector('form');\n            if (form) {\n              form.requestSubmit();\n            }\n          },\n          className: \"btn-primary\",\n          children: \"Submit Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1877,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1871,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1856,\n      columnNumber: 7\n    }, this), showJsonPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content json-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Form Data JSON Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1893,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"json-preview\",\n          children: jsonPreviewData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1894,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowJsonPreview(false),\n            className: \"modal-close\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1898,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              navigator.clipboard.writeText(jsonPreviewData);\n              alert('JSON copied to clipboard!');\n            },\n            className: \"btn-copy\",\n            children: \"Copy JSON\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1904,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1897,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1892,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1891,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1827,\n    columnNumber: 5\n  }, this);\n};\n_s(OnboardingForm, \"QR9AKn+UqHk31SUQxGwmtCYLZEg=\", false, function () {\n  return [useParams];\n});\n_c = OnboardingForm;\nexport default OnboardingForm;\nvar _c;\n$RefreshReg$(_c, \"OnboardingForm\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OnboardingForm", "_s", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "showJsonPreview", "setShowJsonPreview", "jsonPreviewData", "setJsonPreviewData", "token", "personalDetails", "setPersonalDetails", "firstName", "middleName", "lastName", "dateOfBirth", "gender", "birthPlace", "age", "marriageDate", "maritalStatus", "nationality", "religion", "nativeState", "contactNumber", "emergencyContactNo", "stateOfDomicile", "email", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "present<PERSON>in", "permanent<PERSON>in", "passportPhoto", "languageSkills", "setLanguageSkills", "language", "speak", "read", "write", "documentDetails", "setDocumentDetails", "passportNo", "passportIssueDate", "validUptoDate", "countryOfIssue", "validVisaDetails", "panNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passportFile", "panFile", "a<PERSON>har<PERSON><PERSON>", "family<PERSON><PERSON>bers", "setFamilyMembers", "name", "relationship", "qualification", "occupation", "organisationAndPosition", "physicalDetails", "setPhysicalDetails", "height", "weight", "bloodGroup", "eyesightRight", "eyesightLeft", "physicalDisability", "identificationMark", "previousEmployment", "setPreviousEmployment", "hasBeenInterviewed", "interviewDate", "interviewPosition", "interviewCompany", "references", "setReferences", "relativesInCompany", "position", "companyAndPhone", "howDidYouKnow", "personalBusiness", "businessNature", "contractWithPreviousEmployer", "contractDetails", "whenCanJoin", "workExperience", "setWorkExperience", "employerName", "address", "duration", "fromDate", "toDate", "lastPosition", "designation", "natureOfDuties", "immediateSuperior", "grossEmoluments", "lastDrawn", "basicSalary", "fixedSalary", "variableSalary", "grossSalary", "education", "setEducation", "examination", "specialisation", "schoolCollege", "university", "fullTimePartTime", "monthYearPassing", "gradeMarks", "distinctions", "certificateFile", "currentStep", "setCurrentStep", "totalSteps", "criminalRecords", "setCriminalRecords", "hasBeenInvolved", "details", "hasBeenConvicted", "convictionDetails", "declaration", "setDeclaration", "notConnectedToDirectors", "isPartnerOrRelative", "partnerRelativeDetails", "place", "date", "applicantSignature", "handlePersonalDetailsChange", "field", "value", "prev", "handleDocumentDetailsChange", "handlePhysicalDetailsChange", "fileToBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "error", "handleFileUpload", "section", "index", "maxSize", "size", "alert", "undefined", "newEducation", "addLanguageSkill", "addFamilyMember", "addWorkExperience", "addEducation", "getStepTitle", "step", "titles", "nextStep", "console", "log", "prevStep", "validateRequired<PERSON><PERSON>s", "_personalDetails$firs", "_personalDetails$last", "_personalDetails$date", "_personalDetails$gend", "_personalDetails$nati", "_personalDetails$cont", "_personalDetails$emer", "requiredFields", "trim", "every", "Boolean", "handleSubmit", "event", "preventDefault", "processedPersonalDetails", "processedDocumentDetails", "processedEducation", "all", "map", "edu", "processedEdu", "completeFormData", "submissionTimestamp", "Date", "toISOString", "formVersion", "JSON", "stringify", "url", "post", "headers", "downloadJsonFile", "data", "jsonString", "blob", "Blob", "type", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "previewFormData", "previewData", "renderStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "width", "viewBox", "fill", "stroke", "strokeWidth", "d", "cx", "cy", "r", "accept", "required", "onChange", "e", "_e$target$files", "target", "files", "_e$target$files2", "_e$target$files3", "_e$target$files4", "skill", "newSkills", "checked", "onClick", "member", "newMembers", "_e$target$files5", "experience", "newExperience", "placeholder", "closeModal", "style", "onSubmit", "form", "querySelector", "requestSubmit", "navigator", "clipboard", "writeText", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\n\ninterface PersonalDetails {\n  firstName: string;\n  middleName: string;\n  lastName: string;\n  dateOfBirth: string;\n  gender: string;\n  birthPlace: string;\n  age: string;\n  marriageDate: string;\n  maritalStatus: string;\n  nationality: string;\n  religion: string;\n  nativeState: string;\n  contactNumber: string;\n  emergencyContactNo: string;\n  stateOfDomicile: string;\n  email: string;\n  presentAddress: string;\n  permanentAddress: string;\n  presentPin: string;\n  permanentPin: string;\n  passportPhoto: File | null;\n}\n\ninterface LanguageSkills {\n  language: string;\n  speak: boolean;\n  read: boolean;\n  write: boolean;\n}\n\ninterface DocumentDetails {\n  passportNo: string;\n  passportIssueDate: string;\n  validUptoDate: string;\n  countryOfIssue: string;\n  validVisaDetails: string;\n  panNumber: string;\n  aadharNumber: string;\n  passportFile: File | null;\n  panFile: File | null;\n  aadharFile: File | null;\n}\n\ninterface FamilyMember {\n  name: string;\n  relationship: string;\n  dateOfBirth: string;\n  qualification: string;\n  occupation: string;\n  organisationAndPosition: string;\n}\n\ninterface PhysicalDetails {\n  height: string;\n  weight: string;\n  bloodGroup: string;\n  eyesightRight: string;\n  eyesightLeft: string;\n  physicalDisability: string;\n  identificationMark: string;\n}\n\ninterface PreviousEmployment {\n  hasBeenInterviewed: boolean;\n  interviewDate: string;\n  interviewPosition: string;\n  interviewCompany: string;\n}\n\ninterface References {\n  relativesInCompany: {\n    name: string;\n    relationship: string;\n    position: string;\n    companyAndPhone: string;\n  };\n  howDidYouKnow: string;\n  personalBusiness: boolean;\n  businessNature: string;\n  contractWithPreviousEmployer: boolean;\n  contractDetails: string;\n  whenCanJoin: string;\n}\n\ninterface WorkExperience {\n  employerName: string;\n  address: string;\n  duration: string;\n  fromDate: string;\n  toDate: string;\n  lastPosition: string;\n  designation: string;\n  natureOfDuties: string;\n  immediateSuperior: string;\n  grossEmoluments: string;\n  lastDrawn: string;\n  basicSalary: string;\n  fixedSalary: string;\n  variableSalary: string;\n  grossSalary: string;\n}\n\ninterface Education {\n  examination: string;\n  specialisation: string;\n  schoolCollege: string;\n  university: string;\n  fullTimePartTime: string;\n  duration: string;\n  monthYearPassing: string;\n  gradeMarks: string;\n  distinctions: string;\n  certificateFile: File | null;\n}\n\nconst OnboardingForm: React.FC = () => {\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [errorMessage, setErrorMessage] = useState<string | null>(null);\n  const [showJsonPreview, setShowJsonPreview] = useState<boolean>(false);\n  const [jsonPreviewData, setJsonPreviewData] = useState<string>('');\n  const { token } = useParams<{ token: string }>();\n  const [personalDetails, setPersonalDetails] = useState<PersonalDetails>({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n\n  const [languageSkills, setLanguageSkills] = useState<LanguageSkills[]>([\n    { language: '', speak: false, read: false, write: false }\n  ]);\n\n  const [documentDetails, setDocumentDetails] = useState<DocumentDetails>({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n\n  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([\n    { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }\n  ]);\n\n  const [physicalDetails, setPhysicalDetails] = useState<PhysicalDetails>({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n\n  const [previousEmployment, setPreviousEmployment] = useState<PreviousEmployment>({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n\n  const [references, setReferences] = useState<References>({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n\n  const [workExperience, setWorkExperience] = useState<WorkExperience[]>([\n    {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }\n  ]);\n\n  const [education, setEducation] = useState<Education[]>([\n    {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }\n  ]);\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n\n  const handlePersonalDetailsChange = (field: keyof PersonalDetails, value: string) => {\n    setPersonalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleDocumentDetailsChange = (field: keyof DocumentDetails, value: string) => {\n    setDocumentDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handlePhysicalDetailsChange = (field: keyof PhysicalDetails, value: string) => {\n    setPhysicalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  // Helper function to convert file to base64\n  const fileToBase64 = (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result as string);\n      reader.onerror = error => reject(error);\n    });\n  };\n\n  const handleFileUpload = (file: File | null, section: 'personal' | 'document' | 'education', field: string, index?: number) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = { ...newEducation[index], [field]: file };\n        return newEducation;\n      });\n    }\n  };\n\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, { language: '', speak: false, read: false, write: false }]);\n  };\n\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }]);\n  };\n\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n\n  const getStepTitle = (step: number) => {\n    const titles = [\n      '',\n      'PERSONAL DETAILS',\n      'FAMILY DATA',\n      'HEALTH DATA',\n      'EDUCATION DETAILS',\n      'WORK HISTORY DATA',\n      'GENERAL DATA',\n      'CRIMINAL RECORDS',\n      'DECLARATION FORM'\n    ];\n    return titles[step] || '';\n  };\n\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    const requiredFields = [\n      personalDetails.firstName?.trim(),\n      personalDetails.lastName?.trim(),\n      personalDetails.dateOfBirth?.trim(),\n      personalDetails.gender?.trim(),\n      personalDetails.nationality?.trim(),\n      personalDetails.contactNumber?.trim(),\n      personalDetails.emergencyContactNo?.trim(),\n    ];\n    return requiredFields.every(Boolean);\n  };\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n\n    try {\n      // Convert all files to base64\n      const processedPersonalDetails = { ...personalDetails };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = await fileToBase64(personalDetails.passportPhoto) as any;\n      }\n\n      const processedDocumentDetails = { ...documentDetails };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = await fileToBase64(documentDetails.passportFile) as any;\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = await fileToBase64(documentDetails.panFile) as any;\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = await fileToBase64(documentDetails.aadharFile) as any;\n      }\n\n      const processedEducation = await Promise.all(\n        education.map(async (edu) => {\n          const processedEdu = { ...edu };\n          if (edu.certificateFile) {\n            processedEdu.certificateFile = await fileToBase64(edu.certificateFile) as any;\n          }\n          return processedEdu;\n        })\n      );\n\n      // Create the complete JSON object\n      const completeFormData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Log the complete JSON for debugging\n      console.log(\"Complete Form Data JSON:\", JSON.stringify(completeFormData, null, 2));\n\n      // Send to API\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      await axios.post(url, completeFormData, {\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n\n      setSuccessMessage(\"Form submitted successfully!\");\n      setErrorMessage(null);\n\n      // Optional: Download JSON file locally\n      downloadJsonFile(completeFormData);\n\n    } catch (error) {\n      console.error(\"Form submission error:\", error);\n      setErrorMessage(\"Failed to submit the form. Please try again later.\");\n      setSuccessMessage(null);\n    }\n  };\n\n  // Function to download JSON file locally\n  const downloadJsonFile = (data: any) => {\n    const jsonString = JSON.stringify(data, null, 2);\n    const blob = new Blob([jsonString], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `onboarding-form-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  // Function to preview current form data as JSON\n  const previewFormData = async () => {\n    try {\n      // Convert files to base64 for preview\n      const processedPersonalDetails = { ...personalDetails };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = `[FILE: ${personalDetails.passportPhoto.name}]` as any;\n      }\n\n      const processedDocumentDetails = { ...documentDetails };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = `[FILE: ${documentDetails.passportFile.name}]` as any;\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = `[FILE: ${documentDetails.panFile.name}]` as any;\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = `[FILE: ${documentDetails.aadharFile.name}]` as any;\n      }\n\n      const processedEducation = education.map((edu) => {\n        const processedEdu = { ...edu };\n        if (edu.certificateFile) {\n          processedEdu.certificateFile = `[FILE: ${edu.certificateFile.name}]` as any;\n        }\n        return processedEdu;\n      });\n\n      const previewData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Show JSON in console and modal\n      const jsonString = JSON.stringify(previewData, null, 2);\n      console.log(\"Form Data Preview:\", jsonString);\n      setJsonPreviewData(jsonString);\n      setShowJsonPreview(true);\n\n      // Optional: Download preview JSON\n      downloadJsonFile(previewData);\n    } catch (error) {\n      console.error(\"Preview error:\", error);\n      alert(\"Error generating preview\");\n    }\n  };\n\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"form-section\">\n            <h3>PERSONAL DETAILS</h3>\n\n            {/* Passport Photo Upload Section */}\n            <div className=\"photo-upload-section\">\n              <div className=\"photo-placeholder\">\n                <div className=\"photo-frame\">\n                  {personalDetails.passportPhoto ? (\n                    <img\n                      src={URL.createObjectURL(personalDetails.passportPhoto)}\n                      alt=\"Passport Photo\"\n                      className=\"uploaded-photo\"\n                    />\n                  ) : (\n                    <div className=\"placeholder-icon\">\n                      <svg width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n                      </svg>\n                    </div>\n                  )}\n                </div>\n                <span className=\"photo-label\">Uploaded Photo</span>\n              </div>\n              <div className=\"upload-area\">\n                <div className=\"upload-content\">\n                  <p className=\"upload-text\">Please upload a image less than 100kb</p>\n                  <label className=\"upload-button\">\n                    Choose a file\n                    <input\n                      type=\"file\"\n                      accept=\"image/*\"\n                      required\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'personal', 'passportPhoto')}\n                      className=\"hidden-file-input\"\n                    />\n                  </label>\n                  {personalDetails.passportPhoto && (\n                    <p className=\"file-info\">{personalDetails.passportPhoto.name}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>First Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.firstName}\n                  onChange={(e) => handlePersonalDetailsChange('firstName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Middle Name</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.middleName}\n                  onChange={(e) => handlePersonalDetailsChange('middleName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Last Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.lastName}\n                  onChange={(e) => handlePersonalDetailsChange('lastName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Date of Birth *</label>\n                <input\n                  type=\"date\"\n                  required\n                  value={personalDetails.dateOfBirth}\n                  onChange={(e) => handlePersonalDetailsChange('dateOfBirth', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Gender *</label>\n                <select\n                  required\n                  value={personalDetails.gender}\n                  onChange={(e) => handlePersonalDetailsChange('gender', e.target.value)}\n                >\n                  <option value=\"\">Select Gender</option>\n                  <option value=\"Male\">Male</option>\n                  <option value=\"Female\">Female</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Birth Place</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.birthPlace}\n                  onChange={(e) => handlePersonalDetailsChange('birthPlace', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Age</label>\n                <input\n                  type=\"number\"\n                  value={personalDetails.age}\n                  onChange={(e) => handlePersonalDetailsChange('age', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marriage Date</label>\n                <input\n                  type=\"date\"\n                  value={personalDetails.marriageDate}\n                  onChange={(e) => handlePersonalDetailsChange('marriageDate', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marital Status</label>\n                <select\n                  value={personalDetails.maritalStatus}\n                  onChange={(e) => handlePersonalDetailsChange('maritalStatus', e.target.value)}\n                >\n                  <option value=\"\">Select Status</option>\n                  <option value=\"Single\">Single</option>\n                  <option value=\"Married\">Married</option>\n                  <option value=\"Divorced\">Divorced</option>\n                  <option value=\"Widowed\">Widowed</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Nationality *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.nationality}\n                  onChange={(e) => handlePersonalDetailsChange('nationality', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Religion</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.religion}\n                  onChange={(e) => handlePersonalDetailsChange('religion', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Native State</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.nativeState}\n                  onChange={(e) => handlePersonalDetailsChange('nativeState', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Contact Number *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.contactNumber}\n                  onChange={(e) => handlePersonalDetailsChange('contactNumber', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Emergency Contact No. *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.emergencyContactNo}\n                  onChange={(e) => handlePersonalDetailsChange('emergencyContactNo', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>State of Domicile</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.stateOfDomicile}\n                  onChange={(e) => handlePersonalDetailsChange('stateOfDomicile', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Email *</label>\n                <input\n                  type=\"email\"\n                  required\n                  value={personalDetails.email}\n                  onChange={(e) => handlePersonalDetailsChange('email', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Present Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.presentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('presentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Permanent Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.permanentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('permanentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Present PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.presentPin}\n                  onChange={(e) => handlePersonalDetailsChange('presentPin', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Permanent PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.permanentPin}\n                  onChange={(e) => handlePersonalDetailsChange('permanentPin', e.target.value)}\n                />\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Document Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Passport No.</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.passportNo}\n                    onChange={(e) => handleDocumentDetailsChange('passportNo', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Issue Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.passportIssueDate}\n                    onChange={(e) => handleDocumentDetailsChange('passportIssueDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Upto Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.validUptoDate}\n                    onChange={(e) => handleDocumentDetailsChange('validUptoDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Country of Issue</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.countryOfIssue}\n                    onChange={(e) => handleDocumentDetailsChange('countryOfIssue', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Visa Details</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.validVisaDetails}\n                    onChange={(e) => handleDocumentDetailsChange('validVisaDetails', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.panNumber}\n                    onChange={(e) => handleDocumentDetailsChange('panNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'panFile')}\n                  />\n                  {documentDetails.panFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.panFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.aadharNumber}\n                    onChange={(e) => handleDocumentDetailsChange('aadharNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'aadharFile')}\n                  />\n                  {documentDetails.aadharFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.aadharFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'passportFile')}\n                  />\n                  {documentDetails.passportFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.passportFile.name}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Language Known</h4>\n              {languageSkills.map((skill, index) => (\n                <div key={index} className=\"language-skill-row\">\n                  <div className=\"form-group\">\n                    <label>Language</label>\n                    <input\n                      type=\"text\"\n                      value={skill.language}\n                      onChange={(e) => {\n                        const newSkills = [...languageSkills];\n                        newSkills[index].language = e.target.value;\n                        setLanguageSkills(newSkills);\n                      }}\n                    />\n                  </div>\n                  <div className=\"checkbox-group\">\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.speak}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].speak = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Speak\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.read}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].read = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Read\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.write}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].write = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Write\n                    </label>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addLanguageSkill} className=\"btn-add\">\n                Add Language\n              </button>\n            </div>\n          </div>\n        );\n      case 2:\n        return (\n          <div className=\"form-section\">\n            <h3>FAMILY DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Family Members</h4>\n              {familyMembers.map((member, index) => (\n                <div key={index} className=\"family-member-section\">\n                  <div className=\"form-grid\">\n                    <div className=\"form-group\">\n                      <label>Name</label>\n                      <input\n                        type=\"text\"\n                        value={member.name}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].name = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Relationship</label>\n                      <input\n                        type=\"text\"\n                        value={member.relationship}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].relationship = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Date of Birth</label>\n                      <input\n                        type=\"date\"\n                        value={member.dateOfBirth}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].dateOfBirth = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Qualification</label>\n                      <input\n                        type=\"text\"\n                        value={member.qualification}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].qualification = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Occupation</label>\n                      <input\n                        type=\"text\"\n                        value={member.occupation}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].occupation = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Organisation and Position</label>\n                      <input\n                        type=\"text\"\n                        value={member.organisationAndPosition}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].organisationAndPosition = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addFamilyMember} className=\"btn-add\">\n                Add Family Member\n              </button>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"form-section\">\n            <h3>HEALTH DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Physical Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Height</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.height}\n                    onChange={(e) => handlePhysicalDetailsChange('height', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Weight</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.weight}\n                    onChange={(e) => handlePhysicalDetailsChange('weight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Blood Group</label>\n                  <select\n                    value={physicalDetails.bloodGroup}\n                    onChange={(e) => handlePhysicalDetailsChange('bloodGroup', e.target.value)}\n                  >\n                    <option value=\"\">Select Blood Group</option>\n                    <option value=\"A+\">A+</option>\n                    <option value=\"A-\">A-</option>\n                    <option value=\"B+\">B+</option>\n                    <option value=\"B-\">B-</option>\n                    <option value=\"AB+\">AB+</option>\n                    <option value=\"AB-\">AB-</option>\n                    <option value=\"O+\">O+</option>\n                    <option value=\"O-\">O-</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Right</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightRight}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightRight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Left</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightLeft}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightLeft', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Physical Disability</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.physicalDisability}\n                    onChange={(e) => handlePhysicalDetailsChange('physicalDisability', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Identification Mark</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.identificationMark}\n                    onChange={(e) => handlePhysicalDetailsChange('identificationMark', e.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"form-section\">\n            <h3>EDUCATION DETAILS</h3>\n\n            {education.map((edu, index) => (\n              <div key={index} className=\"education-section\">\n                <h4>Education {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Examination Passed</label>\n                    <input\n                      type=\"text\"\n                      value={edu.examination}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].examination = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Specialisation</label>\n                    <input\n                      type=\"text\"\n                      value={edu.specialisation}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].specialisation = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>School/College/Institution</label>\n                    <input\n                      type=\"text\"\n                      value={edu.schoolCollege}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].schoolCollege = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>University/Board</label>\n                    <input\n                      type=\"text\"\n                      value={edu.university}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].university = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Full Time/Part Time/Correspondence</label>\n                    <select\n                      value={edu.fullTimePartTime}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].fullTimePartTime = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    >\n                      <option value=\"\">Select Type</option>\n                      <option value=\"Full Time\">Full Time</option>\n                      <option value=\"Part Time\">Part Time</option>\n                      <option value=\"Correspondence\">Correspondence</option>\n                    </select>\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Duration of Course</label>\n                    <input\n                      type=\"text\"\n                      value={edu.duration}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].duration = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Month & Year of Passing</label>\n                    <input\n                      type=\"text\"\n                      value={edu.monthYearPassing}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].monthYearPassing = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Grade/% Marks</label>\n                    <input\n                      type=\"text\"\n                      value={edu.gradeMarks}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].gradeMarks = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Distinctions/Scholarships/Prizes Won</label>\n                    <textarea\n                      value={edu.distinctions}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].distinctions = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Certificate/Document Upload</label>\n                    <input\n                      type=\"file\"\n                      accept=\"image/*,.pdf\"\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'education', 'certificateFile', index)}\n                    />\n                    {edu.certificateFile && (\n                      <div className=\"file-preview\">\n                        <span className=\"file-name\">{edu.certificateFile.name}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addEducation} className=\"btn-add\">\n              Add Education\n            </button>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"form-section\">\n            <h3>WORK HISTORY DATA</h3>\n\n            {workExperience.map((experience, index) => (\n              <div key={index} className=\"work-experience-section\">\n                <h4>Work Experience {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Employer's Name</label>\n                    <input\n                      type=\"text\"\n                      value={experience.employerName}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].employerName = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Address</label>\n                    <textarea\n                      value={experience.address}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].address = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>From Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.fromDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fromDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>To Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.toDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].toDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Position Held</label>\n                    <input\n                      type=\"text\"\n                      value={experience.lastPosition}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastPosition = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Designation</label>\n                    <input\n                      type=\"text\"\n                      value={experience.designation}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].designation = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Nature of Duties</label>\n                    <textarea\n                      value={experience.natureOfDuties}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].natureOfDuties = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Name & Designation of Immediate Superior</label>\n                    <input\n                      type=\"text\"\n                      value={experience.immediateSuperior}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].immediateSuperior = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Emoluments (Rs. Per month at joining)</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossEmoluments}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossEmoluments = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Drawn</label>\n                    <input\n                      type=\"number\"\n                      value={experience.lastDrawn}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastDrawn = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Basic Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.basicSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].basicSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Fixed Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.fixedSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fixedSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Variable Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.variableSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].variableSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addWorkExperience} className=\"btn-add\">\n              Add Work Experience\n            </button>\n          </div>\n        );\n\n      case 6:\n        return (\n          <div className=\"form-section\">\n            <h3>GENERAL DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Previous Interview with MH Group</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Have you ever been interviewed by any of the MH Group of Companies?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"true\"\n                        checked={previousEmployment.hasBeenInterviewed === true}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"false\"\n                        checked={previousEmployment.hasBeenInterviewed === false}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {previousEmployment.hasBeenInterviewed && (\n                  <>\n                    <div className=\"form-group\">\n                      <label>Date/Year</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewDate}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewDate: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Position</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewPosition}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewPosition: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Company</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewCompany}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewCompany: e.target.value }))}\n                      />\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>References & Additional Information</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Relatives/Acquaintance in MH Group - Name</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.name}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, name: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Relationship</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.relationship}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, relationship: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Position</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.position}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, position: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Company & Phone No.</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.companyAndPhone}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, companyAndPhone: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group full-width\">\n                  <label>How did you come to know of this position?</label>\n                  <textarea\n                    value={references.howDidYouKnow}\n                    onChange={(e) => setReferences(prev => ({ ...prev, howDidYouKnow: e.target.value }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Are you engaged in any personal business?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"true\"\n                        checked={references.personalBusiness === true}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"false\"\n                        checked={references.personalBusiness === false}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.personalBusiness && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, indicate nature of business</label>\n                    <textarea\n                      value={references.businessNature}\n                      onChange={(e) => setReferences(prev => ({ ...prev, businessNature: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>Do you have any contract/bond with your previous employer?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"true\"\n                        checked={references.contractWithPreviousEmployer === true}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"false\"\n                        checked={references.contractWithPreviousEmployer === false}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.contractWithPreviousEmployer && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, Give Details</label>\n                    <textarea\n                      value={references.contractDetails}\n                      onChange={(e) => setReferences(prev => ({ ...prev, contractDetails: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>If selected, when can you join?</label>\n                  <input\n                    type=\"date\"\n                    value={references.whenCanJoin}\n                    onChange={(e) => setReferences(prev => ({ ...prev, whenCanJoin: e.target.value }))}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 7:\n        return (\n          <div className=\"form-section\">\n            <h3>CRIMINAL RECORDS</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Have you ever been involved in any criminal proceedings?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenInvolved === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenInvolved === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenInvolved && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.details}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, details: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Have you ever been convicted of any offence?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenConvicted === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenConvicted === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenConvicted && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.convictionDetails}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, convictionDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        );\n\n      case 8:\n        return (\n          <div className=\"form-section\">\n            <h3>DECLARATION FORM</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-text\">\n                  <p><strong>I hereby declare that:</strong></p>\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>\n                  <input\n                    type=\"checkbox\"\n                    checked={declaration.notConnectedToDirectors}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, notConnectedToDirectors: e.target.checked }))}\n                  />\n                  I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\n                </label>\n              </div>\n\n              <div className=\"form-group\">\n                <label>OR I am a partner or relative of a Director of the Company</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"true\"\n                      checked={declaration.isPartnerOrRelative === true}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"false\"\n                      checked={declaration.isPartnerOrRelative === false}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n\n              {declaration.isPartnerOrRelative && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, provide details</label>\n                  <textarea\n                    value={declaration.partnerRelativeDetails}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, partnerRelativeDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Place</label>\n                <input\n                  type=\"text\"\n                  value={declaration.place}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, place: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label>Date</label>\n                <input\n                  type=\"date\"\n                  value={declaration.date}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, date: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>Applicant's Signature</label>\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your full name as signature\"\n                  value={declaration.applicantSignature}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, applicantSignature: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-note\">\n                  <p><em>Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.</em></p>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step {currentStep}</div>;\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n\n  return (\n    <div className=\"onboarding-form\">\n      <div className=\"form-header\">\n        <h2>Employee Onboarding Form</h2>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress-fill\"\n            style={{ width: `${(currentStep / totalSteps) * 100}%` }}\n          ></div>\n        </div>\n        <p>Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}</p>\n      </div>\n\n\n      {/* Popup Modal for feedback messages */}\n      {(successMessage || errorMessage) && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content\">\n            <div className={successMessage ? \"success-message\" : \"error-message\"}>\n              {successMessage || errorMessage}\n            </div>\n            <button className=\"modal-close\" onClick={closeModal}>Close</button>\n          </div>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        {renderStep()}\n      </form>\n\n      <div className=\"form-navigation\">\n        <div className=\"nav-left\">\n          {currentStep > 1 && (\n            <button type=\"button\" onClick={prevStep} className=\"btn-secondary\">\n              Previous\n            </button>\n          )}\n        </div>\n\n        <div className=\"nav-center\">\n          <button type=\"button\" onClick={previewFormData} className=\"btn-preview\">\n            Preview JSON\n          </button>\n        </div>\n\n        <div className=\"nav-right\">\n          {currentStep < totalSteps ? (\n            <button type=\"button\" onClick={nextStep} className=\"btn-primary\">\n              Next\n            </button>\n          ) : (\n            <button type=\"button\" onClick={() => {\n              const form = document.querySelector('form');\n              if (form) {\n                form.requestSubmit();\n              }\n            }} className=\"btn-primary\">\n              Submit Form\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* JSON Preview Modal */}\n      {showJsonPreview && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content json-modal\">\n            <h3>Form Data JSON Preview</h3>\n            <pre className=\"json-preview\">\n              {jsonPreviewData}\n            </pre>\n            <div className=\"modal-actions\">\n              <button\n                onClick={() => setShowJsonPreview(false)}\n                className=\"modal-close\"\n              >\n                Close\n              </button>\n              <button\n                onClick={() => {\n                  navigator.clipboard.writeText(jsonPreviewData);\n                  alert('JSON copied to clipboard!');\n                }}\n                className=\"btn-copy\"\n              >\n                Copy JSON\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default OnboardingForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAC7B,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsH7C,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAU,KAAK,CAAC;EACtE,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM;IAAEiB;EAAM,CAAC,GAAGf,SAAS,CAAoB,CAAC;EAChD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAkB;IACtEoB,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,GAAG,EAAE,EAAE;IACPC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAmB,CACrE;IAAE2C,QAAQ,EAAE,EAAE;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC1D,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAkB;IACtEiD,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAiB,CACjE;IAAE6D,IAAI,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEvC,WAAW,EAAE,EAAE;IAAEwC,aAAa,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,uBAAuB,EAAE;EAAG,CAAC,CAChH,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAkB;IACtEoE,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5E,QAAQ,CAAqB;IAC/E6E,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,EAAE;IACrBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAa;IACvDmF,kBAAkB,EAAE;MAClBtB,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBsB,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC;IACDC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,cAAc,EAAE,EAAE;IAClBC,4BAA4B,EAAE,KAAK;IACnCC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7F,QAAQ,CAAmB,CACrE;IACE8F,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAc,CACtD;IACE+G,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBnB,QAAQ,EAAE,EAAE;IACZoB,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE;EACnB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM0H,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC;IACrD6H,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,KAAK;IACvBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlI,QAAQ,CAAC;IAC7CmI,uBAAuB,EAAE,KAAK;IAC9BC,mBAAmB,EAAE,KAAK;IAC1BC,sBAAsB,EAAE,EAAE;IAC1BC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,2BAA2B,GAAGA,CAACC,KAA4B,EAAEC,KAAa,KAAK;IACnFxH,kBAAkB,CAACyH,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,2BAA2B,GAAGA,CAACH,KAA4B,EAAEC,KAAa,KAAK;IACnF3F,kBAAkB,CAAC4F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,2BAA2B,GAAGA,CAACJ,KAA4B,EAAEC,KAAa,KAAK;IACnFxE,kBAAkB,CAACyE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMI,YAAY,GAAIC,IAAU,IAAsB;IACpD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC1BI,MAAM,CAACG,MAAM,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAgB,CAAC;MACtDJ,MAAM,CAACK,OAAO,GAAGC,KAAK,IAAIP,MAAM,CAACO,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACX,IAAiB,EAAEY,OAA8C,EAAElB,KAAa,EAAEmB,KAAc,KAAK;IAC7H;IACA,IAAIb,IAAI,IAAIY,OAAO,KAAK,UAAU,IAAIlB,KAAK,KAAK,eAAe,EAAE;MAC/D,MAAMoB,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;MAC5B,IAAId,IAAI,CAACe,IAAI,GAAGD,OAAO,EAAE;QACvBE,KAAK,CAAC,wCAAwC,CAAC;QAC/C;MACF;IACF;IAEA,IAAIJ,OAAO,KAAK,UAAU,EAAE;MAC1BzI,kBAAkB,CAACyH,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIY,OAAO,KAAK,UAAU,EAAE;MACjC5G,kBAAkB,CAAC4F,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIY,OAAO,KAAK,WAAW,IAAIC,KAAK,KAAKI,SAAS,EAAE;MACzDnD,YAAY,CAAC8B,IAAI,IAAI;QACnB,MAAMsB,YAAY,GAAG,CAAC,GAAGtB,IAAI,CAAC;QAC9BsB,YAAY,CAACL,KAAK,CAAC,GAAG;UAAE,GAAGK,YAAY,CAACL,KAAK,CAAC;UAAE,CAACnB,KAAK,GAAGM;QAAK,CAAC;QAC/D,OAAOkB,YAAY;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzH,iBAAiB,CAACkG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEjG,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,CAAC,CAAC;EACjG,CAAC;EAED,MAAMsH,eAAe,GAAGA,CAAA,KAAM;IAC5BxG,gBAAgB,CAACgF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAE/E,IAAI,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEvC,WAAW,EAAE,EAAE;MAAEwC,aAAa,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,uBAAuB,EAAE;IAAG,CAAC,CAAC,CAAC;EACtJ,CAAC;EAED,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxE,iBAAiB,CAAC+C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAClC9C,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IACzBxD,YAAY,CAAC8B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC7B7B,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,EAAE;MACpBnB,QAAQ,EAAE,EAAE;MACZoB,gBAAgB,EAAE,EAAE;MACpBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgD,YAAY,GAAIC,IAAY,IAAK;IACrC,MAAMC,MAAM,GAAG,CACb,EAAE,EACF,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,kBAAkB,CACnB;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAI,EAAE;EAC3B,CAAC;EAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEpD,WAAW,EAAE,cAAc,EAAEE,UAAU,CAAC;IACtF,IAAIF,WAAW,GAAGE,UAAU,EAAE;MAC5BD,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;MAC/BmD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEpD,WAAW,GAAG,CAAC,CAAC;IACjD,CAAC,MAAM;MACLmD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIrD,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMsD,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACnC,MAAMC,cAAc,GAAG,EAAAP,qBAAA,GACrB7J,eAAe,CAACE,SAAS,cAAA2J,qBAAA,uBAAzBA,qBAAA,CAA2BQ,IAAI,CAAC,CAAC,GAAAP,qBAAA,GACjC9J,eAAe,CAACI,QAAQ,cAAA0J,qBAAA,uBAAxBA,qBAAA,CAA0BO,IAAI,CAAC,CAAC,GAAAN,qBAAA,GAChC/J,eAAe,CAACK,WAAW,cAAA0J,qBAAA,uBAA3BA,qBAAA,CAA6BM,IAAI,CAAC,CAAC,GAAAL,qBAAA,GACnChK,eAAe,CAACM,MAAM,cAAA0J,qBAAA,uBAAtBA,qBAAA,CAAwBK,IAAI,CAAC,CAAC,GAAAJ,qBAAA,GAC9BjK,eAAe,CAACW,WAAW,cAAAsJ,qBAAA,uBAA3BA,qBAAA,CAA6BI,IAAI,CAAC,CAAC,GAAAH,qBAAA,GACnClK,eAAe,CAACc,aAAa,cAAAoJ,qBAAA,uBAA7BA,qBAAA,CAA+BG,IAAI,CAAC,CAAC,GAAAF,qBAAA,GACrCnK,eAAe,CAACe,kBAAkB,cAAAoJ,qBAAA,uBAAlCA,qBAAA,CAAoCE,IAAI,CAAC,CAAC,CAC3C;IACD,OAAOD,cAAc,CAACE,KAAK,CAACC,OAAO,CAAC;EACtC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,KAAsB,IAAK;IACrDA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBlL,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAI,CAACkK,sBAAsB,CAAC,CAAC,EAAE;MAC7BlK,eAAe,CAAC,2EAA2E,CAAC;MAC5F;IACF;IACA,IAAI4G,WAAW,KAAKE,UAAU,EAAE;MAC9B9G,eAAe,CAAC,uDAAuD,CAAC;MACxE;IACF;IAEA,IAAI;MACF;MACA,MAAMiL,wBAAwB,GAAG;QAAE,GAAG3K;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACsB,aAAa,EAAE;QACjCqJ,wBAAwB,CAACrJ,aAAa,GAAG,MAAMuG,YAAY,CAAC7H,eAAe,CAACsB,aAAa,CAAQ;MACnG;MAEA,MAAMsJ,wBAAwB,GAAG;QAAE,GAAG/I;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACS,YAAY,EAAE;QAChCsI,wBAAwB,CAACtI,YAAY,GAAG,MAAMuF,YAAY,CAAChG,eAAe,CAACS,YAAY,CAAQ;MACjG;MACA,IAAIT,eAAe,CAACU,OAAO,EAAE;QAC3BqI,wBAAwB,CAACrI,OAAO,GAAG,MAAMsF,YAAY,CAAChG,eAAe,CAACU,OAAO,CAAQ;MACvF;MACA,IAAIV,eAAe,CAACW,UAAU,EAAE;QAC9BoI,wBAAwB,CAACpI,UAAU,GAAG,MAAMqF,YAAY,CAAChG,eAAe,CAACW,UAAU,CAAQ;MAC7F;MAEA,MAAMqI,kBAAkB,GAAG,MAAM9C,OAAO,CAAC+C,GAAG,CAC1CnF,SAAS,CAACoF,GAAG,CAAC,MAAOC,GAAG,IAAK;QAC3B,MAAMC,YAAY,GAAG;UAAE,GAAGD;QAAI,CAAC;QAC/B,IAAIA,GAAG,CAAC3E,eAAe,EAAE;UACvB4E,YAAY,CAAC5E,eAAe,GAAG,MAAMwB,YAAY,CAACmD,GAAG,CAAC3E,eAAe,CAAQ;QAC/E;QACA,OAAO4E,YAAY;MACrB,CAAC,CACH,CAAC;;MAED;MACA,MAAMC,gBAAgB,GAAG;QACvBlL,eAAe,EAAE2K,wBAAwB;QACzCpJ,cAAc;QACdM,eAAe,EAAE+I,wBAAwB;QACzCnI,aAAa;QACbO,eAAe;QACfS,kBAAkB;QAClBM,UAAU;QACVW,cAAc;QACdiB,SAAS,EAAEkF,kBAAkB;QAC7BpE,eAAe;QACfM,WAAW;QACXoE,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7CC,WAAW,EAAE;MACf,CAAC;;MAED;MACA7B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6B,IAAI,CAACC,SAAS,CAACN,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAElF;MACA,MAAMO,GAAG,GAAG,wDAAwD1L,KAAK,GAAG;MAC5E,MAAMhB,KAAK,CAAC2M,IAAI,CAACD,GAAG,EAAEP,gBAAgB,EAAE;QACtCS,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEFnM,iBAAiB,CAAC,8BAA8B,CAAC;MACjDE,eAAe,CAAC,IAAI,CAAC;;MAErB;MACAkM,gBAAgB,CAACV,gBAAgB,CAAC;IAEpC,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C9I,eAAe,CAAC,oDAAoD,CAAC;MACrEF,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMoM,gBAAgB,GAAIC,IAAS,IAAK;IACtC,MAAMC,UAAU,GAAGP,IAAI,CAACC,SAAS,CAACK,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAChD,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAmB,CAAC,CAAC;IACjE,MAAMR,GAAG,GAAGS,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGd,GAAG;IACfW,IAAI,CAACI,QAAQ,GAAG,mBAAmB,IAAIpB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAChFJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;IACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAC/BF,GAAG,CAACY,eAAe,CAACrB,GAAG,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA,MAAMpC,wBAAwB,GAAG;QAAE,GAAG3K;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACsB,aAAa,EAAE;QACjCqJ,wBAAwB,CAACrJ,aAAa,GAAG,UAAUtB,eAAe,CAACsB,aAAa,CAACqB,IAAI,GAAU;MACjG;MAEA,MAAMiI,wBAAwB,GAAG;QAAE,GAAG/I;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACS,YAAY,EAAE;QAChCsI,wBAAwB,CAACtI,YAAY,GAAG,UAAUT,eAAe,CAACS,YAAY,CAACK,IAAI,GAAU;MAC/F;MACA,IAAId,eAAe,CAACU,OAAO,EAAE;QAC3BqI,wBAAwB,CAACrI,OAAO,GAAG,UAAUV,eAAe,CAACU,OAAO,CAACI,IAAI,GAAU;MACrF;MACA,IAAId,eAAe,CAACW,UAAU,EAAE;QAC9BoI,wBAAwB,CAACpI,UAAU,GAAG,UAAUX,eAAe,CAACW,UAAU,CAACG,IAAI,GAAU;MAC3F;MAEA,MAAMkI,kBAAkB,GAAGlF,SAAS,CAACoF,GAAG,CAAEC,GAAG,IAAK;QAChD,MAAMC,YAAY,GAAG;UAAE,GAAGD;QAAI,CAAC;QAC/B,IAAIA,GAAG,CAAC3E,eAAe,EAAE;UACvB4E,YAAY,CAAC5E,eAAe,GAAG,UAAU2E,GAAG,CAAC3E,eAAe,CAAC1D,IAAI,GAAU;QAC7E;QACA,OAAOsI,YAAY;MACrB,CAAC,CAAC;MAEF,MAAM+B,WAAW,GAAG;QAClBhN,eAAe,EAAE2K,wBAAwB;QACzCpJ,cAAc;QACdM,eAAe,EAAE+I,wBAAwB;QACzCnI,aAAa;QACbO,eAAe;QACfS,kBAAkB;QAClBM,UAAU;QACVW,cAAc;QACdiB,SAAS,EAAEkF,kBAAkB;QAC7BpE,eAAe;QACfM,WAAW;QACXoE,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7CC,WAAW,EAAE;MACf,CAAC;;MAED;MACA,MAAMQ,UAAU,GAAGP,IAAI,CAACC,SAAS,CAACwB,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;MACvDvD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoC,UAAU,CAAC;MAC7ChM,kBAAkB,CAACgM,UAAU,CAAC;MAC9BlM,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACAgM,gBAAgB,CAACoB,WAAW,CAAC;IAC/B,CAAC,CAAC,OAAOxE,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCM,KAAK,CAAC,0BAA0B,CAAC;IACnC;EACF,CAAC;EAED,MAAMmE,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ3G,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEpH,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGzBrO,OAAA;YAAKgO,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCjO,OAAA;cAAKgO,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBnN,eAAe,CAACsB,aAAa,gBAC5BpC,OAAA;kBACEsO,GAAG,EAAEtB,GAAG,CAACC,eAAe,CAACnM,eAAe,CAACsB,aAAa,CAAE;kBACxDmM,GAAG,EAAC,gBAAgB;kBACpBP,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,gBAEFrO,OAAA;kBAAKgO,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BjO,OAAA;oBAAKwO,KAAK,EAAC,IAAI;oBAACxK,MAAM,EAAC,IAAI;oBAACyK,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAAX,QAAA,gBAC/FjO,OAAA;sBAAM6O,CAAC,EAAC;oBAA2C;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3DrO,OAAA;sBAAQ8O,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAG;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrO,OAAA;gBAAMgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BjO,OAAA;gBAAKgO,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BjO,OAAA;kBAAGgO,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpErO,OAAA;kBAAOgO,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,eAE/B,eAAAjO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXkC,MAAM,EAAC,SAAS;oBAChBC,QAAQ;oBACRC,QAAQ,EAAGC,CAAC;sBAAA,IAAAC,eAAA;sBAAA,OAAK9F,gBAAgB,CAAC,EAAA8F,eAAA,GAAAD,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,eAAe,CAAC;oBAAA,CAAC;oBAC5FrB,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EACPvN,eAAe,CAACsB,aAAa,iBAC5BpC,OAAA;kBAAGgO,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEnN,eAAe,CAACsB,aAAa,CAACqB;gBAAI;kBAAAyK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACjE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrO,OAAA;YAAKgO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXmC,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACE,SAAU;gBACjCmO,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,WAAW,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACG,UAAW;gBAClCkO,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,YAAY,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXmC,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACI,QAAS;gBAChCiO,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,UAAU,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXmC,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACK,WAAY;gBACnCgO,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,aAAa,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBrO,OAAA;gBACEkP,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACM,MAAO;gBAC9B+N,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,QAAQ,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK,CAAE;gBAAA0F,QAAA,gBAEvEjO,OAAA;kBAAQuI,KAAK,EAAC,EAAE;kBAAA0F,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCrO,OAAA;kBAAQuI,KAAK,EAAC,MAAM;kBAAA0F,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrO,OAAA;kBAAQuI,KAAK,EAAC,QAAQ;kBAAA0F,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrO,OAAA;kBAAQuI,KAAK,EAAC,OAAO;kBAAA0F,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACO,UAAW;gBAClC8N,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,YAAY,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClBrO,OAAA;gBACE+M,IAAI,EAAC,QAAQ;gBACbxE,KAAK,EAAEzH,eAAe,CAACQ,GAAI;gBAC3B6N,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,KAAK,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACS,YAAa;gBACpC4N,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,cAAc,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7BrO,OAAA;gBACEuI,KAAK,EAAEzH,eAAe,CAACU,aAAc;gBACrC2N,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,eAAe,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK,CAAE;gBAAA0F,QAAA,gBAE9EjO,OAAA;kBAAQuI,KAAK,EAAC,EAAE;kBAAA0F,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCrO,OAAA;kBAAQuI,KAAK,EAAC,QAAQ;kBAAA0F,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrO,OAAA;kBAAQuI,KAAK,EAAC,SAAS;kBAAA0F,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCrO,OAAA;kBAAQuI,KAAK,EAAC,UAAU;kBAAA0F,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CrO,OAAA;kBAAQuI,KAAK,EAAC,SAAS;kBAAA0F,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXmC,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACW,WAAY;gBACnC0N,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,aAAa,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACY,QAAS;gBAChCyN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,UAAU,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACa,WAAY;gBACnCwN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,aAAa,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BrO,OAAA;gBACE+M,IAAI,EAAC,KAAK;gBACVmC,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACc,aAAc;gBACrCuN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,eAAe,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCrO,OAAA;gBACE+M,IAAI,EAAC,KAAK;gBACVmC,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACe,kBAAmB;gBAC1CsN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,oBAAoB,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACgB,eAAgB;gBACvCqN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,iBAAiB,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBrO,OAAA;gBACE+M,IAAI,EAAC,OAAO;gBACZmC,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACiB,KAAM;gBAC7BoN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,OAAO,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjO,OAAA;gBAAAiO,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCrO,OAAA;gBACEkP,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACkB,cAAe;gBACtCmN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,gBAAgB,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjO,OAAA;gBAAAiO,QAAA,EAAO;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCrO,OAAA;gBACEkP,QAAQ;gBACR3G,KAAK,EAAEzH,eAAe,CAACmB,gBAAiB;gBACxCkN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,kBAAkB,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACoB,UAAW;gBAClCiN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,YAAY,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEzH,eAAe,CAACqB,YAAa;gBACpCgN,QAAQ,EAAGC,CAAC,IAAK/G,2BAA2B,CAAC,cAAc,EAAE+G,CAAC,CAACE,MAAM,CAAC/G,KAAK;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrO,OAAA;YAAKgO,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjO,OAAA;cAAAiO,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBrO,OAAA;cAAKgO,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE5F,eAAe,CAACE,UAAW;kBAClCsM,QAAQ,EAAGC,CAAC,IAAK3G,2BAA2B,CAAC,YAAY,EAAE2G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE5F,eAAe,CAACG,iBAAkB;kBACzCqM,QAAQ,EAAGC,CAAC,IAAK3G,2BAA2B,CAAC,mBAAmB,EAAE2G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE5F,eAAe,CAACI,aAAc;kBACrCoM,QAAQ,EAAGC,CAAC,IAAK3G,2BAA2B,CAAC,eAAe,EAAE2G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE5F,eAAe,CAACK,cAAe;kBACtCmM,QAAQ,EAAGC,CAAC,IAAK3G,2BAA2B,CAAC,gBAAgB,EAAE2G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE5F,eAAe,CAACM,gBAAiB;kBACxCkM,QAAQ,EAAGC,CAAC,IAAK3G,2BAA2B,CAAC,kBAAkB,EAAE2G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXmC,QAAQ;kBACR3G,KAAK,EAAE5F,eAAe,CAACO,SAAU;kBACjCiM,QAAQ,EAAGC,CAAC,IAAK3G,2BAA2B,CAAC,WAAW,EAAE2G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXkC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAI,gBAAA;oBAAA,OAAKjG,gBAAgB,CAAC,EAAAiG,gBAAA,GAAAJ,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAC,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC;kBAAA;gBAAC;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC,EACD1L,eAAe,CAACU,OAAO,iBACtBrD,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjO,OAAA;oBAAMgO,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEtL,eAAe,CAACU,OAAO,CAACI;kBAAI;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXmC,QAAQ;kBACR3G,KAAK,EAAE5F,eAAe,CAACQ,YAAa;kBACpCgM,QAAQ,EAAGC,CAAC,IAAK3G,2BAA2B,CAAC,cAAc,EAAE2G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXkC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAK,gBAAA;oBAAA,OAAKlG,gBAAgB,CAAC,EAAAkG,gBAAA,GAAAL,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAE,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC;kBAAA;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,EACD1L,eAAe,CAACW,UAAU,iBACzBtD,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjO,OAAA;oBAAMgO,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEtL,eAAe,CAACW,UAAU,CAACG;kBAAI;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXkC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAM,gBAAA;oBAAA,OAAKnG,gBAAgB,CAAC,EAAAmG,gBAAA,GAAAN,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAG,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC;kBAAA;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,EACD1L,eAAe,CAACS,YAAY,iBAC3BpD,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjO,OAAA;oBAAMgO,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEtL,eAAe,CAACS,YAAY,CAACK;kBAAI;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrO,OAAA;YAAKgO,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjO,OAAA;cAAAiO,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtBhM,cAAc,CAACwJ,GAAG,CAAC,CAAC8D,KAAK,EAAElG,KAAK,kBAC/BzJ,OAAA;cAAiBgO,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBAC7CjO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEoH,KAAK,CAACpN,QAAS;kBACtB4M,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGvN,cAAc,CAAC;oBACrCuN,SAAS,CAACnG,KAAK,CAAC,CAAClH,QAAQ,GAAG6M,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC1CjG,iBAAiB,CAACsN,SAAS,CAAC;kBAC9B;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BjO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,UAAU;oBACf8C,OAAO,EAAEF,KAAK,CAACnN,KAAM;oBACrB2M,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGvN,cAAc,CAAC;sBACrCuN,SAAS,CAACnG,KAAK,CAAC,CAACjH,KAAK,GAAG4M,CAAC,CAACE,MAAM,CAACO,OAAO;sBACzCvN,iBAAiB,CAACsN,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,UAAU;oBACf8C,OAAO,EAAEF,KAAK,CAAClN,IAAK;oBACpB0M,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGvN,cAAc,CAAC;sBACrCuN,SAAS,CAACnG,KAAK,CAAC,CAAChH,IAAI,GAAG2M,CAAC,CAACE,MAAM,CAACO,OAAO;sBACxCvN,iBAAiB,CAACsN,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,QAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,UAAU;oBACf8C,OAAO,EAAEF,KAAK,CAACjN,KAAM;oBACrByM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGvN,cAAc,CAAC;sBACrCuN,SAAS,CAACnG,KAAK,CAAC,CAAC/G,KAAK,GAAG0M,CAAC,CAACE,MAAM,CAACO,OAAO;sBACzCvN,iBAAiB,CAACsN,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAlDE5E,KAAK;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDV,CACN,CAAC,eACFrO,OAAA;cAAQ+M,IAAI,EAAC,QAAQ;cAAC+C,OAAO,EAAE/F,gBAAiB;cAACiE,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,oBACErO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBrO,OAAA;YAAKgO,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjO,OAAA;cAAAiO,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtB9K,aAAa,CAACsI,GAAG,CAAC,CAACkE,MAAM,EAAEtG,KAAK,kBAC/BzJ,OAAA;cAAiBgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAChDjO,OAAA;gBAAKgO,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBjO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEwH,MAAM,CAACtM,IAAK;oBACnB0L,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGzM,aAAa,CAAC;sBACrCyM,UAAU,CAACvG,KAAK,CAAC,CAAChG,IAAI,GAAG2L,CAAC,CAACE,MAAM,CAAC/G,KAAK;sBACvC/E,gBAAgB,CAACwM,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEwH,MAAM,CAACrM,YAAa;oBAC3ByL,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGzM,aAAa,CAAC;sBACrCyM,UAAU,CAACvG,KAAK,CAAC,CAAC/F,YAAY,GAAG0L,CAAC,CAACE,MAAM,CAAC/G,KAAK;sBAC/C/E,gBAAgB,CAACwM,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEwH,MAAM,CAAC5O,WAAY;oBAC1BgO,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGzM,aAAa,CAAC;sBACrCyM,UAAU,CAACvG,KAAK,CAAC,CAACtI,WAAW,GAAGiO,CAAC,CAACE,MAAM,CAAC/G,KAAK;sBAC9C/E,gBAAgB,CAACwM,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEwH,MAAM,CAACpM,aAAc;oBAC5BwL,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGzM,aAAa,CAAC;sBACrCyM,UAAU,CAACvG,KAAK,CAAC,CAAC9F,aAAa,GAAGyL,CAAC,CAACE,MAAM,CAAC/G,KAAK;sBAChD/E,gBAAgB,CAACwM,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzBrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEwH,MAAM,CAACnM,UAAW;oBACzBuL,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGzM,aAAa,CAAC;sBACrCyM,UAAU,CAACvG,KAAK,CAAC,CAAC7F,UAAU,GAAGwL,CAAC,CAACE,MAAM,CAAC/G,KAAK;sBAC7C/E,gBAAgB,CAACwM,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxCrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEwH,MAAM,CAAClM,uBAAwB;oBACtCsL,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGzM,aAAa,CAAC;sBACrCyM,UAAU,CAACvG,KAAK,CAAC,CAAC5F,uBAAuB,GAAGuL,CAAC,CAACE,MAAM,CAAC/G,KAAK;sBAC1D/E,gBAAgB,CAACwM,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1EE5E,KAAK;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2EV,CACN,CAAC,eACFrO,OAAA;cAAQ+M,IAAI,EAAC,QAAQ;cAAC+C,OAAO,EAAE9F,eAAgB;cAACgE,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBrO,OAAA;YAAKgO,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjO,OAAA;cAAAiO,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBrO,OAAA;cAAKgO,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEzE,eAAe,CAACE,MAAO;kBAC9BmL,QAAQ,EAAGC,CAAC,IAAK1G,2BAA2B,CAAC,QAAQ,EAAE0G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEzE,eAAe,CAACG,MAAO;kBAC9BkL,QAAQ,EAAGC,CAAC,IAAK1G,2BAA2B,CAAC,QAAQ,EAAE0G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BrO,OAAA;kBACEuI,KAAK,EAAEzE,eAAe,CAACI,UAAW;kBAClCiL,QAAQ,EAAGC,CAAC,IAAK1G,2BAA2B,CAAC,YAAY,EAAE0G,CAAC,CAACE,MAAM,CAAC/G,KAAK,CAAE;kBAAA0F,QAAA,gBAE3EjO,OAAA;oBAAQuI,KAAK,EAAC,EAAE;oBAAA0F,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CrO,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA0F,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrO,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA0F,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrO,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA0F,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrO,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA0F,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrO,OAAA;oBAAQuI,KAAK,EAAC,KAAK;oBAAA0F,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCrO,OAAA;oBAAQuI,KAAK,EAAC,KAAK;oBAAA0F,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCrO,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA0F,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrO,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA0F,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEzE,eAAe,CAACK,aAAc;kBACrCgL,QAAQ,EAAGC,CAAC,IAAK1G,2BAA2B,CAAC,eAAe,EAAE0G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEzE,eAAe,CAACM,YAAa;kBACpC+K,QAAQ,EAAGC,CAAC,IAAK1G,2BAA2B,CAAC,cAAc,EAAE0G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEzE,eAAe,CAACO,kBAAmB;kBAC1C8K,QAAQ,EAAGC,CAAC,IAAK1G,2BAA2B,CAAC,oBAAoB,EAAE0G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEzE,eAAe,CAACQ,kBAAmB;kBAC1C6K,QAAQ,EAAGC,CAAC,IAAK1G,2BAA2B,CAAC,oBAAoB,EAAE0G,CAAC,CAACE,MAAM,CAAC/G,KAAK;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzB5H,SAAS,CAACoF,GAAG,CAAC,CAACC,GAAG,EAAErC,KAAK,kBACxBzJ,OAAA;YAAiBgO,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC5CjO,OAAA;cAAAiO,QAAA,GAAI,YAAU,EAACxE,KAAK,GAAG,CAAC;YAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9BrO,OAAA;cAAKgO,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEuD,GAAG,CAACnF,WAAY;kBACvBwI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC9C,WAAW,GAAGyI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAChD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEuD,GAAG,CAAClF,cAAe;kBAC1BuI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC7C,cAAc,GAAGwI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACnD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEuD,GAAG,CAACjF,aAAc;kBACzBsI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC5C,aAAa,GAAGuI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAClD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEuD,GAAG,CAAChF,UAAW;kBACtBqI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC3C,UAAU,GAAGsI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC/C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDrO,OAAA;kBACEuI,KAAK,EAAEuD,GAAG,CAAC/E,gBAAiB;kBAC5BoI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC1C,gBAAgB,GAAGqI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACrD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B,CAAE;kBAAAmE,QAAA,gBAEFjO,OAAA;oBAAQuI,KAAK,EAAC,EAAE;oBAAA0F,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCrO,OAAA;oBAAQuI,KAAK,EAAC,WAAW;oBAAA0F,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CrO,OAAA;oBAAQuI,KAAK,EAAC,WAAW;oBAAA0F,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CrO,OAAA;oBAAQuI,KAAK,EAAC,gBAAgB;oBAAA0F,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEuD,GAAG,CAAClG,QAAS;kBACpBuJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC7D,QAAQ,GAAGwJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC7C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEuD,GAAG,CAAC9E,gBAAiB;kBAC5BmI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACzC,gBAAgB,GAAGoI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACrD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAEuD,GAAG,CAAC7E,UAAW;kBACtBkI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACxC,UAAU,GAAGmI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC/C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDrO,OAAA;kBACEuI,KAAK,EAAEuD,GAAG,CAAC5E,YAAa;kBACxBiI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMtF,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACvC,YAAY,GAAGkI,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACjD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXkC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAa,gBAAA;oBAAA,OAAK1G,gBAAgB,CAAC,EAAA0G,gBAAA,GAAAb,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAU,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAExG,KAAK,CAAC;kBAAA;gBAAC;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,EACDvC,GAAG,CAAC3E,eAAe,iBAClBnH,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjO,OAAA;oBAAMgO,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEnC,GAAG,CAAC3E,eAAe,CAAC1D;kBAAI;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/HE5E,KAAK;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgIV,CACN,CAAC,eACFrO,OAAA;YAAQ+M,IAAI,EAAC,QAAQ;YAAC+C,OAAO,EAAE5F,YAAa;YAAC8D,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzB7I,cAAc,CAACqG,GAAG,CAAC,CAACqE,UAAU,EAAEzG,KAAK,kBACpCzJ,OAAA;YAAiBgO,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAClDjO,OAAA;cAAAiO,QAAA,GAAI,kBAAgB,EAACxE,KAAK,GAAG,CAAC;YAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCrO,OAAA;cAAKgO,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE2H,UAAU,CAACxK,YAAa;kBAC/ByJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAAC/D,YAAY,GAAG0J,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAClD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBrO,OAAA;kBACEuI,KAAK,EAAE2H,UAAU,CAACvK,OAAQ;kBAC1BwJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAAC9D,OAAO,GAAGyJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC7C9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE2H,UAAU,CAACrK,QAAS;kBAC3BsJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAAC5D,QAAQ,GAAGuJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC9C9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE2H,UAAU,CAACpK,MAAO;kBACzBqJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAAC3D,MAAM,GAAGsJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC5C9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE2H,UAAU,CAACnK,YAAa;kBAC/BoJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAAC1D,YAAY,GAAGqJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAClD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE2H,UAAU,CAAClK,WAAY;kBAC9BmJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACzD,WAAW,GAAGoJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACjD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BrO,OAAA;kBACEuI,KAAK,EAAE2H,UAAU,CAACjK,cAAe;kBACjCkJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACxD,cAAc,GAAGmJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACpD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE2H,UAAU,CAAChK,iBAAkB;kBACpCiJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACvD,iBAAiB,GAAGkJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACvD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DrO,OAAA;kBACE+M,IAAI,EAAC,QAAQ;kBACbxE,KAAK,EAAE2H,UAAU,CAAC/J,eAAgB;kBAClCgJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACtD,eAAe,GAAGiJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACrD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBrO,OAAA;kBACE+M,IAAI,EAAC,QAAQ;kBACbxE,KAAK,EAAE2H,UAAU,CAAC9J,SAAU;kBAC5B+I,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACrD,SAAS,GAAGgJ,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBAC/C9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrO,OAAA;kBACE+M,IAAI,EAAC,QAAQ;kBACbxE,KAAK,EAAE2H,UAAU,CAAC7J,WAAY;kBAC9B8I,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACpD,WAAW,GAAG+I,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACjD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrO,OAAA;kBACE+M,IAAI,EAAC,QAAQ;kBACbxE,KAAK,EAAE2H,UAAU,CAAC5J,WAAY;kBAC9B6I,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACnD,WAAW,GAAG8I,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACjD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrO,OAAA;kBACE+M,IAAI,EAAC,QAAQ;kBACbxE,KAAK,EAAE2H,UAAU,CAAC3J,cAAe;kBACjC4I,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAAClD,cAAc,GAAG6I,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACpD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrO,OAAA;kBACE+M,IAAI,EAAC,QAAQ;kBACbxE,KAAK,EAAE2H,UAAU,CAAC1J,WAAY;kBAC9B2I,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG3K,cAAc,CAAC;oBACzC2K,aAAa,CAAC1G,KAAK,CAAC,CAACjD,WAAW,GAAG4I,CAAC,CAACE,MAAM,CAAC/G,KAAK;oBACjD9C,iBAAiB,CAAC0K,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAzKE5E,KAAK;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0KV,CACN,CAAC,eACFrO,OAAA;YAAQ+M,IAAI,EAAC,QAAQ;YAAC+C,OAAO,EAAE7F,iBAAkB;YAAC+D,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAErBrO,OAAA;YAAKgO,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjO,OAAA;cAAAiO,QAAA,EAAI;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCrO,OAAA;cAAKgO,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAmE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClFrO,OAAA;kBAAKgO,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BjO,OAAA;oBAAAiO,QAAA,gBACEjO,OAAA;sBACE+M,IAAI,EAAC,OAAO;sBACZtJ,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,MAAM;sBACZsH,OAAO,EAAEtL,kBAAkB,CAACE,kBAAkB,KAAK,IAAK;sBACxD0K,QAAQ,EAAEA,CAAA,KAAM3K,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrO,OAAA;oBAAAiO,QAAA,gBACEjO,OAAA;sBACE+M,IAAI,EAAC,OAAO;sBACZtJ,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,OAAO;sBACbsH,OAAO,EAAEtL,kBAAkB,CAACE,kBAAkB,KAAK,KAAM;sBACzD0K,QAAQ,EAAEA,CAAA,KAAM3K,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL9J,kBAAkB,CAACE,kBAAkB,iBACpCzE,OAAA,CAAAE,SAAA;gBAAA+N,QAAA,gBACEjO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxBrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEhE,kBAAkB,CAACG,aAAc;oBACxCyK,QAAQ,EAAGC,CAAC,IAAK5K,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE9D,aAAa,EAAE0K,CAAC,CAACE,MAAM,CAAC/G;oBAAM,CAAC,CAAC;kBAAE;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvBrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEhE,kBAAkB,CAACI,iBAAkB;oBAC5CwK,QAAQ,EAAGC,CAAC,IAAK5K,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE7D,iBAAiB,EAAEyK,CAAC,CAACE,MAAM,CAAC/G;oBAAM,CAAC,CAAC;kBAAE;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrO,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAO;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtBrO,OAAA;oBACE+M,IAAI,EAAC,MAAM;oBACXxE,KAAK,EAAEhE,kBAAkB,CAACK,gBAAiB;oBAC3CuK,QAAQ,EAAGC,CAAC,IAAK5K,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE5D,gBAAgB,EAAEwK,CAAC,CAACE,MAAM,CAAC/G;oBAAM,CAAC,CAAC;kBAAE;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrO,OAAA;YAAKgO,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjO,OAAA;cAAAiO,QAAA,EAAI;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CrO,OAAA;cAAKgO,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACtB,IAAK;kBAC1C0L,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEtB,IAAI,EAAE2L,CAAC,CAACE,MAAM,CAAC/G;oBAAM;kBACzE,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACrB,YAAa;kBAClDyL,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAErB,YAAY,EAAE0L,CAAC,CAACE,MAAM,CAAC/G;oBAAM;kBACjF,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACC,QAAS;kBAC9CmK,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEC,QAAQ,EAAEoK,CAAC,CAACE,MAAM,CAAC/G;oBAAM;kBAC7E,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACE,eAAgB;kBACrDkK,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEE,eAAe,EAAEmK,CAAC,CAACE,MAAM,CAAC/G;oBAAM;kBACpF,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjO,OAAA;kBAAAiO,QAAA,EAAO;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDrO,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACK,aAAc;kBAChCiK,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtD,aAAa,EAAEkK,CAAC,CAACE,MAAM,CAAC/G;kBAAM,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDrO,OAAA;kBAAKgO,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BjO,OAAA;oBAAAiO,QAAA,gBACEjO,OAAA;sBACE+M,IAAI,EAAC,OAAO;sBACZtJ,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,MAAM;sBACZsH,OAAO,EAAEhL,UAAU,CAACM,gBAAgB,KAAK,IAAK;sBAC9CgK,QAAQ,EAAEA,CAAA,KAAMrK,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAA+I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrO,OAAA;oBAAAiO,QAAA,gBACEjO,OAAA;sBACE+M,IAAI,EAAC,OAAO;sBACZtJ,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,OAAO;sBACbsH,OAAO,EAAEhL,UAAU,CAACM,gBAAgB,KAAK,KAAM;sBAC/CgK,QAAQ,EAAEA,CAAA,KAAMrK,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAA+I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLxJ,UAAU,CAACM,gBAAgB,iBAC1BnF,OAAA;gBAAKgO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAmC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDrO,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACO,cAAe;kBACjC+J,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEpD,cAAc,EAAEgK,CAAC,CAACE,MAAM,CAAC/G;kBAAM,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzErO,OAAA;kBAAKgO,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BjO,OAAA;oBAAAiO,QAAA,gBACEjO,OAAA;sBACE+M,IAAI,EAAC,OAAO;sBACZtJ,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,MAAM;sBACZsH,OAAO,EAAEhL,UAAU,CAACQ,4BAA4B,KAAK,IAAK;sBAC1D8J,QAAQ,EAAEA,CAAA,KAAMrK,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrO,OAAA;oBAAAiO,QAAA,gBACEjO,OAAA;sBACE+M,IAAI,EAAC,OAAO;sBACZtJ,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,OAAO;sBACbsH,OAAO,EAAEhL,UAAU,CAACQ,4BAA4B,KAAK,KAAM;sBAC3D8J,QAAQ,EAAEA,CAAA,KAAMrK,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLxJ,UAAU,CAACQ,4BAA4B,iBACtCrF,OAAA;gBAAKgO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjO,OAAA;kBAAAiO,QAAA,EAAO;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnCrO,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACS,eAAgB;kBAClC6J,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAElD,eAAe,EAAE8J,CAAC,CAACE,MAAM,CAAC/G;kBAAM,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDrO,OAAA;gBAAKgO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjO,OAAA;kBAAAiO,QAAA,EAAO;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CrO,OAAA;kBACE+M,IAAI,EAAC,MAAM;kBACXxE,KAAK,EAAE1D,UAAU,CAACU,WAAY;kBAC9B4J,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjD,WAAW,EAAE6J,CAAC,CAACE,MAAM,CAAC/G;kBAAM,CAAC,CAAC;gBAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBrO,OAAA;YAAKgO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAwD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvErO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,OAAO;oBACZtJ,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,MAAM;oBACZsH,OAAO,EAAEtI,eAAe,CAACE,eAAe,KAAK,IAAK;oBAClD0H,QAAQ,EAAEA,CAAA,KAAM3H,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,OAAO;oBACZtJ,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,OAAO;oBACbsH,OAAO,EAAEtI,eAAe,CAACE,eAAe,KAAK,KAAM;oBACnD0H,QAAQ,EAAEA,CAAA,KAAM3H,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL9G,eAAe,CAACE,eAAe,iBAC9BzH,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjO,OAAA;gBAAAiO,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCrO,OAAA;gBACEuI,KAAK,EAAEhB,eAAe,CAACG,OAAQ;gBAC/ByH,QAAQ,EAAGC,CAAC,IAAK5H,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEd,OAAO,EAAE0H,CAAC,CAACE,MAAM,CAAC/G;gBAAM,CAAC,CAAC;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DrO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,OAAO;oBACZtJ,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,MAAM;oBACZsH,OAAO,EAAEtI,eAAe,CAACI,gBAAgB,KAAK,IAAK;oBACnDwH,QAAQ,EAAEA,CAAA,KAAM3H,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,OAAO;oBACZtJ,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,OAAO;oBACbsH,OAAO,EAAEtI,eAAe,CAACI,gBAAgB,KAAK,KAAM;oBACpDwH,QAAQ,EAAEA,CAAA,KAAM3H,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL9G,eAAe,CAACI,gBAAgB,iBAC/B3H,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjO,OAAA;gBAAAiO,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCrO,OAAA;gBACEuI,KAAK,EAAEhB,eAAe,CAACK,iBAAkB;gBACzCuH,QAAQ,EAAGC,CAAC,IAAK5H,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEZ,iBAAiB,EAAEwH,CAAC,CAACE,MAAM,CAAC/G;gBAAM,CAAC,CAAC;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjO,OAAA;YAAAiO,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBrO,OAAA;YAAKgO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjO,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCjO,OAAA;gBAAKgO,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BjO,OAAA;kBAAAiO,QAAA,eAAGjO,OAAA;oBAAAiO,QAAA,EAAQ;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrO,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCjO,OAAA;gBAAAiO,QAAA,gBACEjO,OAAA;kBACE+M,IAAI,EAAC,UAAU;kBACf8C,OAAO,EAAEhI,WAAW,CAACE,uBAAwB;kBAC7CoH,QAAQ,EAAGC,CAAC,IAAKtH,cAAc,CAACU,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAET,uBAAuB,EAAEqH,CAAC,CAACE,MAAM,CAACO;kBAAQ,CAAC,CAAC;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,mJAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzErO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,OAAO;oBACZtJ,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,MAAM;oBACZsH,OAAO,EAAEhI,WAAW,CAACG,mBAAmB,KAAK,IAAK;oBAClDmH,QAAQ,EAAEA,CAAA,KAAMrH,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrO,OAAA;kBAAAiO,QAAA,gBACEjO,OAAA;oBACE+M,IAAI,EAAC,OAAO;oBACZtJ,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,OAAO;oBACbsH,OAAO,EAAEhI,WAAW,CAACG,mBAAmB,KAAK,KAAM;oBACnDmH,QAAQ,EAAEA,CAAA,KAAMrH,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELxG,WAAW,CAACG,mBAAmB,iBAC9BhI,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjO,OAAA;gBAAAiO,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCrO,OAAA;gBACEuI,KAAK,EAAEV,WAAW,CAACI,sBAAuB;gBAC1CkH,QAAQ,EAAGC,CAAC,IAAKtH,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEP,sBAAsB,EAAEmH,CAAC,CAACE,MAAM,CAAC/G;gBAAM,CAAC,CAAC;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpBrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEV,WAAW,CAACK,KAAM;gBACzBiH,QAAQ,EAAGC,CAAC,IAAKtH,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEN,KAAK,EAAEkH,CAAC,CAACE,MAAM,CAAC/G;gBAAM,CAAC,CAAC;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrO,OAAA;cAAKgO,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjO,OAAA;gBAAAiO,QAAA,EAAO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnBrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXxE,KAAK,EAAEV,WAAW,CAACM,IAAK;gBACxBgH,QAAQ,EAAGC,CAAC,IAAKtH,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEL,IAAI,EAAEiH,CAAC,CAACE,MAAM,CAAC/G;gBAAM,CAAC,CAAC;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrO,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjO,OAAA;gBAAAiO,QAAA,EAAO;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCrO,OAAA;gBACE+M,IAAI,EAAC,MAAM;gBACXqD,WAAW,EAAC,kCAAkC;gBAC9C7H,KAAK,EAAEV,WAAW,CAACO,kBAAmB;gBACtC+G,QAAQ,EAAGC,CAAC,IAAKtH,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEJ,kBAAkB,EAAEgH,CAAC,CAACE,MAAM,CAAC/G;gBAAM,CAAC,CAAC;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrO,OAAA;cAAKgO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCjO,OAAA;gBAAKgO,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BjO,OAAA;kBAAAiO,QAAA,eAAGjO,OAAA;oBAAAiO,QAAA,EAAI;kBAA0I;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAOrO,OAAA;UAAAiO,QAAA,GAAK,OAAK,EAAC7G,WAAW;QAAA;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAGA,CAAA,KAAM;IACvB/P,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACER,OAAA;IAAKgO,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BjO,OAAA;MAAKgO,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjO,OAAA;QAAAiO,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCrO,OAAA;QAAKgO,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BjO,OAAA;UACEgO,SAAS,EAAC,eAAe;UACzBsC,KAAK,EAAE;YAAE9B,KAAK,EAAE,GAAIpH,WAAW,GAAGE,UAAU,GAAI,GAAG;UAAI;QAAE;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrO,OAAA;QAAAiO,QAAA,GAAG,OAAK,EAAC7G,WAAW,EAAC,MAAI,EAACE,UAAU,EAAC,IAAE,EAAC6C,YAAY,CAAC/C,WAAW,CAAC;MAAA;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAIL,CAAChO,cAAc,IAAIE,YAAY,kBAC9BP,OAAA;MAAKgO,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BjO,OAAA;QAAKgO,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjO,OAAA;UAAKgO,SAAS,EAAE3N,cAAc,GAAG,iBAAiB,GAAG,eAAgB;UAAA4N,QAAA,EAClE5N,cAAc,IAAIE;QAAY;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNrO,OAAA;UAAQgO,SAAS,EAAC,aAAa;UAAC8B,OAAO,EAAEO,UAAW;UAAApC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDrO,OAAA;MAAMuQ,QAAQ,EAAEjF,YAAa;MAAA2C,QAAA,EAC1BF,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEPrO,OAAA;MAAKgO,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BjO,OAAA;QAAKgO,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtB7G,WAAW,GAAG,CAAC,iBACdpH,OAAA;UAAQ+M,IAAI,EAAC,QAAQ;UAAC+C,OAAO,EAAErF,QAAS;UAACuD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENrO,OAAA;QAAKgO,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBjO,OAAA;UAAQ+M,IAAI,EAAC,QAAQ;UAAC+C,OAAO,EAAEjC,eAAgB;UAACG,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENrO,OAAA;QAAKgO,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB7G,WAAW,GAAGE,UAAU,gBACvBtH,OAAA;UAAQ+M,IAAI,EAAC,QAAQ;UAAC+C,OAAO,EAAExF,QAAS;UAAC0D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETrO,OAAA;UAAQ+M,IAAI,EAAC,QAAQ;UAAC+C,OAAO,EAAEA,CAAA,KAAM;YACnC,MAAMU,IAAI,GAAGrD,QAAQ,CAACsD,aAAa,CAAC,MAAM,CAAC;YAC3C,IAAID,IAAI,EAAE;cACRA,IAAI,CAACE,aAAa,CAAC,CAAC;YACtB;UACF,CAAE;UAAC1C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5N,eAAe,iBACdT,OAAA;MAAKgO,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BjO,OAAA;QAAKgO,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCjO,OAAA;UAAAiO,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BrO,OAAA;UAAKgO,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BtN;QAAe;UAAAuN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNrO,OAAA;UAAKgO,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BjO,OAAA;YACE8P,OAAO,EAAEA,CAAA,KAAMpP,kBAAkB,CAAC,KAAK,CAAE;YACzCsN,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrO,OAAA;YACE8P,OAAO,EAAEA,CAAA,KAAM;cACba,SAAS,CAACC,SAAS,CAACC,SAAS,CAAClQ,eAAe,CAAC;cAC9CiJ,KAAK,CAAC,2BAA2B,CAAC;YACpC,CAAE;YACFoE,SAAS,EAAC,UAAU;YAAAC,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjO,EAAA,CArwDID,cAAwB;EAAA,QAMVL,SAAS;AAAA;AAAAgR,EAAA,GANvB3Q,cAAwB;AAuwD9B,eAAeA,cAAc;AAAC,IAAA2Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}