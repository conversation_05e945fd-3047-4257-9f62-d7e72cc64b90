{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OnboardingForm = () => {\n  _s();\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [errorMessage, setErrorMessage] = useState(null);\n  const {\n    token\n  } = useParams();\n  const [personalDetails, setPersonalDetails] = useState({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n  const [languageSkills, setLanguageSkills] = useState([{\n    language: '',\n    speak: false,\n    read: false,\n    write: false\n  }]);\n  const [documentDetails, setDocumentDetails] = useState({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n  const [familyMembers, setFamilyMembers] = useState([{\n    name: '',\n    relationship: '',\n    dateOfBirth: '',\n    qualification: '',\n    occupation: '',\n    organisationAndPosition: ''\n  }]);\n  const [physicalDetails, setPhysicalDetails] = useState({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n  const [previousEmployment, setPreviousEmployment] = useState({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n  const [references, setReferences] = useState({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n  const [workExperience, setWorkExperience] = useState([{\n    employerName: '',\n    address: '',\n    duration: '',\n    fromDate: '',\n    toDate: '',\n    lastPosition: '',\n    designation: '',\n    natureOfDuties: '',\n    immediateSuperior: '',\n    grossEmoluments: '',\n    lastDrawn: '',\n    basicSalary: '',\n    fixedSalary: '',\n    variableSalary: '',\n    grossSalary: ''\n  }]);\n  const [education, setEducation] = useState([{\n    examination: '',\n    specialisation: '',\n    schoolCollege: '',\n    university: '',\n    fullTimePartTime: '',\n    duration: '',\n    monthYearPassing: '',\n    gradeMarks: '',\n    distinctions: '',\n    certificateFile: null\n  }]);\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n  const handlePersonalDetailsChange = (field, value) => {\n    setPersonalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleDocumentDetailsChange = (field, value) => {\n    setDocumentDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handlePhysicalDetailsChange = (field, value) => {\n    setPhysicalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleFileUpload = (file, section, field, index) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = {\n          ...newEducation[index],\n          [field]: file\n        };\n        return newEducation;\n      });\n    }\n  };\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, {\n      language: '',\n      speak: false,\n      read: false,\n      write: false\n    }]);\n  };\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, {\n      name: '',\n      relationship: '',\n      dateOfBirth: '',\n      qualification: '',\n      occupation: '',\n      organisationAndPosition: ''\n    }]);\n  };\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n  const getStepTitle = step => {\n    const titles = ['', 'PERSONAL DETAILS', 'FAMILY DATA', 'HEALTH DATA', 'EDUCATION DETAILS', 'WORK HISTORY DATA', 'GENERAL DATA', 'CRIMINAL RECORDS', 'DECLARATION FORM'];\n    return titles[step] || '';\n  };\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    var _personalDetails$firs, _personalDetails$last, _personalDetails$date, _personalDetails$gend, _personalDetails$nati, _personalDetails$cont, _personalDetails$emer;\n    const requiredFields = [(_personalDetails$firs = personalDetails.firstName) === null || _personalDetails$firs === void 0 ? void 0 : _personalDetails$firs.trim(), (_personalDetails$last = personalDetails.lastName) === null || _personalDetails$last === void 0 ? void 0 : _personalDetails$last.trim(), (_personalDetails$date = personalDetails.dateOfBirth) === null || _personalDetails$date === void 0 ? void 0 : _personalDetails$date.trim(), (_personalDetails$gend = personalDetails.gender) === null || _personalDetails$gend === void 0 ? void 0 : _personalDetails$gend.trim(), (_personalDetails$nati = personalDetails.nationality) === null || _personalDetails$nati === void 0 ? void 0 : _personalDetails$nati.trim(), (_personalDetails$cont = personalDetails.contactNumber) === null || _personalDetails$cont === void 0 ? void 0 : _personalDetails$cont.trim(), (_personalDetails$emer = personalDetails.emergencyContactNo) === null || _personalDetails$emer === void 0 ? void 0 : _personalDetails$emer.trim()];\n    return requiredFields.every(Boolean);\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n    // Collect all form data into a single object\n    const allFormData = {\n      personalDetails,\n      languageSkills,\n      documentDetails,\n      familyMembers,\n      physicalDetails,\n      previousEmployment,\n      references,\n      workExperience,\n      education,\n      criminalRecords,\n      declaration\n    };\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      // Send as { form_data: ... }\n      await axios.post(url, {\n        form_data: allFormData\n      });\n      setSuccessMessage(\"Form submitted successfully!\");\n      setErrorMessage(null);\n    } catch (error) {\n      setErrorMessage(\"Failed to submit the form. Please try again later.\");\n      setSuccessMessage(null);\n    }\n  };\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"PERSONAL DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"photo-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"photo-placeholder\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"photo-frame\",\n                children: personalDetails.passportPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(personalDetails.passportPhoto),\n                  alt: \"Passport Photo\",\n                  className: \"uploaded-photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"placeholder-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"40\",\n                    height: \"40\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"7\",\n                      r: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"photo-label\",\n                children: \"Uploaded Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"upload-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"upload-text\",\n                  children: \"Please upload a image less than 100kb\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"upload-button\",\n                  children: [\"Choose a file\", /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"image/*\",\n                    required: true,\n                    onChange: e => {\n                      var _e$target$files;\n                      return handleFileUpload(((_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0]) || null, 'personal', 'passportPhoto');\n                    },\n                    className: \"hidden-file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), personalDetails.passportPhoto && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"file-info\",\n                  children: personalDetails.passportPhoto.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"First Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.firstName,\n                onChange: e => handlePersonalDetailsChange('firstName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Middle Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.middleName,\n                onChange: e => handlePersonalDetailsChange('middleName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Last Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.lastName,\n                onChange: e => handlePersonalDetailsChange('lastName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date of Birth *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                required: true,\n                value: personalDetails.dateOfBirth,\n                onChange: e => handlePersonalDetailsChange('dateOfBirth', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Gender *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                required: true,\n                value: personalDetails.gender,\n                onChange: e => handlePersonalDetailsChange('gender', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Birth Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.birthPlace,\n                onChange: e => handlePersonalDetailsChange('birthPlace', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Age\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: personalDetails.age,\n                onChange: e => handlePersonalDetailsChange('age', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marriage Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: personalDetails.marriageDate,\n                onChange: e => handlePersonalDetailsChange('marriageDate', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marital Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: personalDetails.maritalStatus,\n                onChange: e => handlePersonalDetailsChange('maritalStatus', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Single\",\n                  children: \"Single\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Married\",\n                  children: \"Married\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Divorced\",\n                  children: \"Divorced\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Widowed\",\n                  children: \"Widowed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Nationality *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.nationality,\n                onChange: e => handlePersonalDetailsChange('nationality', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Religion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.religion,\n                onChange: e => handlePersonalDetailsChange('religion', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Native State\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.nativeState,\n                onChange: e => handlePersonalDetailsChange('nativeState', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Contact Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.contactNumber,\n                onChange: e => handlePersonalDetailsChange('contactNumber', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Emergency Contact No. *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.emergencyContactNo,\n                onChange: e => handlePersonalDetailsChange('emergencyContactNo', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"State of Domicile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.stateOfDomicile,\n                onChange: e => handlePersonalDetailsChange('stateOfDomicile', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                required: true,\n                value: personalDetails.email,\n                onChange: e => handlePersonalDetailsChange('email', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.presentAddress,\n                onChange: e => handlePersonalDetailsChange('presentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.permanentAddress,\n                onChange: e => handlePersonalDetailsChange('permanentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.presentPin,\n                onChange: e => handlePersonalDetailsChange('presentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.permanentPin,\n                onChange: e => handlePersonalDetailsChange('permanentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Document Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.passportNo,\n                  onChange: e => handleDocumentDetailsChange('passportNo', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Issue Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.passportIssueDate,\n                  onChange: e => handleDocumentDetailsChange('passportIssueDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Upto Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.validUptoDate,\n                  onChange: e => handleDocumentDetailsChange('validUptoDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Country of Issue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.countryOfIssue,\n                  onChange: e => handleDocumentDetailsChange('countryOfIssue', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Visa Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.validVisaDetails,\n                  onChange: e => handleDocumentDetailsChange('validVisaDetails', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.panNumber,\n                  onChange: e => handleDocumentDetailsChange('panNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files2;\n                    return handleFileUpload(((_e$target$files2 = e.target.files) === null || _e$target$files2 === void 0 ? void 0 : _e$target$files2[0]) || null, 'document', 'panFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this), documentDetails.panFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.panFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.aadharNumber,\n                  onChange: e => handleDocumentDetailsChange('aadharNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files3;\n                    return handleFileUpload(((_e$target$files3 = e.target.files) === null || _e$target$files3 === void 0 ? void 0 : _e$target$files3[0]) || null, 'document', 'aadharFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 19\n                }, this), documentDetails.aadharFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.aadharFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files4;\n                    return handleFileUpload(((_e$target$files4 = e.target.files) === null || _e$target$files4 === void 0 ? void 0 : _e$target$files4[0]) || null, 'document', 'passportFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 19\n                }, this), documentDetails.passportFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.passportFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Language Known\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), languageSkills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"language-skill-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: skill.language,\n                  onChange: e => {\n                    const newSkills = [...languageSkills];\n                    newSkills[index].language = e.target.value;\n                    setLanguageSkills(newSkills);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.speak,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].speak = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 23\n                  }, this), \"Speak\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.read,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].read = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 23\n                  }, this), \"Read\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.write,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].write = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 23\n                  }, this), \"Write\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addLanguageSkill,\n              className: \"btn-add\",\n              children: \"Add Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"FAMILY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Family Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 15\n            }, this), familyMembers.map((member, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"family-member-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 825,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.name,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].name = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Relationship\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.relationship,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].relationship = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date of Birth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: member.dateOfBirth,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].dateOfBirth = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Qualification\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.qualification,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].qualification = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Occupation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.occupation,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].occupation = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Organisation and Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.organisationAndPosition,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].organisationAndPosition = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addFamilyMember,\n              className: \"btn-add\",\n              children: \"Add Family Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"HEALTH DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Physical Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 912,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Height\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.height,\n                  onChange: e => handlePhysicalDetailsChange('height', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 916,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Weight\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.weight,\n                  onChange: e => handlePhysicalDetailsChange('weight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Blood Group\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: physicalDetails.bloodGroup,\n                  onChange: e => handlePhysicalDetailsChange('bloodGroup', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Blood Group\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A+\",\n                    children: \"A+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A-\",\n                    children: \"A-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B+\",\n                    children: \"B+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 939,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B-\",\n                    children: \"B-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB+\",\n                    children: \"AB+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB-\",\n                    children: \"AB-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O+\",\n                    children: \"O+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O-\",\n                    children: \"O-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightRight,\n                  onChange: e => handlePhysicalDetailsChange('eyesightRight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightLeft,\n                  onChange: e => handlePhysicalDetailsChange('eyesightLeft', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Physical Disability\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.physicalDisability,\n                  onChange: e => handlePhysicalDetailsChange('physicalDisability', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Identification Mark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.identificationMark,\n                  onChange: e => handlePhysicalDetailsChange('identificationMark', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 973,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"EDUCATION DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 13\n          }, this), education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"education-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Education \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Examination Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.examination,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].examination = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Specialisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1006,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.specialisation,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].specialisation = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"School/College/Institution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1018,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.schoolCollege,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].schoolCollege = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"University/Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.university,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].university = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Full Time/Part Time/Correspondence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: edu.fullTimePartTime,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].fullTimePartTime = e.target.value;\n                    setEducation(newEducation);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1051,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time\",\n                    children: \"Full Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time\",\n                    children: \"Part Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Correspondence\",\n                    children: \"Correspondence\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1054,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Duration of Course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.duration,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].duration = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Month & Year of Passing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.monthYearPassing,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].monthYearPassing = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1071,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Grade/% Marks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.gradeMarks,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].gradeMarks = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1083,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Distinctions/Scholarships/Prizes Won\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: edu.distinctions,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].distinctions = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Certificate/Document Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files5;\n                    return handleFileUpload(((_e$target$files5 = e.target.files) === null || _e$target$files5 === void 0 ? void 0 : _e$target$files5[0]) || null, 'education', 'certificateFile', index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1106,\n                  columnNumber: 21\n                }, this), edu.certificateFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: edu.certificateFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1113,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addEducation,\n            className: \"btn-add\",\n            children: \"Add Education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 986,\n          columnNumber: 11\n        }, this);\n      case 5:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"WORK HISTORY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1129,\n            columnNumber: 13\n          }, this), workExperience.map((experience, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"work-experience-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Work Experience \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Employer's Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.employerName,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].employerName = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.address,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].address = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1149,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"From Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.fromDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fromDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1160,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"To Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.toDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].toDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1172,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Position Held\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.lastPosition,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastPosition = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1184,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Designation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.designation,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].designation = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1196,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nature of Duties\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.natureOfDuties,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].natureOfDuties = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1208,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Name & Designation of Immediate Superior\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.immediateSuperior,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].immediateSuperior = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Emoluments (Rs. Per month at joining)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossEmoluments,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossEmoluments = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Drawn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.lastDrawn,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastDrawn = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1243,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Basic Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.basicSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].basicSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1255,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fixed Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.fixedSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fixedSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1267,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Variable Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1278,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.variableSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].variableSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1279,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addWorkExperience,\n            className: \"btn-add\",\n            children: \"Add Work Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1128,\n          columnNumber: 11\n        }, this);\n      case 6:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"GENERAL DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Previous Interview with MH Group\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Have you ever been interviewed by any of the MH Group of Companies?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1319,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"true\",\n                      checked: previousEmployment.hasBeenInterviewed === true,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1322,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1321,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"false\",\n                      checked: previousEmployment.hasBeenInterviewed === false,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1332,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1331,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1320,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1318,\n                columnNumber: 17\n              }, this), previousEmployment.hasBeenInterviewed && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date/Year\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewDate,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewDate: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1347,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1354,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewPosition,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewPosition: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1355,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1353,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Company\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1362,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewCompany,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewCompany: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1363,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"References & Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relatives/Acquaintance in MH Group - Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.name,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      name: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1379,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1377,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relationship\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.relationship,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      relationship: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1400,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.position,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      position: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1401,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Company & Phone No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1411,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.companyAndPhone,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      companyAndPhone: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1412,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"How did you come to know of this position?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1422,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.howDidYouKnow,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    howDidYouKnow: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1423,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Are you engaged in any personal business?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"true\",\n                      checked: references.personalBusiness === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1432,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1431,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"false\",\n                      checked: references.personalBusiness === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1442,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1441,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1430,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1428,\n                columnNumber: 17\n              }, this), references.personalBusiness && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, indicate nature of business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.businessNature,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    businessNature: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1456,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Do you have any contract/bond with your previous employer?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1464,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"true\",\n                      checked: references.contractWithPreviousEmployer === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1467,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1466,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"false\",\n                      checked: references.contractWithPreviousEmployer === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1477,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1476,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1465,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 17\n              }, this), references.contractWithPreviousEmployer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, Give Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.contractDetails,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    contractDetails: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1491,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1489,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If selected, when can you join?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: references.whenCanJoin,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    whenCanJoin: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1500,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1498,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1312,\n          columnNumber: 11\n        }, this);\n      case 7:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"CRIMINAL RECORDS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been involved in any criminal proceedings?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1518,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenInvolved === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1521,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1520,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenInvolved === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1531,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1530,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1519,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1517,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenInvolved && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.details,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  details: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1545,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1543,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been convicted of any offence?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenConvicted === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1556,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1555,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenConvicted === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1566,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1565,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1554,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1552,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenConvicted && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1579,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.convictionDetails,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  convictionDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1580,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1578,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1516,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1513,\n          columnNumber: 11\n        }, this);\n      case 8:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"DECLARATION FORM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1593,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-text\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"I hereby declare that:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1598,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1598,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1597,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1596,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: declaration.notConnectedToDirectors,\n                  onChange: e => setDeclaration(prev => ({\n                    ...prev,\n                    notConnectedToDirectors: e.target.checked\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1604,\n                  columnNumber: 19\n                }, this), \"I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1603,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1602,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"OR I am a partner or relative of a Director of the Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1614,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"true\",\n                    checked: declaration.isPartnerOrRelative === true,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1617,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1616,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"false\",\n                    checked: declaration.isPartnerOrRelative === false,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1627,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1626,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1615,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1613,\n              columnNumber: 15\n            }, this), declaration.isPartnerOrRelative && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, provide details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1641,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: declaration.partnerRelativeDetails,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  partnerRelativeDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1642,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1640,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1650,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: declaration.place,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  place: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1651,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1649,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1659,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: declaration.date,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  date: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1660,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1658,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Applicant's Signature\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Type your full name as signature\",\n                value: declaration.applicantSignature,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  applicantSignature: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1669,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1667,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-note\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"em\", {\n                    children: \"Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1679,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1679,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1678,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1677,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1595,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1592,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Step \", currentStep]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1687,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"onboarding-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Employee Onboarding Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1700,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-fill\",\n          style: {\n            width: `${currentStep / totalSteps * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1702,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1701,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Step \", currentStep, \" of \", totalSteps, \": \", getStepTitle(currentStep)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1707,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1699,\n      columnNumber: 7\n    }, this), (successMessage || errorMessage) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: successMessage ? \"success-message\" : \"error-message\",\n          children: successMessage || errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1715,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1718,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1714,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1713,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: renderStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1723,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-navigation\",\n      children: [currentStep > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: prevStep,\n        className: \"btn-secondary\",\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1729,\n        columnNumber: 11\n      }, this), currentStep < totalSteps ? /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: nextStep,\n        className: \"btn-primary\",\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1734,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: () => {\n          const form = document.querySelector('form');\n          if (form) {\n            form.requestSubmit();\n          }\n        },\n        className: \"btn-primary\",\n        children: \"Submit Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1738,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1727,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1698,\n    columnNumber: 5\n  }, this);\n};\n_s(OnboardingForm, \"Z6RflfP5tY5nZwm/YuMoABzC3Ps=\", false, function () {\n  return [useParams];\n});\n_c = OnboardingForm;\nexport default OnboardingForm;\nvar _c;\n$RefreshReg$(_c, \"OnboardingForm\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OnboardingForm", "_s", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "token", "personalDetails", "setPersonalDetails", "firstName", "middleName", "lastName", "dateOfBirth", "gender", "birthPlace", "age", "marriageDate", "maritalStatus", "nationality", "religion", "nativeState", "contactNumber", "emergencyContactNo", "stateOfDomicile", "email", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "present<PERSON>in", "permanent<PERSON>in", "passportPhoto", "languageSkills", "setLanguageSkills", "language", "speak", "read", "write", "documentDetails", "setDocumentDetails", "passportNo", "passportIssueDate", "validUptoDate", "countryOfIssue", "validVisaDetails", "panNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passportFile", "panFile", "a<PERSON>har<PERSON><PERSON>", "family<PERSON><PERSON>bers", "setFamilyMembers", "name", "relationship", "qualification", "occupation", "organisationAndPosition", "physicalDetails", "setPhysicalDetails", "height", "weight", "bloodGroup", "eyesightRight", "eyesightLeft", "physicalDisability", "identificationMark", "previousEmployment", "setPreviousEmployment", "hasBeenInterviewed", "interviewDate", "interviewPosition", "interviewCompany", "references", "setReferences", "relativesInCompany", "position", "companyAndPhone", "howDidYouKnow", "personalBusiness", "businessNature", "contractWithPreviousEmployer", "contractDetails", "whenCanJoin", "workExperience", "setWorkExperience", "employerName", "address", "duration", "fromDate", "toDate", "lastPosition", "designation", "natureOfDuties", "immediateSuperior", "grossEmoluments", "lastDrawn", "basicSalary", "fixedSalary", "variableSalary", "grossSalary", "education", "setEducation", "examination", "specialisation", "schoolCollege", "university", "fullTimePartTime", "monthYearPassing", "gradeMarks", "distinctions", "certificateFile", "currentStep", "setCurrentStep", "totalSteps", "criminalRecords", "setCriminalRecords", "hasBeenInvolved", "details", "hasBeenConvicted", "convictionDetails", "declaration", "setDeclaration", "notConnectedToDirectors", "isPartnerOrRelative", "partnerRelativeDetails", "place", "date", "applicantSignature", "handlePersonalDetailsChange", "field", "value", "prev", "handleDocumentDetailsChange", "handlePhysicalDetailsChange", "handleFileUpload", "file", "section", "index", "maxSize", "size", "alert", "undefined", "newEducation", "addLanguageSkill", "addFamilyMember", "addWorkExperience", "addEducation", "getStepTitle", "step", "titles", "nextStep", "console", "log", "prevStep", "validateRequired<PERSON><PERSON>s", "_personalDetails$firs", "_personalDetails$last", "_personalDetails$date", "_personalDetails$gend", "_personalDetails$nati", "_personalDetails$cont", "_personalDetails$emer", "requiredFields", "trim", "every", "Boolean", "handleSubmit", "event", "preventDefault", "allFormData", "url", "post", "form_data", "error", "renderStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "URL", "createObjectURL", "alt", "width", "viewBox", "fill", "stroke", "strokeWidth", "d", "cx", "cy", "r", "type", "accept", "required", "onChange", "e", "_e$target$files", "target", "files", "_e$target$files2", "_e$target$files3", "_e$target$files4", "map", "skill", "newSkills", "checked", "onClick", "member", "newMembers", "edu", "_e$target$files5", "experience", "newExperience", "placeholder", "closeModal", "style", "onSubmit", "form", "document", "querySelector", "requestSubmit", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\n\ninterface PersonalDetails {\n  firstName: string;\n  middleName: string;\n  lastName: string;\n  dateOfBirth: string;\n  gender: string;\n  birthPlace: string;\n  age: string;\n  marriageDate: string;\n  maritalStatus: string;\n  nationality: string;\n  religion: string;\n  nativeState: string;\n  contactNumber: string;\n  emergencyContactNo: string;\n  stateOfDomicile: string;\n  email: string;\n  presentAddress: string;\n  permanentAddress: string;\n  presentPin: string;\n  permanentPin: string;\n  passportPhoto: File | null;\n}\n\ninterface LanguageSkills {\n  language: string;\n  speak: boolean;\n  read: boolean;\n  write: boolean;\n}\n\ninterface DocumentDetails {\n  passportNo: string;\n  passportIssueDate: string;\n  validUptoDate: string;\n  countryOfIssue: string;\n  validVisaDetails: string;\n  panNumber: string;\n  aadharNumber: string;\n  passportFile: File | null;\n  panFile: File | null;\n  aadharFile: File | null;\n}\n\ninterface FamilyMember {\n  name: string;\n  relationship: string;\n  dateOfBirth: string;\n  qualification: string;\n  occupation: string;\n  organisationAndPosition: string;\n}\n\ninterface PhysicalDetails {\n  height: string;\n  weight: string;\n  bloodGroup: string;\n  eyesightRight: string;\n  eyesightLeft: string;\n  physicalDisability: string;\n  identificationMark: string;\n}\n\ninterface PreviousEmployment {\n  hasBeenInterviewed: boolean;\n  interviewDate: string;\n  interviewPosition: string;\n  interviewCompany: string;\n}\n\ninterface References {\n  relativesInCompany: {\n    name: string;\n    relationship: string;\n    position: string;\n    companyAndPhone: string;\n  };\n  howDidYouKnow: string;\n  personalBusiness: boolean;\n  businessNature: string;\n  contractWithPreviousEmployer: boolean;\n  contractDetails: string;\n  whenCanJoin: string;\n}\n\ninterface WorkExperience {\n  employerName: string;\n  address: string;\n  duration: string;\n  fromDate: string;\n  toDate: string;\n  lastPosition: string;\n  designation: string;\n  natureOfDuties: string;\n  immediateSuperior: string;\n  grossEmoluments: string;\n  lastDrawn: string;\n  basicSalary: string;\n  fixedSalary: string;\n  variableSalary: string;\n  grossSalary: string;\n}\n\ninterface Education {\n  examination: string;\n  specialisation: string;\n  schoolCollege: string;\n  university: string;\n  fullTimePartTime: string;\n  duration: string;\n  monthYearPassing: string;\n  gradeMarks: string;\n  distinctions: string;\n  certificateFile: File | null;\n}\n\nconst OnboardingForm: React.FC = () => {\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [errorMessage, setErrorMessage] = useState<string | null>(null);\n  const { token } = useParams<{ token: string }>();\n  const [personalDetails, setPersonalDetails] = useState<PersonalDetails>({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n\n  const [languageSkills, setLanguageSkills] = useState<LanguageSkills[]>([\n    { language: '', speak: false, read: false, write: false }\n  ]);\n\n  const [documentDetails, setDocumentDetails] = useState<DocumentDetails>({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n\n  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([\n    { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }\n  ]);\n\n  const [physicalDetails, setPhysicalDetails] = useState<PhysicalDetails>({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n\n  const [previousEmployment, setPreviousEmployment] = useState<PreviousEmployment>({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n\n  const [references, setReferences] = useState<References>({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n\n  const [workExperience, setWorkExperience] = useState<WorkExperience[]>([\n    {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }\n  ]);\n\n  const [education, setEducation] = useState<Education[]>([\n    {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }\n  ]);\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n\n  const handlePersonalDetailsChange = (field: keyof PersonalDetails, value: string) => {\n    setPersonalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleDocumentDetailsChange = (field: keyof DocumentDetails, value: string) => {\n    setDocumentDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handlePhysicalDetailsChange = (field: keyof PhysicalDetails, value: string) => {\n    setPhysicalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleFileUpload = (file: File | null, section: 'personal' | 'document' | 'education', field: string, index?: number) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = { ...newEducation[index], [field]: file };\n        return newEducation;\n      });\n    }\n  };\n\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, { language: '', speak: false, read: false, write: false }]);\n  };\n\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }]);\n  };\n\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n\n  const getStepTitle = (step: number) => {\n    const titles = [\n      '',\n      'PERSONAL DETAILS',\n      'FAMILY DATA',\n      'HEALTH DATA',\n      'EDUCATION DETAILS',\n      'WORK HISTORY DATA',\n      'GENERAL DATA',\n      'CRIMINAL RECORDS',\n      'DECLARATION FORM'\n    ];\n    return titles[step] || '';\n  };\n\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    const requiredFields = [\n      personalDetails.firstName?.trim(),\n      personalDetails.lastName?.trim(),\n      personalDetails.dateOfBirth?.trim(),\n      personalDetails.gender?.trim(),\n      personalDetails.nationality?.trim(),\n      personalDetails.contactNumber?.trim(),\n      personalDetails.emergencyContactNo?.trim(),\n    ];\n    return requiredFields.every(Boolean);\n  };\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n    // Collect all form data into a single object\n    const allFormData = {\n      personalDetails,\n      languageSkills,\n      documentDetails,\n      familyMembers,\n      physicalDetails,\n      previousEmployment,\n      references,\n      workExperience,\n      education,\n      criminalRecords,\n      declaration\n    };\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      // Send as { form_data: ... }\n      await axios.post(url, { form_data: allFormData });\n      setSuccessMessage(\"Form submitted successfully!\");\n      setErrorMessage(null);\n    } catch (error) {\n      setErrorMessage(\"Failed to submit the form. Please try again later.\");\n      setSuccessMessage(null);\n    }\n  };\n\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"form-section\">\n            <h3>PERSONAL DETAILS</h3>\n\n            {/* Passport Photo Upload Section */}\n            <div className=\"photo-upload-section\">\n              <div className=\"photo-placeholder\">\n                <div className=\"photo-frame\">\n                  {personalDetails.passportPhoto ? (\n                    <img\n                      src={URL.createObjectURL(personalDetails.passportPhoto)}\n                      alt=\"Passport Photo\"\n                      className=\"uploaded-photo\"\n                    />\n                  ) : (\n                    <div className=\"placeholder-icon\">\n                      <svg width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n                      </svg>\n                    </div>\n                  )}\n                </div>\n                <span className=\"photo-label\">Uploaded Photo</span>\n              </div>\n              <div className=\"upload-area\">\n                <div className=\"upload-content\">\n                  <p className=\"upload-text\">Please upload a image less than 100kb</p>\n                  <label className=\"upload-button\">\n                    Choose a file\n                    <input\n                      type=\"file\"\n                      accept=\"image/*\"\n                      required\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'personal', 'passportPhoto')}\n                      className=\"hidden-file-input\"\n                    />\n                  </label>\n                  {personalDetails.passportPhoto && (\n                    <p className=\"file-info\">{personalDetails.passportPhoto.name}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>First Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.firstName}\n                  onChange={(e) => handlePersonalDetailsChange('firstName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Middle Name</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.middleName}\n                  onChange={(e) => handlePersonalDetailsChange('middleName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Last Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.lastName}\n                  onChange={(e) => handlePersonalDetailsChange('lastName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Date of Birth *</label>\n                <input\n                  type=\"date\"\n                  required\n                  value={personalDetails.dateOfBirth}\n                  onChange={(e) => handlePersonalDetailsChange('dateOfBirth', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Gender *</label>\n                <select\n                  required\n                  value={personalDetails.gender}\n                  onChange={(e) => handlePersonalDetailsChange('gender', e.target.value)}\n                >\n                  <option value=\"\">Select Gender</option>\n                  <option value=\"Male\">Male</option>\n                  <option value=\"Female\">Female</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Birth Place</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.birthPlace}\n                  onChange={(e) => handlePersonalDetailsChange('birthPlace', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Age</label>\n                <input\n                  type=\"number\"\n                  value={personalDetails.age}\n                  onChange={(e) => handlePersonalDetailsChange('age', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marriage Date</label>\n                <input\n                  type=\"date\"\n                  value={personalDetails.marriageDate}\n                  onChange={(e) => handlePersonalDetailsChange('marriageDate', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marital Status</label>\n                <select\n                  value={personalDetails.maritalStatus}\n                  onChange={(e) => handlePersonalDetailsChange('maritalStatus', e.target.value)}\n                >\n                  <option value=\"\">Select Status</option>\n                  <option value=\"Single\">Single</option>\n                  <option value=\"Married\">Married</option>\n                  <option value=\"Divorced\">Divorced</option>\n                  <option value=\"Widowed\">Widowed</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Nationality *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.nationality}\n                  onChange={(e) => handlePersonalDetailsChange('nationality', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Religion</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.religion}\n                  onChange={(e) => handlePersonalDetailsChange('religion', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Native State</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.nativeState}\n                  onChange={(e) => handlePersonalDetailsChange('nativeState', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Contact Number *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.contactNumber}\n                  onChange={(e) => handlePersonalDetailsChange('contactNumber', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Emergency Contact No. *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.emergencyContactNo}\n                  onChange={(e) => handlePersonalDetailsChange('emergencyContactNo', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>State of Domicile</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.stateOfDomicile}\n                  onChange={(e) => handlePersonalDetailsChange('stateOfDomicile', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Email *</label>\n                <input\n                  type=\"email\"\n                  required\n                  value={personalDetails.email}\n                  onChange={(e) => handlePersonalDetailsChange('email', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Present Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.presentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('presentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Permanent Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.permanentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('permanentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Present PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.presentPin}\n                  onChange={(e) => handlePersonalDetailsChange('presentPin', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Permanent PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.permanentPin}\n                  onChange={(e) => handlePersonalDetailsChange('permanentPin', e.target.value)}\n                />\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Document Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Passport No.</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.passportNo}\n                    onChange={(e) => handleDocumentDetailsChange('passportNo', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Issue Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.passportIssueDate}\n                    onChange={(e) => handleDocumentDetailsChange('passportIssueDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Upto Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.validUptoDate}\n                    onChange={(e) => handleDocumentDetailsChange('validUptoDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Country of Issue</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.countryOfIssue}\n                    onChange={(e) => handleDocumentDetailsChange('countryOfIssue', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Visa Details</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.validVisaDetails}\n                    onChange={(e) => handleDocumentDetailsChange('validVisaDetails', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.panNumber}\n                    onChange={(e) => handleDocumentDetailsChange('panNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'panFile')}\n                  />\n                  {documentDetails.panFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.panFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.aadharNumber}\n                    onChange={(e) => handleDocumentDetailsChange('aadharNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'aadharFile')}\n                  />\n                  {documentDetails.aadharFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.aadharFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'passportFile')}\n                  />\n                  {documentDetails.passportFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.passportFile.name}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Language Known</h4>\n              {languageSkills.map((skill, index) => (\n                <div key={index} className=\"language-skill-row\">\n                  <div className=\"form-group\">\n                    <label>Language</label>\n                    <input\n                      type=\"text\"\n                      value={skill.language}\n                      onChange={(e) => {\n                        const newSkills = [...languageSkills];\n                        newSkills[index].language = e.target.value;\n                        setLanguageSkills(newSkills);\n                      }}\n                    />\n                  </div>\n                  <div className=\"checkbox-group\">\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.speak}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].speak = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Speak\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.read}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].read = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Read\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.write}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].write = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Write\n                    </label>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addLanguageSkill} className=\"btn-add\">\n                Add Language\n              </button>\n            </div>\n          </div>\n        );\n      case 2:\n        return (\n          <div className=\"form-section\">\n            <h3>FAMILY DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Family Members</h4>\n              {familyMembers.map((member, index) => (\n                <div key={index} className=\"family-member-section\">\n                  <div className=\"form-grid\">\n                    <div className=\"form-group\">\n                      <label>Name</label>\n                      <input\n                        type=\"text\"\n                        value={member.name}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].name = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Relationship</label>\n                      <input\n                        type=\"text\"\n                        value={member.relationship}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].relationship = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Date of Birth</label>\n                      <input\n                        type=\"date\"\n                        value={member.dateOfBirth}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].dateOfBirth = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Qualification</label>\n                      <input\n                        type=\"text\"\n                        value={member.qualification}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].qualification = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Occupation</label>\n                      <input\n                        type=\"text\"\n                        value={member.occupation}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].occupation = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Organisation and Position</label>\n                      <input\n                        type=\"text\"\n                        value={member.organisationAndPosition}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].organisationAndPosition = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addFamilyMember} className=\"btn-add\">\n                Add Family Member\n              </button>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"form-section\">\n            <h3>HEALTH DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Physical Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Height</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.height}\n                    onChange={(e) => handlePhysicalDetailsChange('height', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Weight</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.weight}\n                    onChange={(e) => handlePhysicalDetailsChange('weight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Blood Group</label>\n                  <select\n                    value={physicalDetails.bloodGroup}\n                    onChange={(e) => handlePhysicalDetailsChange('bloodGroup', e.target.value)}\n                  >\n                    <option value=\"\">Select Blood Group</option>\n                    <option value=\"A+\">A+</option>\n                    <option value=\"A-\">A-</option>\n                    <option value=\"B+\">B+</option>\n                    <option value=\"B-\">B-</option>\n                    <option value=\"AB+\">AB+</option>\n                    <option value=\"AB-\">AB-</option>\n                    <option value=\"O+\">O+</option>\n                    <option value=\"O-\">O-</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Right</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightRight}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightRight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Left</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightLeft}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightLeft', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Physical Disability</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.physicalDisability}\n                    onChange={(e) => handlePhysicalDetailsChange('physicalDisability', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Identification Mark</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.identificationMark}\n                    onChange={(e) => handlePhysicalDetailsChange('identificationMark', e.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"form-section\">\n            <h3>EDUCATION DETAILS</h3>\n\n            {education.map((edu, index) => (\n              <div key={index} className=\"education-section\">\n                <h4>Education {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Examination Passed</label>\n                    <input\n                      type=\"text\"\n                      value={edu.examination}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].examination = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Specialisation</label>\n                    <input\n                      type=\"text\"\n                      value={edu.specialisation}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].specialisation = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>School/College/Institution</label>\n                    <input\n                      type=\"text\"\n                      value={edu.schoolCollege}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].schoolCollege = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>University/Board</label>\n                    <input\n                      type=\"text\"\n                      value={edu.university}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].university = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Full Time/Part Time/Correspondence</label>\n                    <select\n                      value={edu.fullTimePartTime}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].fullTimePartTime = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    >\n                      <option value=\"\">Select Type</option>\n                      <option value=\"Full Time\">Full Time</option>\n                      <option value=\"Part Time\">Part Time</option>\n                      <option value=\"Correspondence\">Correspondence</option>\n                    </select>\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Duration of Course</label>\n                    <input\n                      type=\"text\"\n                      value={edu.duration}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].duration = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Month & Year of Passing</label>\n                    <input\n                      type=\"text\"\n                      value={edu.monthYearPassing}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].monthYearPassing = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Grade/% Marks</label>\n                    <input\n                      type=\"text\"\n                      value={edu.gradeMarks}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].gradeMarks = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Distinctions/Scholarships/Prizes Won</label>\n                    <textarea\n                      value={edu.distinctions}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].distinctions = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Certificate/Document Upload</label>\n                    <input\n                      type=\"file\"\n                      accept=\"image/*,.pdf\"\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'education', 'certificateFile', index)}\n                    />\n                    {edu.certificateFile && (\n                      <div className=\"file-preview\">\n                        <span className=\"file-name\">{edu.certificateFile.name}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addEducation} className=\"btn-add\">\n              Add Education\n            </button>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"form-section\">\n            <h3>WORK HISTORY DATA</h3>\n\n            {workExperience.map((experience, index) => (\n              <div key={index} className=\"work-experience-section\">\n                <h4>Work Experience {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Employer's Name</label>\n                    <input\n                      type=\"text\"\n                      value={experience.employerName}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].employerName = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Address</label>\n                    <textarea\n                      value={experience.address}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].address = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>From Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.fromDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fromDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>To Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.toDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].toDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Position Held</label>\n                    <input\n                      type=\"text\"\n                      value={experience.lastPosition}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastPosition = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Designation</label>\n                    <input\n                      type=\"text\"\n                      value={experience.designation}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].designation = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Nature of Duties</label>\n                    <textarea\n                      value={experience.natureOfDuties}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].natureOfDuties = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Name & Designation of Immediate Superior</label>\n                    <input\n                      type=\"text\"\n                      value={experience.immediateSuperior}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].immediateSuperior = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Emoluments (Rs. Per month at joining)</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossEmoluments}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossEmoluments = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Drawn</label>\n                    <input\n                      type=\"number\"\n                      value={experience.lastDrawn}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastDrawn = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Basic Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.basicSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].basicSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Fixed Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.fixedSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fixedSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Variable Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.variableSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].variableSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addWorkExperience} className=\"btn-add\">\n              Add Work Experience\n            </button>\n          </div>\n        );\n\n      case 6:\n        return (\n          <div className=\"form-section\">\n            <h3>GENERAL DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Previous Interview with MH Group</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Have you ever been interviewed by any of the MH Group of Companies?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"true\"\n                        checked={previousEmployment.hasBeenInterviewed === true}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"false\"\n                        checked={previousEmployment.hasBeenInterviewed === false}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {previousEmployment.hasBeenInterviewed && (\n                  <>\n                    <div className=\"form-group\">\n                      <label>Date/Year</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewDate}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewDate: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Position</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewPosition}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewPosition: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Company</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewCompany}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewCompany: e.target.value }))}\n                      />\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>References & Additional Information</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Relatives/Acquaintance in MH Group - Name</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.name}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, name: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Relationship</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.relationship}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, relationship: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Position</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.position}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, position: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Company & Phone No.</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.companyAndPhone}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, companyAndPhone: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group full-width\">\n                  <label>How did you come to know of this position?</label>\n                  <textarea\n                    value={references.howDidYouKnow}\n                    onChange={(e) => setReferences(prev => ({ ...prev, howDidYouKnow: e.target.value }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Are you engaged in any personal business?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"true\"\n                        checked={references.personalBusiness === true}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"false\"\n                        checked={references.personalBusiness === false}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.personalBusiness && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, indicate nature of business</label>\n                    <textarea\n                      value={references.businessNature}\n                      onChange={(e) => setReferences(prev => ({ ...prev, businessNature: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>Do you have any contract/bond with your previous employer?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"true\"\n                        checked={references.contractWithPreviousEmployer === true}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"false\"\n                        checked={references.contractWithPreviousEmployer === false}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.contractWithPreviousEmployer && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, Give Details</label>\n                    <textarea\n                      value={references.contractDetails}\n                      onChange={(e) => setReferences(prev => ({ ...prev, contractDetails: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>If selected, when can you join?</label>\n                  <input\n                    type=\"date\"\n                    value={references.whenCanJoin}\n                    onChange={(e) => setReferences(prev => ({ ...prev, whenCanJoin: e.target.value }))}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 7:\n        return (\n          <div className=\"form-section\">\n            <h3>CRIMINAL RECORDS</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Have you ever been involved in any criminal proceedings?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenInvolved === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenInvolved === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenInvolved && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.details}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, details: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Have you ever been convicted of any offence?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenConvicted === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenConvicted === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenConvicted && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.convictionDetails}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, convictionDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        );\n\n      case 8:\n        return (\n          <div className=\"form-section\">\n            <h3>DECLARATION FORM</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-text\">\n                  <p><strong>I hereby declare that:</strong></p>\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>\n                  <input\n                    type=\"checkbox\"\n                    checked={declaration.notConnectedToDirectors}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, notConnectedToDirectors: e.target.checked }))}\n                  />\n                  I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\n                </label>\n              </div>\n\n              <div className=\"form-group\">\n                <label>OR I am a partner or relative of a Director of the Company</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"true\"\n                      checked={declaration.isPartnerOrRelative === true}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"false\"\n                      checked={declaration.isPartnerOrRelative === false}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n\n              {declaration.isPartnerOrRelative && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, provide details</label>\n                  <textarea\n                    value={declaration.partnerRelativeDetails}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, partnerRelativeDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Place</label>\n                <input\n                  type=\"text\"\n                  value={declaration.place}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, place: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label>Date</label>\n                <input\n                  type=\"date\"\n                  value={declaration.date}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, date: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>Applicant's Signature</label>\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your full name as signature\"\n                  value={declaration.applicantSignature}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, applicantSignature: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-note\">\n                  <p><em>Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.</em></p>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step {currentStep}</div>;\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n\n  return (\n    <div className=\"onboarding-form\">\n      <div className=\"form-header\">\n        <h2>Employee Onboarding Form</h2>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress-fill\"\n            style={{ width: `${(currentStep / totalSteps) * 100}%` }}\n          ></div>\n        </div>\n        <p>Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}</p>\n      </div>\n\n\n      {/* Popup Modal for feedback messages */}\n      {(successMessage || errorMessage) && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content\">\n            <div className={successMessage ? \"success-message\" : \"error-message\"}>\n              {successMessage || errorMessage}\n            </div>\n            <button className=\"modal-close\" onClick={closeModal}>Close</button>\n          </div>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        {renderStep()}\n      </form>\n\n      <div className=\"form-navigation\">\n        {currentStep > 1 && (\n          <button type=\"button\" onClick={prevStep} className=\"btn-secondary\">\n            Previous\n          </button>\n        )}\n        {currentStep < totalSteps ? (\n          <button type=\"button\" onClick={nextStep} className=\"btn-primary\">\n            Next\n          </button>\n        ) : (\n          <button type=\"button\" onClick={() => {\n            const form = document.querySelector('form');\n            if (form) {\n              form.requestSubmit();\n            }\n          }} className=\"btn-primary\">\n            Submit Form\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default OnboardingForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAC7B,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsH7C,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM;IAAEa;EAAM,CAAC,GAAGX,SAAS,CAAoB,CAAC;EAChD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAkB;IACtEgB,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,GAAG,EAAE,EAAE;IACPC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAmB,CACrE;IAAEuC,QAAQ,EAAE,EAAE;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC1D,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAkB;IACtE6C,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAiB,CACjE;IAAEyD,IAAI,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEvC,WAAW,EAAE,EAAE;IAAEwC,aAAa,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,uBAAuB,EAAE;EAAG,CAAC,CAChH,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAkB;IACtEgE,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxE,QAAQ,CAAqB;IAC/EyE,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,EAAE;IACrBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAa;IACvD+E,kBAAkB,EAAE;MAClBtB,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBsB,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC;IACDC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,cAAc,EAAE,EAAE;IAClBC,4BAA4B,EAAE,KAAK;IACnCC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAmB,CACrE;IACE0F,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1G,QAAQ,CAAc,CACtD;IACE2G,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBnB,QAAQ,EAAE,EAAE;IACZoB,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE;EACnB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAMsH,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxH,QAAQ,CAAC;IACrDyH,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,KAAK;IACvBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9H,QAAQ,CAAC;IAC7C+H,uBAAuB,EAAE,KAAK;IAC9BC,mBAAmB,EAAE,KAAK;IAC1BC,sBAAsB,EAAE,EAAE;IAC1BC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,2BAA2B,GAAGA,CAACC,KAA4B,EAAEC,KAAa,KAAK;IACnFxH,kBAAkB,CAACyH,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,2BAA2B,GAAGA,CAACH,KAA4B,EAAEC,KAAa,KAAK;IACnF3F,kBAAkB,CAAC4F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,2BAA2B,GAAGA,CAACJ,KAA4B,EAAEC,KAAa,KAAK;IACnFxE,kBAAkB,CAACyE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAACC,IAAiB,EAAEC,OAA8C,EAAEP,KAAa,EAAEQ,KAAc,KAAK;IAC7H;IACA,IAAIF,IAAI,IAAIC,OAAO,KAAK,UAAU,IAAIP,KAAK,KAAK,eAAe,EAAE;MAC/D,MAAMS,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;MAC5B,IAAIH,IAAI,CAACI,IAAI,GAAGD,OAAO,EAAE;QACvBE,KAAK,CAAC,wCAAwC,CAAC;QAC/C;MACF;IACF;IAEA,IAAIJ,OAAO,KAAK,UAAU,EAAE;MAC1B9H,kBAAkB,CAACyH,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIC,OAAO,KAAK,UAAU,EAAE;MACjCjG,kBAAkB,CAAC4F,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIC,OAAO,KAAK,WAAW,IAAIC,KAAK,KAAKI,SAAS,EAAE;MACzDxC,YAAY,CAAC8B,IAAI,IAAI;QACnB,MAAMW,YAAY,GAAG,CAAC,GAAGX,IAAI,CAAC;QAC9BW,YAAY,CAACL,KAAK,CAAC,GAAG;UAAE,GAAGK,YAAY,CAACL,KAAK,CAAC;UAAE,CAACR,KAAK,GAAGM;QAAK,CAAC;QAC/D,OAAOO,YAAY;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9G,iBAAiB,CAACkG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEjG,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,CAAC,CAAC;EACjG,CAAC;EAED,MAAM2G,eAAe,GAAGA,CAAA,KAAM;IAC5B7F,gBAAgB,CAACgF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAE/E,IAAI,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEvC,WAAW,EAAE,EAAE;MAAEwC,aAAa,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,uBAAuB,EAAE;IAAG,CAAC,CAAC,CAAC;EACtJ,CAAC;EAED,MAAMyF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7D,iBAAiB,CAAC+C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAClC9C,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM+C,YAAY,GAAGA,CAAA,KAAM;IACzB7C,YAAY,CAAC8B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC7B7B,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,EAAE;MACpBnB,QAAQ,EAAE,EAAE;MACZoB,gBAAgB,EAAE,EAAE;MACpBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMqC,YAAY,GAAIC,IAAY,IAAK;IACrC,MAAMC,MAAM,GAAG,CACb,EAAE,EACF,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,kBAAkB,CACnB;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAI,EAAE;EAC3B,CAAC;EAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEzC,WAAW,EAAE,cAAc,EAAEE,UAAU,CAAC;IACtF,IAAIF,WAAW,GAAGE,UAAU,EAAE;MAC5BD,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;MAC/BwC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEzC,WAAW,GAAG,CAAC,CAAC;IACjD,CAAC,MAAM;MACLwC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI1C,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAM2C,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACnC,MAAMC,cAAc,GAAG,EAAAP,qBAAA,GACrBlJ,eAAe,CAACE,SAAS,cAAAgJ,qBAAA,uBAAzBA,qBAAA,CAA2BQ,IAAI,CAAC,CAAC,GAAAP,qBAAA,GACjCnJ,eAAe,CAACI,QAAQ,cAAA+I,qBAAA,uBAAxBA,qBAAA,CAA0BO,IAAI,CAAC,CAAC,GAAAN,qBAAA,GAChCpJ,eAAe,CAACK,WAAW,cAAA+I,qBAAA,uBAA3BA,qBAAA,CAA6BM,IAAI,CAAC,CAAC,GAAAL,qBAAA,GACnCrJ,eAAe,CAACM,MAAM,cAAA+I,qBAAA,uBAAtBA,qBAAA,CAAwBK,IAAI,CAAC,CAAC,GAAAJ,qBAAA,GAC9BtJ,eAAe,CAACW,WAAW,cAAA2I,qBAAA,uBAA3BA,qBAAA,CAA6BI,IAAI,CAAC,CAAC,GAAAH,qBAAA,GACnCvJ,eAAe,CAACc,aAAa,cAAAyI,qBAAA,uBAA7BA,qBAAA,CAA+BG,IAAI,CAAC,CAAC,GAAAF,qBAAA,GACrCxJ,eAAe,CAACe,kBAAkB,cAAAyI,qBAAA,uBAAlCA,qBAAA,CAAoCE,IAAI,CAAC,CAAC,CAC3C;IACD,OAAOD,cAAc,CAACE,KAAK,CAACC,OAAO,CAAC;EACtC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,KAAsB,IAAK;IACrDA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBnK,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;IACrB;IACA,IAAI,CAACmJ,sBAAsB,CAAC,CAAC,EAAE;MAC7BnJ,eAAe,CAAC,2EAA2E,CAAC;MAC5F;IACF;IACA,IAAIwG,WAAW,KAAKE,UAAU,EAAE;MAC9B1G,eAAe,CAAC,uDAAuD,CAAC;MACxE;IACF;IACA;IACA,MAAMkK,WAAW,GAAG;MAClBhK,eAAe;MACfuB,cAAc;MACdM,eAAe;MACfY,aAAa;MACbO,eAAe;MACfS,kBAAkB;MAClBM,UAAU;MACVW,cAAc;MACdiB,SAAS;MACTc,eAAe;MACfM;IACF,CAAC;IACD,IAAI;MACF,MAAMkD,GAAG,GAAG,wDAAwDlK,KAAK,GAAG;MAC5E;MACA,MAAMZ,KAAK,CAAC+K,IAAI,CAACD,GAAG,EAAE;QAAEE,SAAS,EAAEH;MAAY,CAAC,CAAC;MACjDpK,iBAAiB,CAAC,8BAA8B,CAAC;MACjDE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOsK,KAAK,EAAE;MACdtK,eAAe,CAAC,oDAAoD,CAAC;MACrEF,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAMyK,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ/D,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEhH,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGzBrL,OAAA;YAAKgL,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCjL,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjL,OAAA;gBAAKgL,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBvK,eAAe,CAACsB,aAAa,gBAC5BhC,OAAA;kBACEsL,GAAG,EAAEC,GAAG,CAACC,eAAe,CAAC9K,eAAe,CAACsB,aAAa,CAAE;kBACxDyJ,GAAG,EAAC,gBAAgB;kBACpBT,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,gBAEFrL,OAAA;kBAAKgL,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BjL,OAAA;oBAAK0L,KAAK,EAAC,IAAI;oBAAC9H,MAAM,EAAC,IAAI;oBAAC+H,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAAb,QAAA,gBAC/FjL,OAAA;sBAAM+L,CAAC,EAAC;oBAA2C;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3DrL,OAAA;sBAAQgM,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAG;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrL,OAAA;gBAAMgL,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BjL,OAAA;gBAAKgL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BjL,OAAA;kBAAGgL,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpErL,OAAA;kBAAOgL,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,eAE/B,eAAAjL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXC,MAAM,EAAC,SAAS;oBAChBC,QAAQ;oBACRC,QAAQ,EAAGC,CAAC;sBAAA,IAAAC,eAAA;sBAAA,OAAKjE,gBAAgB,CAAC,EAAAiE,eAAA,GAAAD,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,eAAe,CAAC;oBAAA,CAAC;oBAC5FxB,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EACP3K,eAAe,CAACsB,aAAa,iBAC5BhC,OAAA;kBAAGgL,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEvK,eAAe,CAACsB,aAAa,CAACqB;gBAAI;kBAAA6H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACjE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrL,OAAA;YAAKgL,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXE,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACE,SAAU;gBACjC0L,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,WAAW,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACG,UAAW;gBAClCyL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,YAAY,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXE,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACI,QAAS;gBAChCwL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,UAAU,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXE,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACK,WAAY;gBACnCuL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,aAAa,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBrL,OAAA;gBACEqM,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACM,MAAO;gBAC9BsL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,QAAQ,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK,CAAE;gBAAA8C,QAAA,gBAEvEjL,OAAA;kBAAQmI,KAAK,EAAC,EAAE;kBAAA8C,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCrL,OAAA;kBAAQmI,KAAK,EAAC,MAAM;kBAAA8C,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrL,OAAA;kBAAQmI,KAAK,EAAC,QAAQ;kBAAA8C,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrL,OAAA;kBAAQmI,KAAK,EAAC,OAAO;kBAAA8C,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACO,UAAW;gBAClCqL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,YAAY,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClBrL,OAAA;gBACEmM,IAAI,EAAC,QAAQ;gBACbhE,KAAK,EAAEzH,eAAe,CAACQ,GAAI;gBAC3BoL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,KAAK,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACS,YAAa;gBACpCmL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,cAAc,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7BrL,OAAA;gBACEmI,KAAK,EAAEzH,eAAe,CAACU,aAAc;gBACrCkL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,eAAe,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK,CAAE;gBAAA8C,QAAA,gBAE9EjL,OAAA;kBAAQmI,KAAK,EAAC,EAAE;kBAAA8C,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCrL,OAAA;kBAAQmI,KAAK,EAAC,QAAQ;kBAAA8C,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrL,OAAA;kBAAQmI,KAAK,EAAC,SAAS;kBAAA8C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCrL,OAAA;kBAAQmI,KAAK,EAAC,UAAU;kBAAA8C,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CrL,OAAA;kBAAQmI,KAAK,EAAC,SAAS;kBAAA8C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXE,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACW,WAAY;gBACnCiL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,aAAa,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACY,QAAS;gBAChCgL,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,UAAU,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACa,WAAY;gBACnC+K,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,aAAa,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BrL,OAAA;gBACEmM,IAAI,EAAC,KAAK;gBACVE,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACc,aAAc;gBACrC8K,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,eAAe,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCrL,OAAA;gBACEmM,IAAI,EAAC,KAAK;gBACVE,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACe,kBAAmB;gBAC1C6K,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,oBAAoB,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACgB,eAAgB;gBACvC4K,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,iBAAiB,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBrL,OAAA;gBACEmM,IAAI,EAAC,OAAO;gBACZE,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACiB,KAAM;gBAC7B2K,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,OAAO,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjL,OAAA;gBAAAiL,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCrL,OAAA;gBACEqM,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACkB,cAAe;gBACtC0K,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,gBAAgB,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjL,OAAA;gBAAAiL,QAAA,EAAO;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCrL,OAAA;gBACEqM,QAAQ;gBACRlE,KAAK,EAAEzH,eAAe,CAACmB,gBAAiB;gBACxCyK,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,kBAAkB,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACoB,UAAW;gBAClCwK,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,YAAY,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEzH,eAAe,CAACqB,YAAa;gBACpCuK,QAAQ,EAAGC,CAAC,IAAKtE,2BAA2B,CAAC,cAAc,EAAEsE,CAAC,CAACE,MAAM,CAACtE,KAAK;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrL,OAAA;YAAKgL,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjL,OAAA;cAAAiL,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBrL,OAAA;cAAKgL,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE5F,eAAe,CAACE,UAAW;kBAClC6J,QAAQ,EAAGC,CAAC,IAAKlE,2BAA2B,CAAC,YAAY,EAAEkE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE5F,eAAe,CAACG,iBAAkB;kBACzC4J,QAAQ,EAAGC,CAAC,IAAKlE,2BAA2B,CAAC,mBAAmB,EAAEkE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE5F,eAAe,CAACI,aAAc;kBACrC2J,QAAQ,EAAGC,CAAC,IAAKlE,2BAA2B,CAAC,eAAe,EAAEkE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE5F,eAAe,CAACK,cAAe;kBACtC0J,QAAQ,EAAGC,CAAC,IAAKlE,2BAA2B,CAAC,gBAAgB,EAAEkE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE5F,eAAe,CAACM,gBAAiB;kBACxCyJ,QAAQ,EAAGC,CAAC,IAAKlE,2BAA2B,CAAC,kBAAkB,EAAEkE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXE,QAAQ;kBACRlE,KAAK,EAAE5F,eAAe,CAACO,SAAU;kBACjCwJ,QAAQ,EAAGC,CAAC,IAAKlE,2BAA2B,CAAC,WAAW,EAAEkE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAI,gBAAA;oBAAA,OAAKpE,gBAAgB,CAAC,EAAAoE,gBAAA,GAAAJ,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAC,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC;kBAAA;gBAAC;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC,EACD9I,eAAe,CAACU,OAAO,iBACtBjD,OAAA;kBAAKgL,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjL,OAAA;oBAAMgL,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE1I,eAAe,CAACU,OAAO,CAACI;kBAAI;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXE,QAAQ;kBACRlE,KAAK,EAAE5F,eAAe,CAACQ,YAAa;kBACpCuJ,QAAQ,EAAGC,CAAC,IAAKlE,2BAA2B,CAAC,cAAc,EAAEkE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAK,gBAAA;oBAAA,OAAKrE,gBAAgB,CAAC,EAAAqE,gBAAA,GAAAL,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAE,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC;kBAAA;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,EACD9I,eAAe,CAACW,UAAU,iBACzBlD,OAAA;kBAAKgL,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjL,OAAA;oBAAMgL,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE1I,eAAe,CAACW,UAAU,CAACG;kBAAI;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAM,gBAAA;oBAAA,OAAKtE,gBAAgB,CAAC,EAAAsE,gBAAA,GAAAN,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAG,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC;kBAAA;gBAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,EACD9I,eAAe,CAACS,YAAY,iBAC3BhD,OAAA;kBAAKgL,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjL,OAAA;oBAAMgL,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE1I,eAAe,CAACS,YAAY,CAACK;kBAAI;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrL,OAAA;YAAKgL,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjL,OAAA;cAAAiL,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtBpJ,cAAc,CAAC6K,GAAG,CAAC,CAACC,KAAK,EAAErE,KAAK,kBAC/B1I,OAAA;cAAiBgL,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBAC7CjL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE4E,KAAK,CAAC5K,QAAS;kBACtBmK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMS,SAAS,GAAG,CAAC,GAAG/K,cAAc,CAAC;oBACrC+K,SAAS,CAACtE,KAAK,CAAC,CAACvG,QAAQ,GAAGoK,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC1CjG,iBAAiB,CAAC8K,SAAS,CAAC;kBAC9B;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BjL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,UAAU;oBACfc,OAAO,EAAEF,KAAK,CAAC3K,KAAM;oBACrBkK,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,SAAS,GAAG,CAAC,GAAG/K,cAAc,CAAC;sBACrC+K,SAAS,CAACtE,KAAK,CAAC,CAACtG,KAAK,GAAGmK,CAAC,CAACE,MAAM,CAACQ,OAAO;sBACzC/K,iBAAiB,CAAC8K,SAAS,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,UAAU;oBACfc,OAAO,EAAEF,KAAK,CAAC1K,IAAK;oBACpBiK,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,SAAS,GAAG,CAAC,GAAG/K,cAAc,CAAC;sBACrC+K,SAAS,CAACtE,KAAK,CAAC,CAACrG,IAAI,GAAGkK,CAAC,CAACE,MAAM,CAACQ,OAAO;sBACxC/K,iBAAiB,CAAC8K,SAAS,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,QAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,UAAU;oBACfc,OAAO,EAAEF,KAAK,CAACzK,KAAM;oBACrBgK,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,SAAS,GAAG,CAAC,GAAG/K,cAAc,CAAC;sBACrC+K,SAAS,CAACtE,KAAK,CAAC,CAACpG,KAAK,GAAGiK,CAAC,CAACE,MAAM,CAACQ,OAAO;sBACzC/K,iBAAiB,CAAC8K,SAAS,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAlDE3C,KAAK;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDV,CACN,CAAC,eACFrL,OAAA;cAAQmM,IAAI,EAAC,QAAQ;cAACe,OAAO,EAAElE,gBAAiB;cAACgC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,oBACErL,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBrL,OAAA;YAAKgL,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjL,OAAA;cAAAiL,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtBlI,aAAa,CAAC2J,GAAG,CAAC,CAACK,MAAM,EAAEzE,KAAK,kBAC/B1I,OAAA;cAAiBgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAChDjL,OAAA;gBAAKgL,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBjL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEgF,MAAM,CAAC9J,IAAK;oBACnBiJ,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMa,UAAU,GAAG,CAAC,GAAGjK,aAAa,CAAC;sBACrCiK,UAAU,CAAC1E,KAAK,CAAC,CAACrF,IAAI,GAAGkJ,CAAC,CAACE,MAAM,CAACtE,KAAK;sBACvC/E,gBAAgB,CAACgK,UAAU,CAAC;oBAC9B;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEgF,MAAM,CAAC7J,YAAa;oBAC3BgJ,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMa,UAAU,GAAG,CAAC,GAAGjK,aAAa,CAAC;sBACrCiK,UAAU,CAAC1E,KAAK,CAAC,CAACpF,YAAY,GAAGiJ,CAAC,CAACE,MAAM,CAACtE,KAAK;sBAC/C/E,gBAAgB,CAACgK,UAAU,CAAC;oBAC9B;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEgF,MAAM,CAACpM,WAAY;oBAC1BuL,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMa,UAAU,GAAG,CAAC,GAAGjK,aAAa,CAAC;sBACrCiK,UAAU,CAAC1E,KAAK,CAAC,CAAC3H,WAAW,GAAGwL,CAAC,CAACE,MAAM,CAACtE,KAAK;sBAC9C/E,gBAAgB,CAACgK,UAAU,CAAC;oBAC9B;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEgF,MAAM,CAAC5J,aAAc;oBAC5B+I,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMa,UAAU,GAAG,CAAC,GAAGjK,aAAa,CAAC;sBACrCiK,UAAU,CAAC1E,KAAK,CAAC,CAACnF,aAAa,GAAGgJ,CAAC,CAACE,MAAM,CAACtE,KAAK;sBAChD/E,gBAAgB,CAACgK,UAAU,CAAC;oBAC9B;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzBrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEgF,MAAM,CAAC3J,UAAW;oBACzB8I,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMa,UAAU,GAAG,CAAC,GAAGjK,aAAa,CAAC;sBACrCiK,UAAU,CAAC1E,KAAK,CAAC,CAAClF,UAAU,GAAG+I,CAAC,CAACE,MAAM,CAACtE,KAAK;sBAC7C/E,gBAAgB,CAACgK,UAAU,CAAC;oBAC9B;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxCrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEgF,MAAM,CAAC1J,uBAAwB;oBACtC6I,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMa,UAAU,GAAG,CAAC,GAAGjK,aAAa,CAAC;sBACrCiK,UAAU,CAAC1E,KAAK,CAAC,CAACjF,uBAAuB,GAAG8I,CAAC,CAACE,MAAM,CAACtE,KAAK;sBAC1D/E,gBAAgB,CAACgK,UAAU,CAAC;oBAC9B;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1EE3C,KAAK;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2EV,CACN,CAAC,eACFrL,OAAA;cAAQmM,IAAI,EAAC,QAAQ;cAACe,OAAO,EAAEjE,eAAgB;cAAC+B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErL,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBrL,OAAA;YAAKgL,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjL,OAAA;cAAAiL,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBrL,OAAA;cAAKgL,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEzE,eAAe,CAACE,MAAO;kBAC9B0I,QAAQ,EAAGC,CAAC,IAAKjE,2BAA2B,CAAC,QAAQ,EAAEiE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEzE,eAAe,CAACG,MAAO;kBAC9ByI,QAAQ,EAAGC,CAAC,IAAKjE,2BAA2B,CAAC,QAAQ,EAAEiE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BrL,OAAA;kBACEmI,KAAK,EAAEzE,eAAe,CAACI,UAAW;kBAClCwI,QAAQ,EAAGC,CAAC,IAAKjE,2BAA2B,CAAC,YAAY,EAAEiE,CAAC,CAACE,MAAM,CAACtE,KAAK,CAAE;kBAAA8C,QAAA,gBAE3EjL,OAAA;oBAAQmI,KAAK,EAAC,EAAE;oBAAA8C,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CrL,OAAA;oBAAQmI,KAAK,EAAC,IAAI;oBAAA8C,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrL,OAAA;oBAAQmI,KAAK,EAAC,IAAI;oBAAA8C,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrL,OAAA;oBAAQmI,KAAK,EAAC,IAAI;oBAAA8C,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrL,OAAA;oBAAQmI,KAAK,EAAC,IAAI;oBAAA8C,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrL,OAAA;oBAAQmI,KAAK,EAAC,KAAK;oBAAA8C,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCrL,OAAA;oBAAQmI,KAAK,EAAC,KAAK;oBAAA8C,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCrL,OAAA;oBAAQmI,KAAK,EAAC,IAAI;oBAAA8C,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BrL,OAAA;oBAAQmI,KAAK,EAAC,IAAI;oBAAA8C,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEzE,eAAe,CAACK,aAAc;kBACrCuI,QAAQ,EAAGC,CAAC,IAAKjE,2BAA2B,CAAC,eAAe,EAAEiE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEzE,eAAe,CAACM,YAAa;kBACpCsI,QAAQ,EAAGC,CAAC,IAAKjE,2BAA2B,CAAC,cAAc,EAAEiE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEzE,eAAe,CAACO,kBAAmB;kBAC1CqI,QAAQ,EAAGC,CAAC,IAAKjE,2BAA2B,CAAC,oBAAoB,EAAEiE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEzE,eAAe,CAACQ,kBAAmB;kBAC1CoI,QAAQ,EAAGC,CAAC,IAAKjE,2BAA2B,CAAC,oBAAoB,EAAEiE,CAAC,CAACE,MAAM,CAACtE,KAAK;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErL,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzBhF,SAAS,CAACyG,GAAG,CAAC,CAACO,GAAG,EAAE3E,KAAK,kBACxB1I,OAAA;YAAiBgL,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC5CjL,OAAA;cAAAiL,QAAA,GAAI,YAAU,EAACvC,KAAK,GAAG,CAAC;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9BrL,OAAA;cAAKgL,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEkF,GAAG,CAAC9G,WAAY;kBACvB+F,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAACnC,WAAW,GAAGgG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAChD7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEkF,GAAG,CAAC7G,cAAe;kBAC1B8F,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAAClC,cAAc,GAAG+F,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACnD7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEkF,GAAG,CAAC5G,aAAc;kBACzB6F,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAACjC,aAAa,GAAG8F,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAClD7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEkF,GAAG,CAAC3G,UAAW;kBACtB4F,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAAChC,UAAU,GAAG6F,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC/C7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDrL,OAAA;kBACEmI,KAAK,EAAEkF,GAAG,CAAC1G,gBAAiB;kBAC5B2F,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAAC/B,gBAAgB,GAAG4F,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACrD7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B,CAAE;kBAAAkC,QAAA,gBAEFjL,OAAA;oBAAQmI,KAAK,EAAC,EAAE;oBAAA8C,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCrL,OAAA;oBAAQmI,KAAK,EAAC,WAAW;oBAAA8C,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CrL,OAAA;oBAAQmI,KAAK,EAAC,WAAW;oBAAA8C,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CrL,OAAA;oBAAQmI,KAAK,EAAC,gBAAgB;oBAAA8C,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEkF,GAAG,CAAC7H,QAAS;kBACpB8G,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAAClD,QAAQ,GAAG+G,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC7C7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEkF,GAAG,CAACzG,gBAAiB;kBAC5B0F,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAAC9B,gBAAgB,GAAG2F,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACrD7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEkF,GAAG,CAACxG,UAAW;kBACtByF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAAC7B,UAAU,GAAG0F,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC/C7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDrL,OAAA;kBACEmI,KAAK,EAAEkF,GAAG,CAACvG,YAAa;kBACxBwF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMxD,YAAY,GAAG,CAAC,GAAG1C,SAAS,CAAC;oBACnC0C,YAAY,CAACL,KAAK,CAAC,CAAC5B,YAAY,GAAGyF,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACjD7B,YAAY,CAACyC,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAe,gBAAA;oBAAA,OAAK/E,gBAAgB,CAAC,EAAA+E,gBAAA,GAAAf,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAY,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAE5E,KAAK,CAAC;kBAAA;gBAAC;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,EACDgC,GAAG,CAACtG,eAAe,iBAClB/G,OAAA;kBAAKgL,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjL,OAAA;oBAAMgL,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEoC,GAAG,CAACtG,eAAe,CAAC1D;kBAAI;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/HE3C,KAAK;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgIV,CACN,CAAC,eACFrL,OAAA;YAAQmM,IAAI,EAAC,QAAQ;YAACe,OAAO,EAAE/D,YAAa;YAAC6B,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErL,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzBjG,cAAc,CAAC0H,GAAG,CAAC,CAACS,UAAU,EAAE7E,KAAK,kBACpC1I,OAAA;YAAiBgL,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAClDjL,OAAA;cAAAiL,QAAA,GAAI,kBAAgB,EAACvC,KAAK,GAAG,CAAC;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCrL,OAAA;cAAKgL,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEoF,UAAU,CAACjI,YAAa;kBAC/BgH,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAACpD,YAAY,GAAGiH,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAClD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBrL,OAAA;kBACEmI,KAAK,EAAEoF,UAAU,CAAChI,OAAQ;kBAC1B+G,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAACnD,OAAO,GAAGgH,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC7C9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEoF,UAAU,CAAC9H,QAAS;kBAC3B6G,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAACjD,QAAQ,GAAG8G,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC9C9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEoF,UAAU,CAAC7H,MAAO;kBACzB4G,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAAChD,MAAM,GAAG6G,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC5C9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEoF,UAAU,CAAC5H,YAAa;kBAC/B2G,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAAC/C,YAAY,GAAG4G,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAClD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEoF,UAAU,CAAC3H,WAAY;kBAC9B0G,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAAC9C,WAAW,GAAG2G,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACjD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BrL,OAAA;kBACEmI,KAAK,EAAEoF,UAAU,CAAC1H,cAAe;kBACjCyG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAAC7C,cAAc,GAAG0G,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACpD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEoF,UAAU,CAACzH,iBAAkB;kBACpCwG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAAC5C,iBAAiB,GAAGyG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACvD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DrL,OAAA;kBACEmM,IAAI,EAAC,QAAQ;kBACbhE,KAAK,EAAEoF,UAAU,CAACxH,eAAgB;kBAClCuG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAAC3C,eAAe,GAAGwG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACrD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBrL,OAAA;kBACEmM,IAAI,EAAC,QAAQ;kBACbhE,KAAK,EAAEoF,UAAU,CAACvH,SAAU;kBAC5BsG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAAC1C,SAAS,GAAGuG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBAC/C9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrL,OAAA;kBACEmM,IAAI,EAAC,QAAQ;kBACbhE,KAAK,EAAEoF,UAAU,CAACtH,WAAY;kBAC9BqG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAACzC,WAAW,GAAGsG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACjD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrL,OAAA;kBACEmM,IAAI,EAAC,QAAQ;kBACbhE,KAAK,EAAEoF,UAAU,CAACrH,WAAY;kBAC9BoG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAACxC,WAAW,GAAGqG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACjD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrL,OAAA;kBACEmM,IAAI,EAAC,QAAQ;kBACbhE,KAAK,EAAEoF,UAAU,CAACpH,cAAe;kBACjCmG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAACvC,cAAc,GAAGoG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACpD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrL,OAAA;kBACEmM,IAAI,EAAC,QAAQ;kBACbhE,KAAK,EAAEoF,UAAU,CAACnH,WAAY;kBAC9BkG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMiB,aAAa,GAAG,CAAC,GAAGpI,cAAc,CAAC;oBACzCoI,aAAa,CAAC9E,KAAK,CAAC,CAACtC,WAAW,GAAGmG,CAAC,CAACE,MAAM,CAACtE,KAAK;oBACjD9C,iBAAiB,CAACmI,aAAa,CAAC;kBAClC;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAzKE3C,KAAK;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0KV,CACN,CAAC,eACFrL,OAAA;YAAQmM,IAAI,EAAC,QAAQ;YAACe,OAAO,EAAEhE,iBAAkB;YAAC8B,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErL,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAErBrL,OAAA;YAAKgL,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjL,OAAA;cAAAiL,QAAA,EAAI;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCrL,OAAA;cAAKgL,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAmE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClFrL,OAAA;kBAAKgL,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBACEmM,IAAI,EAAC,OAAO;sBACZ9I,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,MAAM;sBACZ8E,OAAO,EAAE9I,kBAAkB,CAACE,kBAAkB,KAAK,IAAK;sBACxDiI,QAAQ,EAAEA,CAAA,KAAMlI,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBACEmM,IAAI,EAAC,OAAO;sBACZ9I,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,OAAO;sBACb8E,OAAO,EAAE9I,kBAAkB,CAACE,kBAAkB,KAAK,KAAM;sBACzDiI,QAAQ,EAAEA,CAAA,KAAMlI,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlH,kBAAkB,CAACE,kBAAkB,iBACpCrE,OAAA,CAAAE,SAAA;gBAAA+K,QAAA,gBACEjL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxBrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEhE,kBAAkB,CAACG,aAAc;oBACxCgI,QAAQ,EAAGC,CAAC,IAAKnI,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE9D,aAAa,EAAEiI,CAAC,CAACE,MAAM,CAACtE;oBAAM,CAAC,CAAC;kBAAE;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvBrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEhE,kBAAkB,CAACI,iBAAkB;oBAC5C+H,QAAQ,EAAGC,CAAC,IAAKnI,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE7D,iBAAiB,EAAEgI,CAAC,CAACE,MAAM,CAACtE;oBAAM,CAAC,CAAC;kBAAE;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrL,OAAA;kBAAKgL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjL,OAAA;oBAAAiL,QAAA,EAAO;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtBrL,OAAA;oBACEmM,IAAI,EAAC,MAAM;oBACXhE,KAAK,EAAEhE,kBAAkB,CAACK,gBAAiB;oBAC3C8H,QAAQ,EAAGC,CAAC,IAAKnI,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE5D,gBAAgB,EAAE+H,CAAC,CAACE,MAAM,CAACtE;oBAAM,CAAC,CAAC;kBAAE;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrL,OAAA;YAAKgL,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjL,OAAA;cAAAiL,QAAA,EAAI;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CrL,OAAA;cAAKgL,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACtB,IAAK;kBAC1CiJ,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEtB,IAAI,EAAEkJ,CAAC,CAACE,MAAM,CAACtE;oBAAM;kBACzE,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACrB,YAAa;kBAClDgJ,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAErB,YAAY,EAAEiJ,CAAC,CAACE,MAAM,CAACtE;oBAAM;kBACjF,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACC,QAAS;kBAC9C0H,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEC,QAAQ,EAAE2H,CAAC,CAACE,MAAM,CAACtE;oBAAM;kBAC7E,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACE,eAAgB;kBACrDyH,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEE,eAAe,EAAE0H,CAAC,CAACE,MAAM,CAACtE;oBAAM;kBACpF,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjL,OAAA;kBAAAiL,QAAA,EAAO;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDrL,OAAA;kBACEmI,KAAK,EAAE1D,UAAU,CAACK,aAAc;kBAChCwH,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtD,aAAa,EAAEyH,CAAC,CAACE,MAAM,CAACtE;kBAAM,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDrL,OAAA;kBAAKgL,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBACEmM,IAAI,EAAC,OAAO;sBACZ9I,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,MAAM;sBACZ8E,OAAO,EAAExI,UAAU,CAACM,gBAAgB,KAAK,IAAK;sBAC9CuH,QAAQ,EAAEA,CAAA,KAAM5H,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBACEmM,IAAI,EAAC,OAAO;sBACZ9I,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,OAAO;sBACb8E,OAAO,EAAExI,UAAU,CAACM,gBAAgB,KAAK,KAAM;sBAC/CuH,QAAQ,EAAEA,CAAA,KAAM5H,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL5G,UAAU,CAACM,gBAAgB,iBAC1B/E,OAAA;gBAAKgL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAmC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDrL,OAAA;kBACEmI,KAAK,EAAE1D,UAAU,CAACO,cAAe;kBACjCsH,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEpD,cAAc,EAAEuH,CAAC,CAACE,MAAM,CAACtE;kBAAM,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzErL,OAAA;kBAAKgL,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBACEmM,IAAI,EAAC,OAAO;sBACZ9I,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,MAAM;sBACZ8E,OAAO,EAAExI,UAAU,CAACQ,4BAA4B,KAAK,IAAK;sBAC1DqH,QAAQ,EAAEA,CAAA,KAAM5H,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBACEmM,IAAI,EAAC,OAAO;sBACZ9I,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,OAAO;sBACb8E,OAAO,EAAExI,UAAU,CAACQ,4BAA4B,KAAK,KAAM;sBAC3DqH,QAAQ,EAAEA,CAAA,KAAM5H,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL5G,UAAU,CAACQ,4BAA4B,iBACtCjF,OAAA;gBAAKgL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjL,OAAA;kBAAAiL,QAAA,EAAO;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnCrL,OAAA;kBACEmI,KAAK,EAAE1D,UAAU,CAACS,eAAgB;kBAClCoH,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAElD,eAAe,EAAEqH,CAAC,CAACE,MAAM,CAACtE;kBAAM,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDrL,OAAA;gBAAKgL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjL,OAAA;kBAAAiL,QAAA,EAAO;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CrL,OAAA;kBACEmM,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAE1D,UAAU,CAACU,WAAY;kBAC9BmH,QAAQ,EAAGC,CAAC,IAAK7H,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjD,WAAW,EAAEoH,CAAC,CAACE,MAAM,CAACtE;kBAAM,CAAC,CAAC;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErL,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBrL,OAAA;YAAKgL,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAwD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvErL,OAAA;gBAAKgL,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZ9I,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,MAAM;oBACZ8E,OAAO,EAAE9F,eAAe,CAACE,eAAe,KAAK,IAAK;oBAClDiF,QAAQ,EAAEA,CAAA,KAAMlF,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZ9I,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,OAAO;oBACb8E,OAAO,EAAE9F,eAAe,CAACE,eAAe,KAAK,KAAM;oBACnDiF,QAAQ,EAAEA,CAAA,KAAMlF,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLlE,eAAe,CAACE,eAAe,iBAC9BrH,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjL,OAAA;gBAAAiL,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCrL,OAAA;gBACEmI,KAAK,EAAEhB,eAAe,CAACG,OAAQ;gBAC/BgF,QAAQ,EAAGC,CAAC,IAAKnF,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEd,OAAO,EAAEiF,CAAC,CAACE,MAAM,CAACtE;gBAAM,CAAC,CAAC;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DrL,OAAA;gBAAKgL,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZ9I,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,MAAM;oBACZ8E,OAAO,EAAE9F,eAAe,CAACI,gBAAgB,KAAK,IAAK;oBACnD+E,QAAQ,EAAEA,CAAA,KAAMlF,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZ9I,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,OAAO;oBACb8E,OAAO,EAAE9F,eAAe,CAACI,gBAAgB,KAAK,KAAM;oBACpD+E,QAAQ,EAAEA,CAAA,KAAMlF,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLlE,eAAe,CAACI,gBAAgB,iBAC/BvH,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjL,OAAA;gBAAAiL,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCrL,OAAA;gBACEmI,KAAK,EAAEhB,eAAe,CAACK,iBAAkB;gBACzC8E,QAAQ,EAAGC,CAAC,IAAKnF,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEZ,iBAAiB,EAAE+E,CAAC,CAACE,MAAM,CAACtE;gBAAM,CAAC,CAAC;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACErL,OAAA;UAAKgL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjL,OAAA;YAAAiL,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBrL,OAAA;YAAKgL,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjL,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCjL,OAAA;gBAAKgL,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BjL,OAAA;kBAAAiL,QAAA,eAAGjL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrL,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCjL,OAAA;gBAAAiL,QAAA,gBACEjL,OAAA;kBACEmM,IAAI,EAAC,UAAU;kBACfc,OAAO,EAAExF,WAAW,CAACE,uBAAwB;kBAC7C2E,QAAQ,EAAGC,CAAC,IAAK7E,cAAc,CAACU,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAET,uBAAuB,EAAE4E,CAAC,CAACE,MAAM,CAACQ;kBAAQ,CAAC,CAAC;gBAAE;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,mJAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzErL,OAAA;gBAAKgL,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZ9I,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,MAAM;oBACZ8E,OAAO,EAAExF,WAAW,CAACG,mBAAmB,KAAK,IAAK;oBAClD0E,QAAQ,EAAEA,CAAA,KAAM5E,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZ9I,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,OAAO;oBACb8E,OAAO,EAAExF,WAAW,CAACG,mBAAmB,KAAK,KAAM;oBACnD0E,QAAQ,EAAEA,CAAA,KAAM5E,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL5D,WAAW,CAACG,mBAAmB,iBAC9B5H,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjL,OAAA;gBAAAiL,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCrL,OAAA;gBACEmI,KAAK,EAAEV,WAAW,CAACI,sBAAuB;gBAC1CyE,QAAQ,EAAGC,CAAC,IAAK7E,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEP,sBAAsB,EAAE0E,CAAC,CAACE,MAAM,CAACtE;gBAAM,CAAC,CAAC;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpBrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEV,WAAW,CAACK,KAAM;gBACzBwE,QAAQ,EAAGC,CAAC,IAAK7E,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEN,KAAK,EAAEyE,CAAC,CAACE,MAAM,CAACtE;gBAAM,CAAC,CAAC;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrL,OAAA;cAAKgL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjL,OAAA;gBAAAiL,QAAA,EAAO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnBrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEV,WAAW,CAACM,IAAK;gBACxBuE,QAAQ,EAAGC,CAAC,IAAK7E,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEL,IAAI,EAAEwE,CAAC,CAACE,MAAM,CAACtE;gBAAM,CAAC,CAAC;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrL,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCjL,OAAA;gBAAAiL,QAAA,EAAO;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCrL,OAAA;gBACEmM,IAAI,EAAC,MAAM;gBACXsB,WAAW,EAAC,kCAAkC;gBAC9CtF,KAAK,EAAEV,WAAW,CAACO,kBAAmB;gBACtCsE,QAAQ,EAAGC,CAAC,IAAK7E,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEJ,kBAAkB,EAAEuE,CAAC,CAACE,MAAM,CAACtE;gBAAM,CAAC,CAAC;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrL,OAAA;cAAKgL,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCjL,OAAA;gBAAKgL,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BjL,OAAA;kBAAAiL,QAAA,eAAGjL,OAAA;oBAAAiL,QAAA,EAAI;kBAA0I;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAOrL,OAAA;UAAAiL,QAAA,GAAK,OAAK,EAACjE,WAAW;QAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMqC,UAAU,GAAGA,CAAA,KAAM;IACvBpN,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACER,OAAA;IAAKgL,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BjL,OAAA;MAAKgL,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjL,OAAA;QAAAiL,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCrL,OAAA;QAAKgL,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BjL,OAAA;UACEgL,SAAS,EAAC,eAAe;UACzB2C,KAAK,EAAE;YAAEjC,KAAK,EAAE,GAAI1E,WAAW,GAAGE,UAAU,GAAI,GAAG;UAAI;QAAE;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrL,OAAA;QAAAiL,QAAA,GAAG,OAAK,EAACjE,WAAW,EAAC,MAAI,EAACE,UAAU,EAAC,IAAE,EAACkC,YAAY,CAACpC,WAAW,CAAC;MAAA;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAIL,CAAChL,cAAc,IAAIE,YAAY,kBAC9BP,OAAA;MAAKgL,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BjL,OAAA;QAAKgL,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjL,OAAA;UAAKgL,SAAS,EAAE3K,cAAc,GAAG,iBAAiB,GAAG,eAAgB;UAAA4K,QAAA,EAClE5K,cAAc,IAAIE;QAAY;UAAA2K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNrL,OAAA;UAAQgL,SAAS,EAAC,aAAa;UAACkC,OAAO,EAAEQ,UAAW;UAAAzC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDrL,OAAA;MAAM4N,QAAQ,EAAErD,YAAa;MAAAU,QAAA,EAC1BF,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEPrL,OAAA;MAAKgL,SAAS,EAAC,iBAAiB;MAAAC,QAAA,GAC7BjE,WAAW,GAAG,CAAC,iBACdhH,OAAA;QAAQmM,IAAI,EAAC,QAAQ;QAACe,OAAO,EAAExD,QAAS;QAACsB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAEnE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EACArE,WAAW,GAAGE,UAAU,gBACvBlH,OAAA;QAAQmM,IAAI,EAAC,QAAQ;QAACe,OAAO,EAAE3D,QAAS;QAACyB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAEjE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETrL,OAAA;QAAQmM,IAAI,EAAC,QAAQ;QAACe,OAAO,EAAEA,CAAA,KAAM;UACnC,MAAMW,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;UAC3C,IAAIF,IAAI,EAAE;YACRA,IAAI,CAACG,aAAa,CAAC,CAAC;UACtB;QACF,CAAE;QAAChD,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjL,EAAA,CA5lDID,cAAwB;EAAA,QAIVL,SAAS;AAAA;AAAAmO,EAAA,GAJvB9N,cAAwB;AA8lD9B,eAAeA,cAAc;AAAC,IAAA8N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}