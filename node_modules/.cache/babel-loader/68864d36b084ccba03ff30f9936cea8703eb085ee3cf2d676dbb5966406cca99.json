{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OnboardingForm = () => {\n  _s();\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [errorMessage, setErrorMessage] = useState(null);\n  const {\n    token\n  } = useParams();\n  const [personalDetails, setPersonalDetails] = useState({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n  const [languageSkills, setLanguageSkills] = useState([{\n    language: '',\n    speak: false,\n    read: false,\n    write: false\n  }]);\n  const [documentDetails, setDocumentDetails] = useState({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: ''\n  });\n  const [familyMembers, setFamilyMembers] = useState([{\n    name: '',\n    relationship: '',\n    dateOfBirth: '',\n    qualification: '',\n    occupation: '',\n    organisationAndPosition: ''\n  }]);\n  const [physicalDetails, setPhysicalDetails] = useState({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n  const [previousEmployment, setPreviousEmployment] = useState({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n  const [references, setReferences] = useState({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n  const [workExperience, setWorkExperience] = useState([{\n    employerName: '',\n    address: '',\n    duration: '',\n    fromDate: '',\n    toDate: '',\n    lastPosition: '',\n    designation: '',\n    natureOfDuties: '',\n    immediateSuperior: '',\n    grossEmoluments: '',\n    lastDrawn: '',\n    basicSalary: '',\n    fixedSalary: '',\n    variableSalary: '',\n    grossSalary: ''\n  }]);\n  const [education, setEducation] = useState([{\n    examination: '',\n    specialisation: '',\n    schoolCollege: '',\n    university: '',\n    fullTimePartTime: '',\n    duration: '',\n    monthYearPassing: '',\n    gradeMarks: '',\n    distinctions: ''\n  }]);\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n  const handlePersonalDetailsChange = (field, value) => {\n    setPersonalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleDocumentDetailsChange = (field, value) => {\n    setDocumentDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handlePhysicalDetailsChange = (field, value) => {\n    setPhysicalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, {\n      language: '',\n      speak: false,\n      read: false,\n      write: false\n    }]);\n  };\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, {\n      name: '',\n      relationship: '',\n      dateOfBirth: '',\n      qualification: '',\n      occupation: '',\n      organisationAndPosition: ''\n    }]);\n  };\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: ''\n    }]);\n  };\n  const getStepTitle = step => {\n    const titles = ['', 'PERSONAL DETAILS', 'FAMILY DATA', 'HEALTH DATA', 'EDUCATION DETAILS', 'WORK HISTORY DATA', 'GENERAL DATA', 'CRIMINAL RECORDS', 'DECLARATION FORM'];\n    return titles[step] || '';\n  };\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    var _personalDetails$firs, _personalDetails$last, _personalDetails$date, _personalDetails$gend, _personalDetails$nati, _personalDetails$cont, _personalDetails$emer;\n    const requiredFields = [(_personalDetails$firs = personalDetails.firstName) === null || _personalDetails$firs === void 0 ? void 0 : _personalDetails$firs.trim(), (_personalDetails$last = personalDetails.lastName) === null || _personalDetails$last === void 0 ? void 0 : _personalDetails$last.trim(), (_personalDetails$date = personalDetails.dateOfBirth) === null || _personalDetails$date === void 0 ? void 0 : _personalDetails$date.trim(), (_personalDetails$gend = personalDetails.gender) === null || _personalDetails$gend === void 0 ? void 0 : _personalDetails$gend.trim(), (_personalDetails$nati = personalDetails.nationality) === null || _personalDetails$nati === void 0 ? void 0 : _personalDetails$nati.trim(), (_personalDetails$cont = personalDetails.contactNumber) === null || _personalDetails$cont === void 0 ? void 0 : _personalDetails$cont.trim(), (_personalDetails$emer = personalDetails.emergencyContactNo) === null || _personalDetails$emer === void 0 ? void 0 : _personalDetails$emer.trim()];\n    return requiredFields.every(Boolean);\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n    // Collect all form data into a single object\n    const allFormData = {\n      personalDetails,\n      languageSkills,\n      documentDetails,\n      familyMembers,\n      physicalDetails,\n      previousEmployment,\n      references,\n      workExperience,\n      education,\n      criminalRecords,\n      declaration\n    };\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      // Send as { form_data: ... }\n      await axios.post(url, {\n        form_data: allFormData\n      });\n      setSuccessMessage(\"Form submitted successfully!\");\n      setErrorMessage(null);\n    } catch (error) {\n      setErrorMessage(\"Failed to submit the form. Please try again later.\");\n      setSuccessMessage(null);\n    }\n  };\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"PERSONAL DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"First Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.firstName,\n                onChange: e => handlePersonalDetailsChange('firstName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Middle Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.middleName,\n                onChange: e => handlePersonalDetailsChange('middleName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Last Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.lastName,\n                onChange: e => handlePersonalDetailsChange('lastName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date of Birth *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                required: true,\n                value: personalDetails.dateOfBirth,\n                onChange: e => handlePersonalDetailsChange('dateOfBirth', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Gender *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                required: true,\n                value: personalDetails.gender,\n                onChange: e => handlePersonalDetailsChange('gender', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Birth Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.birthPlace,\n                onChange: e => handlePersonalDetailsChange('birthPlace', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Age\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: personalDetails.age,\n                onChange: e => handlePersonalDetailsChange('age', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marriage Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: personalDetails.marriageDate,\n                onChange: e => handlePersonalDetailsChange('marriageDate', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marital Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: personalDetails.maritalStatus,\n                onChange: e => handlePersonalDetailsChange('maritalStatus', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Single\",\n                  children: \"Single\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Married\",\n                  children: \"Married\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Divorced\",\n                  children: \"Divorced\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Widowed\",\n                  children: \"Widowed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Nationality *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.nationality,\n                onChange: e => handlePersonalDetailsChange('nationality', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Religion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.religion,\n                onChange: e => handlePersonalDetailsChange('religion', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Native State\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.nativeState,\n                onChange: e => handlePersonalDetailsChange('nativeState', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Contact Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.contactNumber,\n                onChange: e => handlePersonalDetailsChange('contactNumber', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Emergency Contact No. *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.emergencyContactNo,\n                onChange: e => handlePersonalDetailsChange('emergencyContactNo', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"State of Domicile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.stateOfDomicile,\n                onChange: e => handlePersonalDetailsChange('stateOfDomicile', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                required: true,\n                value: personalDetails.email,\n                onChange: e => handlePersonalDetailsChange('email', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.presentAddress,\n                onChange: e => handlePersonalDetailsChange('presentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.permanentAddress,\n                onChange: e => handlePersonalDetailsChange('permanentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.presentPin,\n                onChange: e => handlePersonalDetailsChange('presentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.permanentPin,\n                onChange: e => handlePersonalDetailsChange('permanentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subsection full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Document Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Passport No.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: documentDetails.passportNo,\n                    onChange: e => handleDocumentDetailsChange('passportNo', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Passport Issue Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: documentDetails.passportIssueDate,\n                    onChange: e => handleDocumentDetailsChange('passportIssueDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Valid Upto Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: documentDetails.validUptoDate,\n                    onChange: e => handleDocumentDetailsChange('validUptoDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Country of Issue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: documentDetails.countryOfIssue,\n                    onChange: e => handleDocumentDetailsChange('countryOfIssue', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Valid Visa Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: documentDetails.validVisaDetails,\n                    onChange: e => handleDocumentDetailsChange('validVisaDetails', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"PAN Number *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    required: true,\n                    value: documentDetails.panNumber,\n                    onChange: e => handleDocumentDetailsChange('panNumber', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Aadhar Number *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    required: true,\n                    value: documentDetails.aadharNumber,\n                    onChange: e => handleDocumentDetailsChange('aadharNumber', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subsection full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Language Known\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this), languageSkills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"language-skill-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Language\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: skill.language,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].language = e.target.value;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"checkbox-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: skill.speak,\n                      onChange: e => {\n                        const newSkills = [...languageSkills];\n                        newSkills[index].speak = e.target.checked;\n                        setLanguageSkills(newSkills);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 25\n                    }, this), \"Speak\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: skill.read,\n                      onChange: e => {\n                        const newSkills = [...languageSkills];\n                        newSkills[index].read = e.target.checked;\n                        setLanguageSkills(newSkills);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 25\n                    }, this), \"Read\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: skill.write,\n                      onChange: e => {\n                        const newSkills = [...languageSkills];\n                        newSkills[index].write = e.target.checked;\n                        setLanguageSkills(newSkills);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 25\n                    }, this), \"Write\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: addLanguageSkill,\n                className: \"btn-add\",\n                children: \"Add Language\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"FAMILY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Family Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 15\n            }, this), familyMembers.map((member, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"family-member-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.name,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].name = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Relationship\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 727,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.relationship,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].relationship = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date of Birth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: member.dateOfBirth,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].dateOfBirth = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Qualification\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.qualification,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].qualification = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Occupation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.occupation,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].occupation = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Organisation and Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.organisationAndPosition,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].organisationAndPosition = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 776,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addFamilyMember,\n              className: \"btn-add\",\n              children: \"Add Family Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"HEALTH DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Physical Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Height\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.height,\n                  onChange: e => handlePhysicalDetailsChange('height', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Weight\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.weight,\n                  onChange: e => handlePhysicalDetailsChange('weight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Blood Group\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: physicalDetails.bloodGroup,\n                  onChange: e => handlePhysicalDetailsChange('bloodGroup', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Blood Group\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A+\",\n                    children: \"A+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A-\",\n                    children: \"A-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B+\",\n                    children: \"B+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B-\",\n                    children: \"B-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB+\",\n                    children: \"AB+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB-\",\n                    children: \"AB-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O+\",\n                    children: \"O+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O-\",\n                    children: \"O-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightRight,\n                  onChange: e => handlePhysicalDetailsChange('eyesightRight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightLeft,\n                  onChange: e => handlePhysicalDetailsChange('eyesightLeft', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 847,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Physical Disability\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.physicalDisability,\n                  onChange: e => handlePhysicalDetailsChange('physicalDisability', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Identification Mark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.identificationMark,\n                  onChange: e => handlePhysicalDetailsChange('identificationMark', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"EDUCATION DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 877,\n            columnNumber: 13\n          }, this), education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"education-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Education \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Examination Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.examination,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].examination = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Specialisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.specialisation,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].specialisation = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"School/College/Institution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.schoolCollege,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].schoolCollege = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"University/Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.university,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].university = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Full Time/Part Time/Correspondence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: edu.fullTimePartTime,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].fullTimePartTime = e.target.value;\n                    setEducation(newEducation);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time\",\n                    children: \"Full Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time\",\n                    children: \"Part Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Correspondence\",\n                    children: \"Correspondence\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Duration of Course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.duration,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].duration = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Month & Year of Passing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.monthYearPassing,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].monthYearPassing = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 961,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 959,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Grade/% Marks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.gradeMarks,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].gradeMarks = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 973,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Distinctions/Scholarships/Prizes Won\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: edu.distinctions,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].distinctions = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addEducation,\n            className: \"btn-add\",\n            children: \"Add Education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 876,\n          columnNumber: 11\n        }, this);\n      case 5:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"WORK HISTORY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 13\n          }, this), workExperience.map((experience, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"work-experience-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Work Experience \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Employer's Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.employerName,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].employerName = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1014,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.address,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].address = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"From Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.fromDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fromDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"To Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.toDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].toDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Position Held\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.lastPosition,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastPosition = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Designation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.designation,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].designation = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nature of Duties\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.natureOfDuties,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].natureOfDuties = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Name & Designation of Immediate Superior\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.immediateSuperior,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].immediateSuperior = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1094,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Emoluments (Rs. Per month at joining)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1107,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossEmoluments,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossEmoluments = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Drawn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1119,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.lastDrawn,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastDrawn = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Basic Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1131,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.basicSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].basicSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1132,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fixed Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.fixedSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fixedSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Variable Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1155,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.variableSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].variableSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1156,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1168,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1166,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addWorkExperience,\n            className: \"btn-add\",\n            children: \"Add Work Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1005,\n          columnNumber: 11\n        }, this);\n      case 6:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"GENERAL DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Previous Interview with MH Group\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Have you ever been interviewed by any of the MH Group of Companies?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"true\",\n                      checked: previousEmployment.hasBeenInterviewed === true,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1199,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"false\",\n                      checked: previousEmployment.hasBeenInterviewed === false,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1209,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1208,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1195,\n                columnNumber: 17\n              }, this), previousEmployment.hasBeenInterviewed && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date/Year\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1223,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewDate,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewDate: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1224,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1231,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewPosition,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewPosition: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Company\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewCompany,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewCompany: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1240,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1238,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"References & Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relatives/Acquaintance in MH Group - Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1255,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.name,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      name: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relationship\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.relationship,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      relationship: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1267,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.position,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      position: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Company & Phone No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.companyAndPhone,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      companyAndPhone: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"How did you come to know of this position?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.howDidYouKnow,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    howDidYouKnow: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1300,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Are you engaged in any personal business?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1306,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"true\",\n                      checked: references.personalBusiness === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1309,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1308,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"false\",\n                      checked: references.personalBusiness === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1319,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1318,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1305,\n                columnNumber: 17\n              }, this), references.personalBusiness && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, indicate nature of business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.businessNature,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    businessNature: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1333,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Do you have any contract/bond with your previous employer?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1341,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"true\",\n                      checked: references.contractWithPreviousEmployer === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1344,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"false\",\n                      checked: references.contractWithPreviousEmployer === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1354,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1353,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1340,\n                columnNumber: 17\n              }, this), references.contractWithPreviousEmployer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, Give Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1367,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.contractDetails,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    contractDetails: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If selected, when can you join?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: references.whenCanJoin,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    whenCanJoin: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1375,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1189,\n          columnNumber: 11\n        }, this);\n      case 7:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"CRIMINAL RECORDS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1391,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been involved in any criminal proceedings?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenInvolved === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1398,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenInvolved === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1407,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1394,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenInvolved && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1421,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.details,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  details: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1422,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1420,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been convicted of any offence?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenConvicted === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1433,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1432,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenConvicted === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1443,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1442,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1431,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1429,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenConvicted && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.convictionDetails,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  convictionDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1457,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1455,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1390,\n          columnNumber: 11\n        }, this);\n      case 8:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"DECLARATION FORM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-text\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"I hereby declare that:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1475,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1475,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1474,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1473,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: declaration.notConnectedToDirectors,\n                  onChange: e => setDeclaration(prev => ({\n                    ...prev,\n                    notConnectedToDirectors: e.target.checked\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1481,\n                  columnNumber: 19\n                }, this), \"I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1480,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"OR I am a partner or relative of a Director of the Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1491,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"true\",\n                    checked: declaration.isPartnerOrRelative === true,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1494,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1493,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"false\",\n                    checked: declaration.isPartnerOrRelative === false,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1504,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1503,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1492,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1490,\n              columnNumber: 15\n            }, this), declaration.isPartnerOrRelative && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, provide details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1518,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: declaration.partnerRelativeDetails,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  partnerRelativeDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1517,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1527,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: declaration.place,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  place: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1528,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: declaration.date,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  date: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1537,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Applicant's Signature\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1545,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Type your full name as signature\",\n                value: declaration.applicantSignature,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  applicantSignature: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1546,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-note\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"em\", {\n                    children: \"Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1556,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1556,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1555,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1554,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1472,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1469,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Step \", currentStep]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1564,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"onboarding-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Employee Onboarding Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1577,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-fill\",\n          style: {\n            width: `${currentStep / totalSteps * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1579,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1578,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Step \", currentStep, \" of \", totalSteps, \": \", getStepTitle(currentStep)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1584,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1576,\n      columnNumber: 7\n    }, this), (successMessage || errorMessage) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: successMessage ? \"success-message\" : \"error-message\",\n          children: successMessage || errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1592,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1595,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1591,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1590,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: renderStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1600,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-navigation\",\n      children: [currentStep > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: prevStep,\n        className: \"btn-secondary\",\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1606,\n        columnNumber: 11\n      }, this), currentStep < totalSteps ? /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: nextStep,\n        className: \"btn-primary\",\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1611,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: () => {\n          const form = document.querySelector('form');\n          if (form) {\n            form.requestSubmit();\n          }\n        },\n        className: \"btn-primary\",\n        children: \"Submit Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1615,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1604,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1575,\n    columnNumber: 5\n  }, this);\n};\n_s(OnboardingForm, \"AgUAYVn8ElFIrMsKrOrl216/qXg=\", false, function () {\n  return [useParams];\n});\n_c = OnboardingForm;\nexport default OnboardingForm;\nvar _c;\n$RefreshReg$(_c, \"OnboardingForm\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OnboardingForm", "_s", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "token", "personalDetails", "setPersonalDetails", "firstName", "middleName", "lastName", "dateOfBirth", "gender", "birthPlace", "age", "marriageDate", "maritalStatus", "nationality", "religion", "nativeState", "contactNumber", "emergencyContactNo", "stateOfDomicile", "email", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "present<PERSON>in", "permanent<PERSON>in", "passportPhoto", "languageSkills", "setLanguageSkills", "language", "speak", "read", "write", "documentDetails", "setDocumentDetails", "passportNo", "passportIssueDate", "validUptoDate", "countryOfIssue", "validVisaDetails", "panNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "family<PERSON><PERSON>bers", "setFamilyMembers", "name", "relationship", "qualification", "occupation", "organisationAndPosition", "physicalDetails", "setPhysicalDetails", "height", "weight", "bloodGroup", "eyesightRight", "eyesightLeft", "physicalDisability", "identificationMark", "previousEmployment", "setPreviousEmployment", "hasBeenInterviewed", "interviewDate", "interviewPosition", "interviewCompany", "references", "setReferences", "relativesInCompany", "position", "companyAndPhone", "howDidYouKnow", "personalBusiness", "businessNature", "contractWithPreviousEmployer", "contractDetails", "whenCanJoin", "workExperience", "setWorkExperience", "employerName", "address", "duration", "fromDate", "toDate", "lastPosition", "designation", "natureOfDuties", "immediateSuperior", "grossEmoluments", "lastDrawn", "basicSalary", "fixedSalary", "variableSalary", "grossSalary", "education", "setEducation", "examination", "specialisation", "schoolCollege", "university", "fullTimePartTime", "monthYearPassing", "gradeMarks", "distinctions", "currentStep", "setCurrentStep", "totalSteps", "criminalRecords", "setCriminalRecords", "hasBeenInvolved", "details", "hasBeenConvicted", "convictionDetails", "declaration", "setDeclaration", "notConnectedToDirectors", "isPartnerOrRelative", "partnerRelativeDetails", "place", "date", "applicantSignature", "handlePersonalDetailsChange", "field", "value", "prev", "handleDocumentDetailsChange", "handlePhysicalDetailsChange", "addLanguageSkill", "addFamilyMember", "addWorkExperience", "addEducation", "getStepTitle", "step", "titles", "nextStep", "console", "log", "prevStep", "validateRequired<PERSON><PERSON>s", "_personalDetails$firs", "_personalDetails$last", "_personalDetails$date", "_personalDetails$gend", "_personalDetails$nati", "_personalDetails$cont", "_personalDetails$emer", "requiredFields", "trim", "every", "Boolean", "handleSubmit", "event", "preventDefault", "allFormData", "url", "post", "form_data", "error", "renderStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "onChange", "e", "target", "map", "skill", "index", "newSkills", "checked", "onClick", "member", "newMembers", "edu", "newEducation", "experience", "newExperience", "placeholder", "closeModal", "style", "width", "onSubmit", "form", "document", "querySelector", "requestSubmit", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\n\ninterface PersonalDetails {\n  firstName: string;\n  middleName: string;\n  lastName: string;\n  dateOfBirth: string;\n  gender: string;\n  birthPlace: string;\n  age: string;\n  marriageDate: string;\n  maritalStatus: string;\n  nationality: string;\n  religion: string;\n  nativeState: string;\n  contactNumber: string;\n  emergencyContactNo: string;\n  stateOfDomicile: string;\n  email: string;\n  presentAddress: string;\n  permanentAddress: string;\n  presentPin: string;\n  permanentPin: string;\n  passportPhoto: File | null;\n}\n\ninterface LanguageSkills {\n  language: string;\n  speak: boolean;\n  read: boolean;\n  write: boolean;\n}\n\ninterface DocumentDetails {\n  passportNo: string;\n  passportIssueDate: string;\n  validUptoDate: string;\n  countryOfIssue: string;\n  validVisaDetails: string;\n  panNumber: string;\n  aadharNumber: string;\n  passportFile: File | null;\n  panFile: File | null;\n  aadharFile: File | null;\n}\n\ninterface FamilyMember {\n  name: string;\n  relationship: string;\n  dateOfBirth: string;\n  qualification: string;\n  occupation: string;\n  organisationAndPosition: string;\n}\n\ninterface PhysicalDetails {\n  height: string;\n  weight: string;\n  bloodGroup: string;\n  eyesightRight: string;\n  eyesightLeft: string;\n  physicalDisability: string;\n  identificationMark: string;\n}\n\ninterface PreviousEmployment {\n  hasBeenInterviewed: boolean;\n  interviewDate: string;\n  interviewPosition: string;\n  interviewCompany: string;\n}\n\ninterface References {\n  relativesInCompany: {\n    name: string;\n    relationship: string;\n    position: string;\n    companyAndPhone: string;\n  };\n  howDidYouKnow: string;\n  personalBusiness: boolean;\n  businessNature: string;\n  contractWithPreviousEmployer: boolean;\n  contractDetails: string;\n  whenCanJoin: string;\n}\n\ninterface WorkExperience {\n  employerName: string;\n  address: string;\n  duration: string;\n  fromDate: string;\n  toDate: string;\n  lastPosition: string;\n  designation: string;\n  natureOfDuties: string;\n  immediateSuperior: string;\n  grossEmoluments: string;\n  lastDrawn: string;\n  basicSalary: string;\n  fixedSalary: string;\n  variableSalary: string;\n  grossSalary: string;\n}\n\ninterface Education {\n  examination: string;\n  specialisation: string;\n  schoolCollege: string;\n  university: string;\n  fullTimePartTime: string;\n  duration: string;\n  monthYearPassing: string;\n  gradeMarks: string;\n  distinctions: string;\n}\n\nconst OnboardingForm: React.FC = () => {\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [errorMessage, setErrorMessage] = useState<string | null>(null);\n  const { token } = useParams<{ token: string }>();\n  const [personalDetails, setPersonalDetails] = useState<PersonalDetails>({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n\n  const [languageSkills, setLanguageSkills] = useState<LanguageSkills[]>([\n    { language: '', speak: false, read: false, write: false }\n  ]);\n\n  const [documentDetails, setDocumentDetails] = useState<DocumentDetails>({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: ''\n  });\n\n  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([\n    { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }\n  ]);\n\n  const [physicalDetails, setPhysicalDetails] = useState<PhysicalDetails>({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n\n  const [previousEmployment, setPreviousEmployment] = useState<PreviousEmployment>({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n\n  const [references, setReferences] = useState<References>({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n\n  const [workExperience, setWorkExperience] = useState<WorkExperience[]>([\n    {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }\n  ]);\n\n  const [education, setEducation] = useState<Education[]>([\n    {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: ''\n    }\n  ]);\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n\n  const handlePersonalDetailsChange = (field: keyof PersonalDetails, value: string) => {\n    setPersonalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleDocumentDetailsChange = (field: keyof DocumentDetails, value: string) => {\n    setDocumentDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handlePhysicalDetailsChange = (field: keyof PhysicalDetails, value: string) => {\n    setPhysicalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, { language: '', speak: false, read: false, write: false }]);\n  };\n\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }]);\n  };\n\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: ''\n    }]);\n  };\n\n  const getStepTitle = (step: number) => {\n    const titles = [\n      '',\n      'PERSONAL DETAILS',\n      'FAMILY DATA',\n      'HEALTH DATA',\n      'EDUCATION DETAILS',\n      'WORK HISTORY DATA',\n      'GENERAL DATA',\n      'CRIMINAL RECORDS',\n      'DECLARATION FORM'\n    ];\n    return titles[step] || '';\n  };\n\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    const requiredFields = [\n      personalDetails.firstName?.trim(),\n      personalDetails.lastName?.trim(),\n      personalDetails.dateOfBirth?.trim(),\n      personalDetails.gender?.trim(),\n      personalDetails.nationality?.trim(),\n      personalDetails.contactNumber?.trim(),\n      personalDetails.emergencyContactNo?.trim(),\n    ];\n    return requiredFields.every(Boolean);\n  };\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n    // Collect all form data into a single object\n    const allFormData = {\n      personalDetails,\n      languageSkills,\n      documentDetails,\n      familyMembers,\n      physicalDetails,\n      previousEmployment,\n      references,\n      workExperience,\n      education,\n      criminalRecords,\n      declaration\n    };\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      // Send as { form_data: ... }\n      await axios.post(url, { form_data: allFormData });\n      setSuccessMessage(\"Form submitted successfully!\");\n      setErrorMessage(null);\n    } catch (error) {\n      setErrorMessage(\"Failed to submit the form. Please try again later.\");\n      setSuccessMessage(null);\n    }\n  };\n\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"form-section\">\n            <h3>PERSONAL DETAILS</h3>\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>First Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.firstName}\n                  onChange={(e) => handlePersonalDetailsChange('firstName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Middle Name</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.middleName}\n                  onChange={(e) => handlePersonalDetailsChange('middleName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Last Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.lastName}\n                  onChange={(e) => handlePersonalDetailsChange('lastName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Date of Birth *</label>\n                <input\n                  type=\"date\"\n                  required\n                  value={personalDetails.dateOfBirth}\n                  onChange={(e) => handlePersonalDetailsChange('dateOfBirth', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Gender *</label>\n                <select\n                  required\n                  value={personalDetails.gender}\n                  onChange={(e) => handlePersonalDetailsChange('gender', e.target.value)}\n                >\n                  <option value=\"\">Select Gender</option>\n                  <option value=\"Male\">Male</option>\n                  <option value=\"Female\">Female</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Birth Place</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.birthPlace}\n                  onChange={(e) => handlePersonalDetailsChange('birthPlace', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Age</label>\n                <input\n                  type=\"number\"\n                  value={personalDetails.age}\n                  onChange={(e) => handlePersonalDetailsChange('age', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marriage Date</label>\n                <input\n                  type=\"date\"\n                  value={personalDetails.marriageDate}\n                  onChange={(e) => handlePersonalDetailsChange('marriageDate', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marital Status</label>\n                <select\n                  value={personalDetails.maritalStatus}\n                  onChange={(e) => handlePersonalDetailsChange('maritalStatus', e.target.value)}\n                >\n                  <option value=\"\">Select Status</option>\n                  <option value=\"Single\">Single</option>\n                  <option value=\"Married\">Married</option>\n                  <option value=\"Divorced\">Divorced</option>\n                  <option value=\"Widowed\">Widowed</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Nationality *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.nationality}\n                  onChange={(e) => handlePersonalDetailsChange('nationality', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Religion</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.religion}\n                  onChange={(e) => handlePersonalDetailsChange('religion', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Native State</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.nativeState}\n                  onChange={(e) => handlePersonalDetailsChange('nativeState', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Contact Number *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.contactNumber}\n                  onChange={(e) => handlePersonalDetailsChange('contactNumber', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Emergency Contact No. *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.emergencyContactNo}\n                  onChange={(e) => handlePersonalDetailsChange('emergencyContactNo', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>State of Domicile</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.stateOfDomicile}\n                  onChange={(e) => handlePersonalDetailsChange('stateOfDomicile', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Email *</label>\n                <input\n                  type=\"email\"\n                  required\n                  value={personalDetails.email}\n                  onChange={(e) => handlePersonalDetailsChange('email', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Present Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.presentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('presentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Permanent Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.permanentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('permanentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Present PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.presentPin}\n                  onChange={(e) => handlePersonalDetailsChange('presentPin', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Permanent PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.permanentPin}\n                  onChange={(e) => handlePersonalDetailsChange('permanentPin', e.target.value)}\n                />\n              </div>\n\n              <div className=\"subsection full-width\">\n                <h4>Document Details</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Passport No.</label>\n                    <input\n                      type=\"text\"\n                      value={documentDetails.passportNo}\n                      onChange={(e) => handleDocumentDetailsChange('passportNo', e.target.value)}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Passport Issue Date</label>\n                    <input\n                      type=\"date\"\n                      value={documentDetails.passportIssueDate}\n                      onChange={(e) => handleDocumentDetailsChange('passportIssueDate', e.target.value)}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Valid Upto Date</label>\n                    <input\n                      type=\"date\"\n                      value={documentDetails.validUptoDate}\n                      onChange={(e) => handleDocumentDetailsChange('validUptoDate', e.target.value)}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Country of Issue</label>\n                    <input\n                      type=\"text\"\n                      value={documentDetails.countryOfIssue}\n                      onChange={(e) => handleDocumentDetailsChange('countryOfIssue', e.target.value)}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Valid Visa Details</label>\n                    <input\n                      type=\"text\"\n                      value={documentDetails.validVisaDetails}\n                      onChange={(e) => handleDocumentDetailsChange('validVisaDetails', e.target.value)}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>PAN Number *</label>\n                    <input\n                      type=\"text\"\n                      required\n                      value={documentDetails.panNumber}\n                      onChange={(e) => handleDocumentDetailsChange('panNumber', e.target.value)}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Aadhar Number *</label>\n                    <input\n                      type=\"text\"\n                      required\n                      value={documentDetails.aadharNumber}\n                      onChange={(e) => handleDocumentDetailsChange('aadharNumber', e.target.value)}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"subsection full-width\">\n                <h4>Language Known</h4>\n                {languageSkills.map((skill, index) => (\n                  <div key={index} className=\"language-skill-row\">\n                    <div className=\"form-group\">\n                      <label>Language</label>\n                      <input\n                        type=\"text\"\n                        value={skill.language}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].language = e.target.value;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                    </div>\n                    <div className=\"checkbox-group\">\n                      <label>\n                        <input\n                          type=\"checkbox\"\n                          checked={skill.speak}\n                          onChange={(e) => {\n                            const newSkills = [...languageSkills];\n                            newSkills[index].speak = e.target.checked;\n                            setLanguageSkills(newSkills);\n                          }}\n                        />\n                        Speak\n                      </label>\n                      <label>\n                        <input\n                          type=\"checkbox\"\n                          checked={skill.read}\n                          onChange={(e) => {\n                            const newSkills = [...languageSkills];\n                            newSkills[index].read = e.target.checked;\n                            setLanguageSkills(newSkills);\n                          }}\n                        />\n                        Read\n                      </label>\n                      <label>\n                        <input\n                          type=\"checkbox\"\n                          checked={skill.write}\n                          onChange={(e) => {\n                            const newSkills = [...languageSkills];\n                            newSkills[index].write = e.target.checked;\n                            setLanguageSkills(newSkills);\n                          }}\n                        />\n                        Write\n                      </label>\n                    </div>\n                  </div>\n                ))}\n                <button type=\"button\" onClick={addLanguageSkill} className=\"btn-add\">\n                  Add Language\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n      case 2:\n        return (\n          <div className=\"form-section\">\n            <h3>FAMILY DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Family Members</h4>\n              {familyMembers.map((member, index) => (\n                <div key={index} className=\"family-member-section\">\n                  <div className=\"form-grid\">\n                    <div className=\"form-group\">\n                      <label>Name</label>\n                      <input\n                        type=\"text\"\n                        value={member.name}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].name = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Relationship</label>\n                      <input\n                        type=\"text\"\n                        value={member.relationship}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].relationship = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Date of Birth</label>\n                      <input\n                        type=\"date\"\n                        value={member.dateOfBirth}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].dateOfBirth = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Qualification</label>\n                      <input\n                        type=\"text\"\n                        value={member.qualification}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].qualification = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Occupation</label>\n                      <input\n                        type=\"text\"\n                        value={member.occupation}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].occupation = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Organisation and Position</label>\n                      <input\n                        type=\"text\"\n                        value={member.organisationAndPosition}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].organisationAndPosition = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addFamilyMember} className=\"btn-add\">\n                Add Family Member\n              </button>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"form-section\">\n            <h3>HEALTH DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Physical Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Height</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.height}\n                    onChange={(e) => handlePhysicalDetailsChange('height', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Weight</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.weight}\n                    onChange={(e) => handlePhysicalDetailsChange('weight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Blood Group</label>\n                  <select\n                    value={physicalDetails.bloodGroup}\n                    onChange={(e) => handlePhysicalDetailsChange('bloodGroup', e.target.value)}\n                  >\n                    <option value=\"\">Select Blood Group</option>\n                    <option value=\"A+\">A+</option>\n                    <option value=\"A-\">A-</option>\n                    <option value=\"B+\">B+</option>\n                    <option value=\"B-\">B-</option>\n                    <option value=\"AB+\">AB+</option>\n                    <option value=\"AB-\">AB-</option>\n                    <option value=\"O+\">O+</option>\n                    <option value=\"O-\">O-</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Right</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightRight}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightRight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Left</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightLeft}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightLeft', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Physical Disability</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.physicalDisability}\n                    onChange={(e) => handlePhysicalDetailsChange('physicalDisability', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Identification Mark</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.identificationMark}\n                    onChange={(e) => handlePhysicalDetailsChange('identificationMark', e.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"form-section\">\n            <h3>EDUCATION DETAILS</h3>\n\n            {education.map((edu, index) => (\n              <div key={index} className=\"education-section\">\n                <h4>Education {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Examination Passed</label>\n                    <input\n                      type=\"text\"\n                      value={edu.examination}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].examination = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Specialisation</label>\n                    <input\n                      type=\"text\"\n                      value={edu.specialisation}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].specialisation = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>School/College/Institution</label>\n                    <input\n                      type=\"text\"\n                      value={edu.schoolCollege}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].schoolCollege = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>University/Board</label>\n                    <input\n                      type=\"text\"\n                      value={edu.university}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].university = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Full Time/Part Time/Correspondence</label>\n                    <select\n                      value={edu.fullTimePartTime}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].fullTimePartTime = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    >\n                      <option value=\"\">Select Type</option>\n                      <option value=\"Full Time\">Full Time</option>\n                      <option value=\"Part Time\">Part Time</option>\n                      <option value=\"Correspondence\">Correspondence</option>\n                    </select>\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Duration of Course</label>\n                    <input\n                      type=\"text\"\n                      value={edu.duration}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].duration = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Month & Year of Passing</label>\n                    <input\n                      type=\"text\"\n                      value={edu.monthYearPassing}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].monthYearPassing = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Grade/% Marks</label>\n                    <input\n                      type=\"text\"\n                      value={edu.gradeMarks}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].gradeMarks = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Distinctions/Scholarships/Prizes Won</label>\n                    <textarea\n                      value={edu.distinctions}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].distinctions = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addEducation} className=\"btn-add\">\n              Add Education\n            </button>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"form-section\">\n            <h3>WORK HISTORY DATA</h3>\n\n            {workExperience.map((experience, index) => (\n              <div key={index} className=\"work-experience-section\">\n                <h4>Work Experience {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Employer's Name</label>\n                    <input\n                      type=\"text\"\n                      value={experience.employerName}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].employerName = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Address</label>\n                    <textarea\n                      value={experience.address}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].address = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>From Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.fromDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fromDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>To Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.toDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].toDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Position Held</label>\n                    <input\n                      type=\"text\"\n                      value={experience.lastPosition}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastPosition = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Designation</label>\n                    <input\n                      type=\"text\"\n                      value={experience.designation}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].designation = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Nature of Duties</label>\n                    <textarea\n                      value={experience.natureOfDuties}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].natureOfDuties = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Name & Designation of Immediate Superior</label>\n                    <input\n                      type=\"text\"\n                      value={experience.immediateSuperior}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].immediateSuperior = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Emoluments (Rs. Per month at joining)</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossEmoluments}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossEmoluments = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Drawn</label>\n                    <input\n                      type=\"number\"\n                      value={experience.lastDrawn}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastDrawn = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Basic Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.basicSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].basicSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Fixed Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.fixedSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fixedSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Variable Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.variableSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].variableSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addWorkExperience} className=\"btn-add\">\n              Add Work Experience\n            </button>\n          </div>\n        );\n\n      case 6:\n        return (\n          <div className=\"form-section\">\n            <h3>GENERAL DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Previous Interview with MH Group</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Have you ever been interviewed by any of the MH Group of Companies?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"true\"\n                        checked={previousEmployment.hasBeenInterviewed === true}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"false\"\n                        checked={previousEmployment.hasBeenInterviewed === false}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {previousEmployment.hasBeenInterviewed && (\n                  <>\n                    <div className=\"form-group\">\n                      <label>Date/Year</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewDate}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewDate: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Position</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewPosition}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewPosition: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Company</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewCompany}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewCompany: e.target.value }))}\n                      />\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>References & Additional Information</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Relatives/Acquaintance in MH Group - Name</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.name}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, name: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Relationship</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.relationship}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, relationship: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Position</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.position}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, position: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Company & Phone No.</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.companyAndPhone}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, companyAndPhone: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group full-width\">\n                  <label>How did you come to know of this position?</label>\n                  <textarea\n                    value={references.howDidYouKnow}\n                    onChange={(e) => setReferences(prev => ({ ...prev, howDidYouKnow: e.target.value }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Are you engaged in any personal business?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"true\"\n                        checked={references.personalBusiness === true}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"false\"\n                        checked={references.personalBusiness === false}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.personalBusiness && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, indicate nature of business</label>\n                    <textarea\n                      value={references.businessNature}\n                      onChange={(e) => setReferences(prev => ({ ...prev, businessNature: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>Do you have any contract/bond with your previous employer?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"true\"\n                        checked={references.contractWithPreviousEmployer === true}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"false\"\n                        checked={references.contractWithPreviousEmployer === false}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.contractWithPreviousEmployer && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, Give Details</label>\n                    <textarea\n                      value={references.contractDetails}\n                      onChange={(e) => setReferences(prev => ({ ...prev, contractDetails: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>If selected, when can you join?</label>\n                  <input\n                    type=\"date\"\n                    value={references.whenCanJoin}\n                    onChange={(e) => setReferences(prev => ({ ...prev, whenCanJoin: e.target.value }))}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 7:\n        return (\n          <div className=\"form-section\">\n            <h3>CRIMINAL RECORDS</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Have you ever been involved in any criminal proceedings?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenInvolved === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenInvolved === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenInvolved && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.details}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, details: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Have you ever been convicted of any offence?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenConvicted === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenConvicted === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenConvicted && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.convictionDetails}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, convictionDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        );\n\n      case 8:\n        return (\n          <div className=\"form-section\">\n            <h3>DECLARATION FORM</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-text\">\n                  <p><strong>I hereby declare that:</strong></p>\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>\n                  <input\n                    type=\"checkbox\"\n                    checked={declaration.notConnectedToDirectors}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, notConnectedToDirectors: e.target.checked }))}\n                  />\n                  I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\n                </label>\n              </div>\n\n              <div className=\"form-group\">\n                <label>OR I am a partner or relative of a Director of the Company</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"true\"\n                      checked={declaration.isPartnerOrRelative === true}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"false\"\n                      checked={declaration.isPartnerOrRelative === false}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n\n              {declaration.isPartnerOrRelative && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, provide details</label>\n                  <textarea\n                    value={declaration.partnerRelativeDetails}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, partnerRelativeDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Place</label>\n                <input\n                  type=\"text\"\n                  value={declaration.place}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, place: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label>Date</label>\n                <input\n                  type=\"date\"\n                  value={declaration.date}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, date: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>Applicant's Signature</label>\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your full name as signature\"\n                  value={declaration.applicantSignature}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, applicantSignature: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-note\">\n                  <p><em>Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.</em></p>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step {currentStep}</div>;\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n\n  return (\n    <div className=\"onboarding-form\">\n      <div className=\"form-header\">\n        <h2>Employee Onboarding Form</h2>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress-fill\"\n            style={{ width: `${(currentStep / totalSteps) * 100}%` }}\n          ></div>\n        </div>\n        <p>Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}</p>\n      </div>\n\n\n      {/* Popup Modal for feedback messages */}\n      {(successMessage || errorMessage) && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content\">\n            <div className={successMessage ? \"success-message\" : \"error-message\"}>\n              {successMessage || errorMessage}\n            </div>\n            <button className=\"modal-close\" onClick={closeModal}>Close</button>\n          </div>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        {renderStep()}\n      </form>\n\n      <div className=\"form-navigation\">\n        {currentStep > 1 && (\n          <button type=\"button\" onClick={prevStep} className=\"btn-secondary\">\n            Previous\n          </button>\n        )}\n        {currentStep < totalSteps ? (\n          <button type=\"button\" onClick={nextStep} className=\"btn-primary\">\n            Next\n          </button>\n        ) : (\n          <button type=\"button\" onClick={() => {\n            const form = document.querySelector('form');\n            if (form) {\n              form.requestSubmit();\n            }\n          }} className=\"btn-primary\">\n            Submit Form\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default OnboardingForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAC7B,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqH7C,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM;IAAEa;EAAM,CAAC,GAAGX,SAAS,CAAoB,CAAC;EAChD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAkB;IACtEgB,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,GAAG,EAAE,EAAE;IACPC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAmB,CACrE;IAAEuC,QAAQ,EAAE,EAAE;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC1D,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAkB;IACtE6C,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAiB,CACjE;IAAEsD,IAAI,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEpC,WAAW,EAAE,EAAE;IAAEqC,aAAa,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,uBAAuB,EAAE;EAAG,CAAC,CAChH,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAkB;IACtE6D,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAqB;IAC/EsE,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,EAAE;IACrBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAa;IACvD4E,kBAAkB,EAAE;MAClBtB,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBsB,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC;IACDC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,cAAc,EAAE,EAAE;IAClBC,4BAA4B,EAAE,KAAK;IACnCC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAmB,CACrE;IACEuF,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvG,QAAQ,CAAc,CACtD;IACEwG,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBnB,QAAQ,EAAE,EAAE;IACZoB,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE;EAChB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAMkH,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpH,QAAQ,CAAC;IACrDqH,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,KAAK;IACvBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1H,QAAQ,CAAC;IAC7C2H,uBAAuB,EAAE,KAAK;IAC9BC,mBAAmB,EAAE,KAAK;IAC1BC,sBAAsB,EAAE,EAAE;IAC1BC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,2BAA2B,GAAGA,CAACC,KAA4B,EAAEC,KAAa,KAAK;IACnFpH,kBAAkB,CAACqH,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,2BAA2B,GAAGA,CAACH,KAA4B,EAAEC,KAAa,KAAK;IACnFvF,kBAAkB,CAACwF,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,2BAA2B,GAAGA,CAACJ,KAA4B,EAAEC,KAAa,KAAK;IACnFvE,kBAAkB,CAACwE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjG,iBAAiB,CAAC8F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAE7F,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,CAAC,CAAC;EACjG,CAAC;EAED,MAAM8F,eAAe,GAAGA,CAAA,KAAM;IAC5BnF,gBAAgB,CAAC+E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAE9E,IAAI,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEpC,WAAW,EAAE,EAAE;MAAEqC,aAAa,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,uBAAuB,EAAE;IAAG,CAAC,CAAC,CAAC;EACtJ,CAAC;EAED,MAAM+E,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnD,iBAAiB,CAAC8C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAClC7C,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMqC,YAAY,GAAGA,CAAA,KAAM;IACzBnC,YAAY,CAAC6B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC7B5B,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,EAAE;MACpBnB,QAAQ,EAAE,EAAE;MACZoB,gBAAgB,EAAE,EAAE;MACpBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,YAAY,GAAIC,IAAY,IAAK;IACrC,MAAMC,MAAM,GAAG,CACb,EAAE,EACF,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,kBAAkB,CACnB;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAI,EAAE;EAC3B,CAAC;EAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEhC,WAAW,EAAE,cAAc,EAAEE,UAAU,CAAC;IACtF,IAAIF,WAAW,GAAGE,UAAU,EAAE;MAC5BD,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;MAC/B+B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEhC,WAAW,GAAG,CAAC,CAAC;IACjD,CAAC,MAAM;MACL+B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIjC,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMkC,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACnC,MAAMC,cAAc,GAAG,EAAAP,qBAAA,GACrBrI,eAAe,CAACE,SAAS,cAAAmI,qBAAA,uBAAzBA,qBAAA,CAA2BQ,IAAI,CAAC,CAAC,GAAAP,qBAAA,GACjCtI,eAAe,CAACI,QAAQ,cAAAkI,qBAAA,uBAAxBA,qBAAA,CAA0BO,IAAI,CAAC,CAAC,GAAAN,qBAAA,GAChCvI,eAAe,CAACK,WAAW,cAAAkI,qBAAA,uBAA3BA,qBAAA,CAA6BM,IAAI,CAAC,CAAC,GAAAL,qBAAA,GACnCxI,eAAe,CAACM,MAAM,cAAAkI,qBAAA,uBAAtBA,qBAAA,CAAwBK,IAAI,CAAC,CAAC,GAAAJ,qBAAA,GAC9BzI,eAAe,CAACW,WAAW,cAAA8H,qBAAA,uBAA3BA,qBAAA,CAA6BI,IAAI,CAAC,CAAC,GAAAH,qBAAA,GACnC1I,eAAe,CAACc,aAAa,cAAA4H,qBAAA,uBAA7BA,qBAAA,CAA+BG,IAAI,CAAC,CAAC,GAAAF,qBAAA,GACrC3I,eAAe,CAACe,kBAAkB,cAAA4H,qBAAA,uBAAlCA,qBAAA,CAAoCE,IAAI,CAAC,CAAC,CAC3C;IACD,OAAOD,cAAc,CAACE,KAAK,CAACC,OAAO,CAAC;EACtC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,KAAsB,IAAK;IACrDA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBtJ,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;IACrB;IACA,IAAI,CAACsI,sBAAsB,CAAC,CAAC,EAAE;MAC7BtI,eAAe,CAAC,2EAA2E,CAAC;MAC5F;IACF;IACA,IAAIoG,WAAW,KAAKE,UAAU,EAAE;MAC9BtG,eAAe,CAAC,uDAAuD,CAAC;MACxE;IACF;IACA;IACA,MAAMqJ,WAAW,GAAG;MAClBnJ,eAAe;MACfuB,cAAc;MACdM,eAAe;MACfS,aAAa;MACbO,eAAe;MACfS,kBAAkB;MAClBM,UAAU;MACVW,cAAc;MACdiB,SAAS;MACTa,eAAe;MACfM;IACF,CAAC;IACD,IAAI;MACF,MAAMyC,GAAG,GAAG,wDAAwDrJ,KAAK,GAAG;MAC5E;MACA,MAAMZ,KAAK,CAACkK,IAAI,CAACD,GAAG,EAAE;QAAEE,SAAS,EAAEH;MAAY,CAAC,CAAC;MACjDvJ,iBAAiB,CAAC,8BAA8B,CAAC;MACjDE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOyJ,KAAK,EAAE;MACdzJ,eAAe,CAAC,oDAAoD,CAAC;MACrEF,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAM4J,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQtD,WAAW;MACjB,KAAK,CAAC;QACJ,oBACE5G,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBxK,OAAA;YAAKmK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACE,SAAU;gBACjC+J,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,WAAW,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACG,UAAW;gBAClC8J,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,YAAY,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACI,QAAS;gBAChC6J,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,UAAU,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACK,WAAY;gBACnC4J,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,aAAa,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBxK,OAAA;gBACE0K,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACM,MAAO;gBAC9B2J,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,QAAQ,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;gBAAAqC,QAAA,gBAEvEpK,OAAA;kBAAQ+H,KAAK,EAAC,EAAE;kBAAAqC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCxK,OAAA;kBAAQ+H,KAAK,EAAC,MAAM;kBAAAqC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCxK,OAAA;kBAAQ+H,KAAK,EAAC,QAAQ;kBAAAqC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCxK,OAAA;kBAAQ+H,KAAK,EAAC,OAAO;kBAAAqC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACO,UAAW;gBAClC0J,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,YAAY,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClBxK,OAAA;gBACEyK,IAAI,EAAC,QAAQ;gBACb1C,KAAK,EAAErH,eAAe,CAACQ,GAAI;gBAC3ByJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,KAAK,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACS,YAAa;gBACpCwJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,cAAc,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7BxK,OAAA;gBACE+H,KAAK,EAAErH,eAAe,CAACU,aAAc;gBACrCuJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,eAAe,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;gBAAAqC,QAAA,gBAE9EpK,OAAA;kBAAQ+H,KAAK,EAAC,EAAE;kBAAAqC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCxK,OAAA;kBAAQ+H,KAAK,EAAC,QAAQ;kBAAAqC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCxK,OAAA;kBAAQ+H,KAAK,EAAC,SAAS;kBAAAqC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCxK,OAAA;kBAAQ+H,KAAK,EAAC,UAAU;kBAAAqC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CxK,OAAA;kBAAQ+H,KAAK,EAAC,SAAS;kBAAAqC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACW,WAAY;gBACnCsJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,aAAa,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACY,QAAS;gBAChCqJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,UAAU,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACa,WAAY;gBACnCoJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,aAAa,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BxK,OAAA;gBACEyK,IAAI,EAAC,KAAK;gBACVC,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACc,aAAc;gBACrCmJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,eAAe,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCxK,OAAA;gBACEyK,IAAI,EAAC,KAAK;gBACVC,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACe,kBAAmB;gBAC1CkJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,oBAAoB,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACgB,eAAgB;gBACvCiJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,iBAAiB,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBxK,OAAA;gBACEyK,IAAI,EAAC,OAAO;gBACZC,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACiB,KAAM;gBAC7BgJ,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,OAAO,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCxK,OAAA;gBACE0K,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACkB,cAAe;gBACtC+I,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,gBAAgB,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAO;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCxK,OAAA;gBACE0K,QAAQ;gBACR3C,KAAK,EAAErH,eAAe,CAACmB,gBAAiB;gBACxC8I,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,kBAAkB,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACoB,UAAW;gBAClC6I,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,YAAY,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAErH,eAAe,CAACqB,YAAa;gBACpC4I,QAAQ,EAAGC,CAAC,IAAK/C,2BAA2B,CAAC,cAAc,EAAE+C,CAAC,CAACC,MAAM,CAAC9C,KAAK;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAI;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBxK,OAAA;gBAAKmK,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAExF,eAAe,CAACE,UAAW;oBAClCkI,QAAQ,EAAGC,CAAC,IAAK3C,2BAA2B,CAAC,YAAY,EAAE2C,CAAC,CAACC,MAAM,CAAC9C,KAAK;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClCxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAExF,eAAe,CAACG,iBAAkB;oBACzCiI,QAAQ,EAAGC,CAAC,IAAK3C,2BAA2B,CAAC,mBAAmB,EAAE2C,CAAC,CAACC,MAAM,CAAC9C,KAAK;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAExF,eAAe,CAACI,aAAc;oBACrCgI,QAAQ,EAAGC,CAAC,IAAK3C,2BAA2B,CAAC,eAAe,EAAE2C,CAAC,CAACC,MAAM,CAAC9C,KAAK;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAExF,eAAe,CAACK,cAAe;oBACtC+H,QAAQ,EAAGC,CAAC,IAAK3C,2BAA2B,CAAC,gBAAgB,EAAE2C,CAAC,CAACC,MAAM,CAAC9C,KAAK;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjCxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAExF,eAAe,CAACM,gBAAiB;oBACxC8H,QAAQ,EAAGC,CAAC,IAAK3C,2BAA2B,CAAC,kBAAkB,EAAE2C,CAAC,CAACC,MAAM,CAAC9C,KAAK;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACXC,QAAQ;oBACR3C,KAAK,EAAExF,eAAe,CAACO,SAAU;oBACjC6H,QAAQ,EAAGC,CAAC,IAAK3C,2BAA2B,CAAC,WAAW,EAAE2C,CAAC,CAACC,MAAM,CAAC9C,KAAK;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACXC,QAAQ;oBACR3C,KAAK,EAAExF,eAAe,CAACQ,YAAa;oBACpC4H,QAAQ,EAAGC,CAAC,IAAK3C,2BAA2B,CAAC,cAAc,EAAE2C,CAAC,CAACC,MAAM,CAAC9C,KAAK;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACtBvI,cAAc,CAAC6I,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/BhL,OAAA;gBAAiBmK,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC7CpK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvBxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAEgD,KAAK,CAAC5I,QAAS;oBACtBwI,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMK,SAAS,GAAG,CAAC,GAAGhJ,cAAc,CAAC;sBACrCgJ,SAAS,CAACD,KAAK,CAAC,CAAC7I,QAAQ,GAAGyI,CAAC,CAACC,MAAM,CAAC9C,KAAK;sBAC1C7F,iBAAiB,CAAC+I,SAAS,CAAC;oBAC9B;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BpK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,UAAU;sBACfS,OAAO,EAAEH,KAAK,CAAC3I,KAAM;sBACrBuI,QAAQ,EAAGC,CAAC,IAAK;wBACf,MAAMK,SAAS,GAAG,CAAC,GAAGhJ,cAAc,CAAC;wBACrCgJ,SAAS,CAACD,KAAK,CAAC,CAAC5I,KAAK,GAAGwI,CAAC,CAACC,MAAM,CAACK,OAAO;wBACzChJ,iBAAiB,CAAC+I,SAAS,CAAC;sBAC9B;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,SAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRxK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,UAAU;sBACfS,OAAO,EAAEH,KAAK,CAAC1I,IAAK;sBACpBsI,QAAQ,EAAGC,CAAC,IAAK;wBACf,MAAMK,SAAS,GAAG,CAAC,GAAGhJ,cAAc,CAAC;wBACrCgJ,SAAS,CAACD,KAAK,CAAC,CAAC3I,IAAI,GAAGuI,CAAC,CAACC,MAAM,CAACK,OAAO;wBACxChJ,iBAAiB,CAAC+I,SAAS,CAAC;sBAC9B;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,QAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRxK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,UAAU;sBACfS,OAAO,EAAEH,KAAK,CAACzI,KAAM;sBACrBqI,QAAQ,EAAGC,CAAC,IAAK;wBACf,MAAMK,SAAS,GAAG,CAAC,GAAGhJ,cAAc,CAAC;wBACrCgJ,SAAS,CAACD,KAAK,CAAC,CAAC1I,KAAK,GAAGsI,CAAC,CAACC,MAAM,CAACK,OAAO;wBACzChJ,iBAAiB,CAAC+I,SAAS,CAAC;sBAC9B;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,SAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAlDEQ,KAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmDV,CACN,CAAC,eACFxK,OAAA;gBAAQyK,IAAI,EAAC,QAAQ;gBAACU,OAAO,EAAEhD,gBAAiB;gBAACgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAErE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,oBACExK,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBxK,OAAA;YAAKmK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpK,OAAA;cAAAoK,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtBxH,aAAa,CAAC8H,GAAG,CAAC,CAACM,MAAM,EAAEJ,KAAK,kBAC/BhL,OAAA;cAAiBmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAChDpK,OAAA;gBAAKmK,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAEqD,MAAM,CAAClI,IAAK;oBACnByH,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,UAAU,GAAG,CAAC,GAAGrI,aAAa,CAAC;sBACrCqI,UAAU,CAACL,KAAK,CAAC,CAAC9H,IAAI,GAAG0H,CAAC,CAACC,MAAM,CAAC9C,KAAK;sBACvC9E,gBAAgB,CAACoI,UAAU,CAAC;oBAC9B;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAEqD,MAAM,CAACjI,YAAa;oBAC3BwH,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,UAAU,GAAG,CAAC,GAAGrI,aAAa,CAAC;sBACrCqI,UAAU,CAACL,KAAK,CAAC,CAAC7H,YAAY,GAAGyH,CAAC,CAACC,MAAM,CAAC9C,KAAK;sBAC/C9E,gBAAgB,CAACoI,UAAU,CAAC;oBAC9B;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAEqD,MAAM,CAACrK,WAAY;oBAC1B4J,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,UAAU,GAAG,CAAC,GAAGrI,aAAa,CAAC;sBACrCqI,UAAU,CAACL,KAAK,CAAC,CAACjK,WAAW,GAAG6J,CAAC,CAACC,MAAM,CAAC9C,KAAK;sBAC9C9E,gBAAgB,CAACoI,UAAU,CAAC;oBAC9B;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAEqD,MAAM,CAAChI,aAAc;oBAC5BuH,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,UAAU,GAAG,CAAC,GAAGrI,aAAa,CAAC;sBACrCqI,UAAU,CAACL,KAAK,CAAC,CAAC5H,aAAa,GAAGwH,CAAC,CAACC,MAAM,CAAC9C,KAAK;sBAChD9E,gBAAgB,CAACoI,UAAU,CAAC;oBAC9B;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzBxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAEqD,MAAM,CAAC/H,UAAW;oBACzBsH,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,UAAU,GAAG,CAAC,GAAGrI,aAAa,CAAC;sBACrCqI,UAAU,CAACL,KAAK,CAAC,CAAC3H,UAAU,GAAGuH,CAAC,CAACC,MAAM,CAAC9C,KAAK;sBAC7C9E,gBAAgB,CAACoI,UAAU,CAAC;oBAC9B;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxCxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAEqD,MAAM,CAAC9H,uBAAwB;oBACtCqH,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMS,UAAU,GAAG,CAAC,GAAGrI,aAAa,CAAC;sBACrCqI,UAAU,CAACL,KAAK,CAAC,CAAC1H,uBAAuB,GAAGsH,CAAC,CAACC,MAAM,CAAC9C,KAAK;sBAC1D9E,gBAAgB,CAACoI,UAAU,CAAC;oBAC9B;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1EEQ,KAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2EV,CACN,CAAC,eACFxK,OAAA;cAAQyK,IAAI,EAAC,QAAQ;cAACU,OAAO,EAAE/C,eAAgB;cAAC+B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExK,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBxK,OAAA;YAAKmK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpK,OAAA;cAAAoK,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBxK,OAAA;cAAKmK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAExE,eAAe,CAACE,MAAO;kBAC9BkH,QAAQ,EAAGC,CAAC,IAAK1C,2BAA2B,CAAC,QAAQ,EAAE0C,CAAC,CAACC,MAAM,CAAC9C,KAAK;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAExE,eAAe,CAACG,MAAO;kBAC9BiH,QAAQ,EAAGC,CAAC,IAAK1C,2BAA2B,CAAC,QAAQ,EAAE0C,CAAC,CAACC,MAAM,CAAC9C,KAAK;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BxK,OAAA;kBACE+H,KAAK,EAAExE,eAAe,CAACI,UAAW;kBAClCgH,QAAQ,EAAGC,CAAC,IAAK1C,2BAA2B,CAAC,YAAY,EAAE0C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;kBAAAqC,QAAA,gBAE3EpK,OAAA;oBAAQ+H,KAAK,EAAC,EAAE;oBAAAqC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CxK,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAqC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BxK,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAqC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BxK,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAqC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BxK,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAqC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BxK,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCxK,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCxK,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAqC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BxK,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAqC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAExE,eAAe,CAACK,aAAc;kBACrC+G,QAAQ,EAAGC,CAAC,IAAK1C,2BAA2B,CAAC,eAAe,EAAE0C,CAAC,CAACC,MAAM,CAAC9C,KAAK;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAExE,eAAe,CAACM,YAAa;kBACpC8G,QAAQ,EAAGC,CAAC,IAAK1C,2BAA2B,CAAC,cAAc,EAAE0C,CAAC,CAACC,MAAM,CAAC9C,KAAK;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAExE,eAAe,CAACO,kBAAmB;kBAC1C6G,QAAQ,EAAGC,CAAC,IAAK1C,2BAA2B,CAAC,oBAAoB,EAAE0C,CAAC,CAACC,MAAM,CAAC9C,KAAK;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAExE,eAAe,CAACQ,kBAAmB;kBAC1C4G,QAAQ,EAAGC,CAAC,IAAK1C,2BAA2B,CAAC,oBAAoB,EAAE0C,CAAC,CAACC,MAAM,CAAC9C,KAAK;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExK,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzBtE,SAAS,CAAC4E,GAAG,CAAC,CAACQ,GAAG,EAAEN,KAAK,kBACxBhL,OAAA;YAAiBmK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC5CpK,OAAA;cAAAoK,QAAA,GAAI,YAAU,EAACY,KAAK,GAAG,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9BxK,OAAA;cAAKmK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEuD,GAAG,CAAClF,WAAY;kBACvBuE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAAC5E,WAAW,GAAGwE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAChD5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEuD,GAAG,CAACjF,cAAe;kBAC1BsE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAAC3E,cAAc,GAAGuE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACnD5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEuD,GAAG,CAAChF,aAAc;kBACzBqE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAAC1E,aAAa,GAAGsE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAClD5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEuD,GAAG,CAAC/E,UAAW;kBACtBoE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAACzE,UAAU,GAAGqE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAC/C5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDxK,OAAA;kBACE+H,KAAK,EAAEuD,GAAG,CAAC9E,gBAAiB;kBAC5BmE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAACxE,gBAAgB,GAAGoE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACrD5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B,CAAE;kBAAAnB,QAAA,gBAEFpK,OAAA;oBAAQ+H,KAAK,EAAC,EAAE;oBAAAqC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCxK,OAAA;oBAAQ+H,KAAK,EAAC,WAAW;oBAAAqC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CxK,OAAA;oBAAQ+H,KAAK,EAAC,WAAW;oBAAAqC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CxK,OAAA;oBAAQ+H,KAAK,EAAC,gBAAgB;oBAAAqC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEuD,GAAG,CAACjG,QAAS;kBACpBsF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAAC3F,QAAQ,GAAGuF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAC7C5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEuD,GAAG,CAAC7E,gBAAiB;kBAC5BkE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAACvE,gBAAgB,GAAGmE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACrD5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEuD,GAAG,CAAC5E,UAAW;kBACtBiE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAACtE,UAAU,GAAGkE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAC/C5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDxK,OAAA;kBACE+H,KAAK,EAAEuD,GAAG,CAAC3E,YAAa;kBACxBgE,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMW,YAAY,GAAG,CAAC,GAAGrF,SAAS,CAAC;oBACnCqF,YAAY,CAACP,KAAK,CAAC,CAACrE,YAAY,GAAGiE,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACjD5B,YAAY,CAACoF,YAAY,CAAC;kBAC5B;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAlHEQ,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmHV,CACN,CAAC,eACFxK,OAAA;YAAQyK,IAAI,EAAC,QAAQ;YAACU,OAAO,EAAE7C,YAAa;YAAC6B,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExK,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzBvF,cAAc,CAAC6F,GAAG,CAAC,CAACU,UAAU,EAAER,KAAK,kBACpChL,OAAA;YAAiBmK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAClDpK,OAAA;cAAAoK,QAAA,GAAI,kBAAgB,EAACY,KAAK,GAAG,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCxK,OAAA;cAAKmK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEyD,UAAU,CAACrG,YAAa;kBAC/BwF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAAC7F,YAAY,GAAGyF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAClD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBxK,OAAA;kBACE+H,KAAK,EAAEyD,UAAU,CAACpG,OAAQ;kBAC1BuF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAAC5F,OAAO,GAAGwF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAC7C7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEyD,UAAU,CAAClG,QAAS;kBAC3BqF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAAC1F,QAAQ,GAAGsF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAC9C7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEyD,UAAU,CAACjG,MAAO;kBACzBoF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACzF,MAAM,GAAGqF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAC5C7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEyD,UAAU,CAAChG,YAAa;kBAC/BmF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACxF,YAAY,GAAGoF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAClD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEyD,UAAU,CAAC/F,WAAY;kBAC9BkF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACvF,WAAW,GAAGmF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACjD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BxK,OAAA;kBACE+H,KAAK,EAAEyD,UAAU,CAAC9F,cAAe;kBACjCiF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACtF,cAAc,GAAGkF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACpD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEyD,UAAU,CAAC7F,iBAAkB;kBACpCgF,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACrF,iBAAiB,GAAGiF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACvD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DxK,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACb1C,KAAK,EAAEyD,UAAU,CAAC5F,eAAgB;kBAClC+E,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACpF,eAAe,GAAGgF,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACrD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBxK,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACb1C,KAAK,EAAEyD,UAAU,CAAC3F,SAAU;kBAC5B8E,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACnF,SAAS,GAAG+E,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBAC/C7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BxK,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACb1C,KAAK,EAAEyD,UAAU,CAAC1F,WAAY;kBAC9B6E,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAAClF,WAAW,GAAG8E,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACjD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BxK,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACb1C,KAAK,EAAEyD,UAAU,CAACzF,WAAY;kBAC9B4E,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAACjF,WAAW,GAAG6E,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACjD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BxK,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACb1C,KAAK,EAAEyD,UAAU,CAACxF,cAAe;kBACjC2E,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAAChF,cAAc,GAAG4E,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACpD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BxK,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACb1C,KAAK,EAAEyD,UAAU,CAACvF,WAAY;kBAC9B0E,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMa,aAAa,GAAG,CAAC,GAAGxG,cAAc,CAAC;oBACzCwG,aAAa,CAACT,KAAK,CAAC,CAAC/E,WAAW,GAAG2E,CAAC,CAACC,MAAM,CAAC9C,KAAK;oBACjD7C,iBAAiB,CAACuG,aAAa,CAAC;kBAClC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAzKEQ,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0KV,CACN,CAAC,eACFxK,OAAA;YAAQyK,IAAI,EAAC,QAAQ;YAACU,OAAO,EAAE9C,iBAAkB;YAAC8B,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExK,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAErBxK,OAAA;YAAKmK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpK,OAAA;cAAAoK,QAAA,EAAI;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCxK,OAAA;cAAKmK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAmE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClFxK,OAAA;kBAAKmK,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BpK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,OAAO;sBACZvH,IAAI,EAAC,oBAAoB;sBACzB6E,KAAK,EAAC,MAAM;sBACZmD,OAAO,EAAElH,kBAAkB,CAACE,kBAAkB,KAAK,IAAK;sBACxDyG,QAAQ,EAAEA,CAAA,KAAM1G,qBAAqB,CAAC+D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE9D,kBAAkB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRxK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,OAAO;sBACZvH,IAAI,EAAC,oBAAoB;sBACzB6E,KAAK,EAAC,OAAO;sBACbmD,OAAO,EAAElH,kBAAkB,CAACE,kBAAkB,KAAK,KAAM;sBACzDyG,QAAQ,EAAEA,CAAA,KAAM1G,qBAAqB,CAAC+D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE9D,kBAAkB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLxG,kBAAkB,CAACE,kBAAkB,iBACpClE,OAAA,CAAAE,SAAA;gBAAAkK,QAAA,gBACEpK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxBxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAE/D,kBAAkB,CAACG,aAAc;oBACxCwG,QAAQ,EAAGC,CAAC,IAAK3G,qBAAqB,CAAC+D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE7D,aAAa,EAAEyG,CAAC,CAACC,MAAM,CAAC9C;oBAAM,CAAC,CAAC;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvBxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAE/D,kBAAkB,CAACI,iBAAkB;oBAC5CuG,QAAQ,EAAGC,CAAC,IAAK3G,qBAAqB,CAAC+D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE5D,iBAAiB,EAAEwG,CAAC,CAACC,MAAM,CAAC9C;oBAAM,CAAC,CAAC;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpK,OAAA;oBAAAoK,QAAA,EAAO;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtBxK,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACX1C,KAAK,EAAE/D,kBAAkB,CAACK,gBAAiB;oBAC3CsG,QAAQ,EAAGC,CAAC,IAAK3G,qBAAqB,CAAC+D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE3D,gBAAgB,EAAEuG,CAAC,CAACC,MAAM,CAAC9C;oBAAM,CAAC,CAAC;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxK,OAAA;YAAKmK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpK,OAAA;cAAAoK,QAAA,EAAI;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CxK,OAAA;cAAKmK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEzD,UAAU,CAACE,kBAAkB,CAACtB,IAAK;kBAC1CyH,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPxD,kBAAkB,EAAE;sBAAE,GAAGwD,IAAI,CAACxD,kBAAkB;sBAAEtB,IAAI,EAAE0H,CAAC,CAACC,MAAM,CAAC9C;oBAAM;kBACzE,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEzD,UAAU,CAACE,kBAAkB,CAACrB,YAAa;kBAClDwH,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPxD,kBAAkB,EAAE;sBAAE,GAAGwD,IAAI,CAACxD,kBAAkB;sBAAErB,YAAY,EAAEyH,CAAC,CAACC,MAAM,CAAC9C;oBAAM;kBACjF,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEzD,UAAU,CAACE,kBAAkB,CAACC,QAAS;kBAC9CkG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPxD,kBAAkB,EAAE;sBAAE,GAAGwD,IAAI,CAACxD,kBAAkB;sBAAEC,QAAQ,EAAEmG,CAAC,CAACC,MAAM,CAAC9C;oBAAM;kBAC7E,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEzD,UAAU,CAACE,kBAAkB,CAACE,eAAgB;kBACrDiG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPxD,kBAAkB,EAAE;sBAAE,GAAGwD,IAAI,CAACxD,kBAAkB;sBAAEE,eAAe,EAAEkG,CAAC,CAACC,MAAM,CAAC9C;oBAAM;kBACpF,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpK,OAAA;kBAAAoK,QAAA,EAAO;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDxK,OAAA;kBACE+H,KAAK,EAAEzD,UAAU,CAACK,aAAc;kBAChCgG,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErD,aAAa,EAAEiG,CAAC,CAACC,MAAM,CAAC9C;kBAAM,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDxK,OAAA;kBAAKmK,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BpK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,OAAO;sBACZvH,IAAI,EAAC,kBAAkB;sBACvB6E,KAAK,EAAC,MAAM;sBACZmD,OAAO,EAAE5G,UAAU,CAACM,gBAAgB,KAAK,IAAK;sBAC9C+F,QAAQ,EAAEA,CAAA,KAAMpG,aAAa,CAACyD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEpD,gBAAgB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRxK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,OAAO;sBACZvH,IAAI,EAAC,kBAAkB;sBACvB6E,KAAK,EAAC,OAAO;sBACbmD,OAAO,EAAE5G,UAAU,CAACM,gBAAgB,KAAK,KAAM;sBAC/C+F,QAAQ,EAAEA,CAAA,KAAMpG,aAAa,CAACyD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEpD,gBAAgB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlG,UAAU,CAACM,gBAAgB,iBAC1B5E,OAAA;gBAAKmK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAmC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDxK,OAAA;kBACE+H,KAAK,EAAEzD,UAAU,CAACO,cAAe;kBACjC8F,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEnD,cAAc,EAAE+F,CAAC,CAACC,MAAM,CAAC9C;kBAAM,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzExK,OAAA;kBAAKmK,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BpK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,OAAO;sBACZvH,IAAI,EAAC,8BAA8B;sBACnC6E,KAAK,EAAC,MAAM;sBACZmD,OAAO,EAAE5G,UAAU,CAACQ,4BAA4B,KAAK,IAAK;sBAC1D6F,QAAQ,EAAEA,CAAA,KAAMpG,aAAa,CAACyD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAElD,4BAA4B,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRxK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBACEyK,IAAI,EAAC,OAAO;sBACZvH,IAAI,EAAC,8BAA8B;sBACnC6E,KAAK,EAAC,OAAO;sBACbmD,OAAO,EAAE5G,UAAU,CAACQ,4BAA4B,KAAK,KAAM;sBAC3D6F,QAAQ,EAAEA,CAAA,KAAMpG,aAAa,CAACyD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAElD,4BAA4B,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlG,UAAU,CAACQ,4BAA4B,iBACtC9E,OAAA;gBAAKmK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCpK,OAAA;kBAAAoK,QAAA,EAAO;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnCxK,OAAA;kBACE+H,KAAK,EAAEzD,UAAU,CAACS,eAAgB;kBAClC4F,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjD,eAAe,EAAE6F,CAAC,CAACC,MAAM,CAAC9C;kBAAM,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDxK,OAAA;gBAAKmK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpK,OAAA;kBAAAoK,QAAA,EAAO;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CxK,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACX1C,KAAK,EAAEzD,UAAU,CAACU,WAAY;kBAC9B2F,QAAQ,EAAGC,CAAC,IAAKrG,aAAa,CAACyD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEhD,WAAW,EAAE4F,CAAC,CAACC,MAAM,CAAC9C;kBAAM,CAAC,CAAC;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExK,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBxK,OAAA;YAAKmK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAwD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvExK,OAAA;gBAAKmK,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpK,OAAA;kBAAAoK,QAAA,gBACEpK,OAAA;oBACEyK,IAAI,EAAC,OAAO;oBACZvH,IAAI,EAAC,iBAAiB;oBACtB6E,KAAK,EAAC,MAAM;oBACZmD,OAAO,EAAEnE,eAAe,CAACE,eAAe,KAAK,IAAK;oBAClD0D,QAAQ,EAAEA,CAAA,KAAM3D,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxK,OAAA;kBAAAoK,QAAA,gBACEpK,OAAA;oBACEyK,IAAI,EAAC,OAAO;oBACZvH,IAAI,EAAC,iBAAiB;oBACtB6E,KAAK,EAAC,OAAO;oBACbmD,OAAO,EAAEnE,eAAe,CAACE,eAAe,KAAK,KAAM;oBACnD0D,QAAQ,EAAEA,CAAA,KAAM3D,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLzD,eAAe,CAACE,eAAe,iBAC9BjH,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCxK,OAAA;gBACE+H,KAAK,EAAEhB,eAAe,CAACG,OAAQ;gBAC/ByD,QAAQ,EAAGC,CAAC,IAAK5D,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEd,OAAO,EAAE0D,CAAC,CAACC,MAAM,CAAC9C;gBAAM,CAAC,CAAC;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DxK,OAAA;gBAAKmK,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpK,OAAA;kBAAAoK,QAAA,gBACEpK,OAAA;oBACEyK,IAAI,EAAC,OAAO;oBACZvH,IAAI,EAAC,kBAAkB;oBACvB6E,KAAK,EAAC,MAAM;oBACZmD,OAAO,EAAEnE,eAAe,CAACI,gBAAgB,KAAK,IAAK;oBACnDwD,QAAQ,EAAEA,CAAA,KAAM3D,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxK,OAAA;kBAAAoK,QAAA,gBACEpK,OAAA;oBACEyK,IAAI,EAAC,OAAO;oBACZvH,IAAI,EAAC,kBAAkB;oBACvB6E,KAAK,EAAC,OAAO;oBACbmD,OAAO,EAAEnE,eAAe,CAACI,gBAAgB,KAAK,KAAM;oBACpDwD,QAAQ,EAAEA,CAAA,KAAM3D,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLzD,eAAe,CAACI,gBAAgB,iBAC/BnH,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCxK,OAAA;gBACE+H,KAAK,EAAEhB,eAAe,CAACK,iBAAkB;gBACzCuD,QAAQ,EAAGC,CAAC,IAAK5D,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEZ,iBAAiB,EAAEwD,CAAC,CAACC,MAAM,CAAC9C;gBAAM,CAAC,CAAC;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExK,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpK,OAAA;YAAAoK,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBxK,OAAA;YAAKmK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCpK,OAAA;gBAAKmK,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BpK,OAAA;kBAAAoK,QAAA,eAAGpK,OAAA;oBAAAoK,QAAA,EAAQ;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCpK,OAAA;gBAAAoK,QAAA,gBACEpK,OAAA;kBACEyK,IAAI,EAAC,UAAU;kBACfS,OAAO,EAAE7D,WAAW,CAACE,uBAAwB;kBAC7CoD,QAAQ,EAAGC,CAAC,IAAKtD,cAAc,CAACU,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAET,uBAAuB,EAAEqD,CAAC,CAACC,MAAM,CAACK;kBAAQ,CAAC,CAAC;gBAAE;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,mJAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzExK,OAAA;gBAAKmK,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpK,OAAA;kBAAAoK,QAAA,gBACEpK,OAAA;oBACEyK,IAAI,EAAC,OAAO;oBACZvH,IAAI,EAAC,qBAAqB;oBAC1B6E,KAAK,EAAC,MAAM;oBACZmD,OAAO,EAAE7D,WAAW,CAACG,mBAAmB,KAAK,IAAK;oBAClDmD,QAAQ,EAAEA,CAAA,KAAMrD,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxK,OAAA;kBAAAoK,QAAA,gBACEpK,OAAA;oBACEyK,IAAI,EAAC,OAAO;oBACZvH,IAAI,EAAC,qBAAqB;oBAC1B6E,KAAK,EAAC,OAAO;oBACbmD,OAAO,EAAE7D,WAAW,CAACG,mBAAmB,KAAK,KAAM;oBACnDmD,QAAQ,EAAEA,CAAA,KAAMrD,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELnD,WAAW,CAACG,mBAAmB,iBAC9BxH,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCxK,OAAA;gBACE+H,KAAK,EAAEV,WAAW,CAACI,sBAAuB;gBAC1CkD,QAAQ,EAAGC,CAAC,IAAKtD,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEP,sBAAsB,EAAEmD,CAAC,CAACC,MAAM,CAAC9C;gBAAM,CAAC,CAAC;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpBxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAEV,WAAW,CAACK,KAAM;gBACzBiD,QAAQ,EAAGC,CAAC,IAAKtD,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEN,KAAK,EAAEkD,CAAC,CAACC,MAAM,CAAC9C;gBAAM,CAAC,CAAC;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxK,OAAA;cAAKmK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnBxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACX1C,KAAK,EAAEV,WAAW,CAACM,IAAK;gBACxBgD,QAAQ,EAAGC,CAAC,IAAKtD,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEL,IAAI,EAAEiD,CAAC,CAACC,MAAM,CAAC9C;gBAAM,CAAC,CAAC;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpK,OAAA;gBAAAoK,QAAA,EAAO;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCxK,OAAA;gBACEyK,IAAI,EAAC,MAAM;gBACXiB,WAAW,EAAC,kCAAkC;gBAC9C3D,KAAK,EAAEV,WAAW,CAACO,kBAAmB;gBACtC+C,QAAQ,EAAGC,CAAC,IAAKtD,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEJ,kBAAkB,EAAEgD,CAAC,CAACC,MAAM,CAAC9C;gBAAM,CAAC,CAAC;cAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxK,OAAA;cAAKmK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCpK,OAAA;gBAAKmK,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BpK,OAAA;kBAAAoK,QAAA,eAAGpK,OAAA;oBAAAoK,QAAA,EAAI;kBAA0I;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAOxK,OAAA;UAAAoK,QAAA,GAAK,OAAK,EAACxD,WAAW;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMmB,UAAU,GAAGA,CAAA,KAAM;IACvBrL,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACER,OAAA;IAAKmK,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BpK,OAAA;MAAKmK,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BpK,OAAA;QAAAoK,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCxK,OAAA;QAAKmK,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BpK,OAAA;UACEmK,SAAS,EAAC,eAAe;UACzByB,KAAK,EAAE;YAAEC,KAAK,EAAE,GAAIjF,WAAW,GAAGE,UAAU,GAAI,GAAG;UAAI;QAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxK,OAAA;QAAAoK,QAAA,GAAG,OAAK,EAACxD,WAAW,EAAC,MAAI,EAACE,UAAU,EAAC,IAAE,EAACyB,YAAY,CAAC3B,WAAW,CAAC;MAAA;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAIL,CAACnK,cAAc,IAAIE,YAAY,kBAC9BP,OAAA;MAAKmK,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BpK,OAAA;QAAKmK,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpK,OAAA;UAAKmK,SAAS,EAAE9J,cAAc,GAAG,iBAAiB,GAAG,eAAgB;UAAA+J,QAAA,EAClE/J,cAAc,IAAIE;QAAY;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNxK,OAAA;UAAQmK,SAAS,EAAC,aAAa;UAACgB,OAAO,EAAEQ,UAAW;UAAAvB,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDxK,OAAA;MAAM8L,QAAQ,EAAEpC,YAAa;MAAAU,QAAA,EAC1BF,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEPxK,OAAA;MAAKmK,SAAS,EAAC,iBAAiB;MAAAC,QAAA,GAC7BxD,WAAW,GAAG,CAAC,iBACd5G,OAAA;QAAQyK,IAAI,EAAC,QAAQ;QAACU,OAAO,EAAEtC,QAAS;QAACsB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAEnE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EACA5D,WAAW,GAAGE,UAAU,gBACvB9G,OAAA;QAAQyK,IAAI,EAAC,QAAQ;QAACU,OAAO,EAAEzC,QAAS;QAACyB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAEjE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETxK,OAAA;QAAQyK,IAAI,EAAC,QAAQ;QAACU,OAAO,EAAEA,CAAA,KAAM;UACnC,MAAMY,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;UAC3C,IAAIF,IAAI,EAAE;YACRA,IAAI,CAACG,aAAa,CAAC,CAAC;UACtB;QACF,CAAE;QAAC/B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpK,EAAA,CAl+CID,cAAwB;EAAA,QAIVL,SAAS;AAAA;AAAAqM,EAAA,GAJvBhM,cAAwB;AAo+C9B,eAAeA,cAAc;AAAC,IAAAgM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}