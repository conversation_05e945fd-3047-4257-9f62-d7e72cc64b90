{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OnboardingForm = () => {\n  _s();\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [errorMessage, setErrorMessage] = useState(null);\n  const [showJsonPreview, setShowJsonPreview] = useState(false);\n  const [jsonPreviewData, setJsonPreviewData] = useState('');\n  const {\n    token\n  } = useParams();\n  const [personalDetails, setPersonalDetails] = useState({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n  const [languageSkills, setLanguageSkills] = useState([{\n    language: '',\n    speak: false,\n    read: false,\n    write: false\n  }]);\n  const [documentDetails, setDocumentDetails] = useState({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n  const [familyMembers, setFamilyMembers] = useState([{\n    name: '',\n    relationship: '',\n    dateOfBirth: '',\n    qualification: '',\n    occupation: '',\n    organisationAndPosition: ''\n  }]);\n  const [physicalDetails, setPhysicalDetails] = useState({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n  const [previousEmployment, setPreviousEmployment] = useState({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n  const [references, setReferences] = useState({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n  const [workExperience, setWorkExperience] = useState([{\n    employerName: '',\n    address: '',\n    duration: '',\n    fromDate: '',\n    toDate: '',\n    lastPosition: '',\n    designation: '',\n    natureOfDuties: '',\n    immediateSuperior: '',\n    grossEmoluments: '',\n    lastDrawn: '',\n    basicSalary: '',\n    fixedSalary: '',\n    variableSalary: '',\n    grossSalary: ''\n  }]);\n  const [education, setEducation] = useState([{\n    examination: '',\n    specialisation: '',\n    schoolCollege: '',\n    university: '',\n    fullTimePartTime: '',\n    duration: '',\n    monthYearPassing: '',\n    gradeMarks: '',\n    distinctions: '',\n    certificateFile: null\n  }]);\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n  const handlePersonalDetailsChange = (field, value) => {\n    setPersonalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleDocumentDetailsChange = (field, value) => {\n    setDocumentDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handlePhysicalDetailsChange = (field, value) => {\n    setPhysicalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Helper function to convert file to base64\n  const fileToBase64 = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  };\n  const handleFileUpload = (file, section, field, index) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = {\n          ...newEducation[index],\n          [field]: file\n        };\n        return newEducation;\n      });\n    }\n  };\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, {\n      language: '',\n      speak: false,\n      read: false,\n      write: false\n    }]);\n  };\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, {\n      name: '',\n      relationship: '',\n      dateOfBirth: '',\n      qualification: '',\n      occupation: '',\n      organisationAndPosition: ''\n    }]);\n  };\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n  const getStepTitle = step => {\n    const titles = ['', 'PERSONAL DETAILS', 'FAMILY DATA', 'HEALTH DATA', 'EDUCATION DETAILS', 'WORK HISTORY DATA', 'GENERAL DATA', 'CRIMINAL RECORDS', 'DECLARATION FORM'];\n    return titles[step] || '';\n  };\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    var _personalDetails$firs, _personalDetails$last, _personalDetails$date, _personalDetails$gend, _personalDetails$nati, _personalDetails$cont, _personalDetails$emer;\n    const requiredFields = [(_personalDetails$firs = personalDetails.firstName) === null || _personalDetails$firs === void 0 ? void 0 : _personalDetails$firs.trim(), (_personalDetails$last = personalDetails.lastName) === null || _personalDetails$last === void 0 ? void 0 : _personalDetails$last.trim(), (_personalDetails$date = personalDetails.dateOfBirth) === null || _personalDetails$date === void 0 ? void 0 : _personalDetails$date.trim(), (_personalDetails$gend = personalDetails.gender) === null || _personalDetails$gend === void 0 ? void 0 : _personalDetails$gend.trim(), (_personalDetails$nati = personalDetails.nationality) === null || _personalDetails$nati === void 0 ? void 0 : _personalDetails$nati.trim(), (_personalDetails$cont = personalDetails.contactNumber) === null || _personalDetails$cont === void 0 ? void 0 : _personalDetails$cont.trim(), (_personalDetails$emer = personalDetails.emergencyContactNo) === null || _personalDetails$emer === void 0 ? void 0 : _personalDetails$emer.trim()];\n    return requiredFields.every(Boolean);\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n    try {\n      // Convert all files to base64\n      const processedPersonalDetails = {\n        ...personalDetails\n      };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = await fileToBase64(personalDetails.passportPhoto);\n      }\n      const processedDocumentDetails = {\n        ...documentDetails\n      };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = await fileToBase64(documentDetails.passportFile);\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = await fileToBase64(documentDetails.panFile);\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = await fileToBase64(documentDetails.aadharFile);\n      }\n      const processedEducation = await Promise.all(education.map(async edu => {\n        const processedEdu = {\n          ...edu\n        };\n        if (edu.certificateFile) {\n          processedEdu.certificateFile = await fileToBase64(edu.certificateFile);\n        }\n        return processedEdu;\n      }));\n\n      // Create the complete JSON object\n      const completeFormData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Log the complete JSON for debugging\n      console.log(\"Complete Form Data JSON:\", JSON.stringify(completeFormData, null, 2));\n\n      // Send to API\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      console.log(\"Sending data to URL:\", url);\n      console.log(\"Token:\", token);\n      console.log(\"Data size:\", JSON.stringify(completeFormData).length, \"characters\");\n\n      // Try both new format and old format for backward compatibility\n      let response;\n      try {\n        // First try the new complete format\n        response = await axios.post(url, completeFormData, {\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          timeout: 30000 // 30 second timeout\n        });\n      } catch (error) {\n        console.log(\"New format failed, trying old format...\");\n        // If new format fails, try the old format\n        const oldFormatData = {\n          form_data: completeFormData\n        };\n        response = await axios.post(url, oldFormatData, {\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          timeout: 30000\n        });\n      }\n      console.log(\"API Response:\", response.data);\n      console.log(\"Response Status:\", response.status);\n      if (response.status === 200 || response.status === 201) {\n        setSuccessMessage(\"Form submitted successfully!\");\n        setErrorMessage(null);\n\n        // Optional: Download JSON file locally\n        downloadJsonFile(completeFormData);\n      } else {\n        throw new Error(`Unexpected response status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error(\"Form submission error:\", error);\n\n      // Detailed error logging\n      if (error.response) {\n        var _error$response$data;\n        console.error(\"Error Response Data:\", error.response.data);\n        console.error(\"Error Response Status:\", error.response.status);\n        console.error(\"Error Response Headers:\", error.response.headers);\n        setErrorMessage(`Server Error (${error.response.status}): ${((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Unknown error'}`);\n      } else if (error.request) {\n        console.error(\"No response received:\", error.request);\n        setErrorMessage(\"No response from server. Please check your internet connection and try again.\");\n      } else {\n        console.error(\"Request setup error:\", error.message);\n        setErrorMessage(`Request Error: ${error.message}`);\n      }\n      setSuccessMessage(null);\n    }\n  };\n\n  // Function to download JSON file locally\n  const downloadJsonFile = data => {\n    const jsonString = JSON.stringify(data, null, 2);\n    const blob = new Blob([jsonString], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `onboarding-form-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  // Function to test API connection\n  const testApiConnection = async () => {\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      console.log(\"Testing API connection to:\", url);\n\n      // Send a simple test request\n      const testData = {\n        test: true,\n        timestamp: new Date().toISOString(),\n        message: \"API connection test\"\n      };\n      const response = await axios.post(url, testData, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000\n      });\n      console.log(\"API Test Response:\", response);\n      alert(`API Connection Successful!\\nStatus: ${response.status}\\nResponse: ${JSON.stringify(response.data)}`);\n    } catch (error) {\n      console.error(\"API Test Error:\", error);\n      if (error.response) {\n        alert(`API Test Failed!\\nStatus: ${error.response.status}\\nError: ${JSON.stringify(error.response.data)}`);\n      } else if (error.request) {\n        alert(\"API Test Failed!\\nNo response from server. Check if the backend is running and accessible.\");\n      } else {\n        alert(`API Test Failed!\\nError: ${error.message}`);\n      }\n    }\n  };\n\n  // Function to fill sample data for testing\n  const fillSampleData = () => {\n    setPersonalDetails({\n      firstName: 'John',\n      middleName: 'Michael',\n      lastName: 'Doe',\n      dateOfBirth: '1990-01-15',\n      gender: 'Male',\n      birthPlace: 'New York',\n      age: '34',\n      marriageDate: '2020-06-15',\n      maritalStatus: 'Married',\n      nationality: 'American',\n      religion: 'Christian',\n      nativeState: 'New York',\n      contactNumber: '+1-555-0123',\n      emergencyContactNo: '+1-555-0124',\n      stateOfDomicile: 'New York',\n      email: '<EMAIL>',\n      presentAddress: '123 Main Street, Apartment 4B, New York, NY 10001',\n      permanentAddress: '456 Oak Avenue, Hometown, NY 12345',\n      presentPin: '10001',\n      permanentPin: '12345',\n      passportPhoto: null\n    });\n    setLanguageSkills([{\n      language: 'English',\n      speak: true,\n      read: true,\n      write: true\n    }, {\n      language: 'Spanish',\n      speak: true,\n      read: false,\n      write: false\n    }]);\n    setDocumentDetails({\n      passportNo: '*********',\n      passportIssueDate: '2020-01-01',\n      validUptoDate: '2030-01-01',\n      countryOfIssue: 'USA',\n      validVisaDetails: 'Valid until 2025',\n      panNumber: '**********',\n      aadharNumber: '1234-5678-9012',\n      passportFile: null,\n      panFile: null,\n      aadharFile: null\n    });\n    alert('Sample data filled! You can now preview the JSON structure.');\n  };\n\n  // Function to preview current form data as JSON\n  const previewFormData = async () => {\n    try {\n      // Convert files to base64 for preview\n      const processedPersonalDetails = {\n        ...personalDetails\n      };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = `[FILE: ${personalDetails.passportPhoto.name}]`;\n      }\n      const processedDocumentDetails = {\n        ...documentDetails\n      };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = `[FILE: ${documentDetails.passportFile.name}]`;\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = `[FILE: ${documentDetails.panFile.name}]`;\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = `[FILE: ${documentDetails.aadharFile.name}]`;\n      }\n      const processedEducation = education.map(edu => {\n        const processedEdu = {\n          ...edu\n        };\n        if (edu.certificateFile) {\n          processedEdu.certificateFile = `[FILE: ${edu.certificateFile.name}]`;\n        }\n        return processedEdu;\n      });\n      const previewData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Show JSON in console and modal\n      const jsonString = JSON.stringify(previewData, null, 2);\n      console.log(\"Form Data Preview:\", jsonString);\n      setJsonPreviewData(jsonString);\n      setShowJsonPreview(true);\n\n      // Optional: Download preview JSON\n      downloadJsonFile(previewData);\n    } catch (error) {\n      console.error(\"Preview error:\", error);\n      alert(\"Error generating preview\");\n    }\n  };\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"PERSONAL DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"photo-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"photo-placeholder\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"photo-frame\",\n                children: personalDetails.passportPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(personalDetails.passportPhoto),\n                  alt: \"Passport Photo\",\n                  className: \"uploaded-photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"placeholder-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"40\",\n                    height: \"40\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"7\",\n                      r: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 696,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"photo-label\",\n                children: \"Uploaded Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"upload-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"upload-text\",\n                  children: \"Please upload a image less than 100kb\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"upload-button\",\n                  children: [\"Choose a file\", /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"image/*\",\n                    required: true,\n                    onChange: e => {\n                      var _e$target$files;\n                      return handleFileUpload(((_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0]) || null, 'personal', 'passportPhoto');\n                    },\n                    className: \"hidden-file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 19\n                }, this), personalDetails.passportPhoto && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"file-info\",\n                  children: personalDetails.passportPhoto.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"First Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.firstName,\n                onChange: e => handlePersonalDetailsChange('firstName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Middle Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.middleName,\n                onChange: e => handlePersonalDetailsChange('middleName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Last Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.lastName,\n                onChange: e => handlePersonalDetailsChange('lastName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date of Birth *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                required: true,\n                value: personalDetails.dateOfBirth,\n                onChange: e => handlePersonalDetailsChange('dateOfBirth', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Gender *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                required: true,\n                value: personalDetails.gender,\n                onChange: e => handlePersonalDetailsChange('gender', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Birth Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.birthPlace,\n                onChange: e => handlePersonalDetailsChange('birthPlace', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Age\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: personalDetails.age,\n                onChange: e => handlePersonalDetailsChange('age', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marriage Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: personalDetails.marriageDate,\n                onChange: e => handlePersonalDetailsChange('marriageDate', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marital Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: personalDetails.maritalStatus,\n                onChange: e => handlePersonalDetailsChange('maritalStatus', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Single\",\n                  children: \"Single\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Married\",\n                  children: \"Married\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Divorced\",\n                  children: \"Divorced\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Widowed\",\n                  children: \"Widowed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Nationality *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.nationality,\n                onChange: e => handlePersonalDetailsChange('nationality', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Religion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.religion,\n                onChange: e => handlePersonalDetailsChange('religion', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Native State\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.nativeState,\n                onChange: e => handlePersonalDetailsChange('nativeState', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Contact Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.contactNumber,\n                onChange: e => handlePersonalDetailsChange('contactNumber', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Emergency Contact No. *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.emergencyContactNo,\n                onChange: e => handlePersonalDetailsChange('emergencyContactNo', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"State of Domicile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.stateOfDomicile,\n                onChange: e => handlePersonalDetailsChange('stateOfDomicile', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                required: true,\n                value: personalDetails.email,\n                onChange: e => handlePersonalDetailsChange('email', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.presentAddress,\n                onChange: e => handlePersonalDetailsChange('presentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.permanentAddress,\n                onChange: e => handlePersonalDetailsChange('permanentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.presentPin,\n                onChange: e => handlePersonalDetailsChange('presentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.permanentPin,\n                onChange: e => handlePersonalDetailsChange('permanentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Document Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.passportNo,\n                  onChange: e => handleDocumentDetailsChange('passportNo', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Issue Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.passportIssueDate,\n                  onChange: e => handleDocumentDetailsChange('passportIssueDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 916,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Upto Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.validUptoDate,\n                  onChange: e => handleDocumentDetailsChange('validUptoDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Country of Issue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.countryOfIssue,\n                  onChange: e => handleDocumentDetailsChange('countryOfIssue', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Visa Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.validVisaDetails,\n                  onChange: e => handleDocumentDetailsChange('validVisaDetails', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.panNumber,\n                  onChange: e => handleDocumentDetailsChange('panNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files2;\n                    return handleFileUpload(((_e$target$files2 = e.target.files) === null || _e$target$files2 === void 0 ? void 0 : _e$target$files2[0]) || null, 'document', 'panFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 19\n                }, this), documentDetails.panFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.panFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 964,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.aadharNumber,\n                  onChange: e => handleDocumentDetailsChange('aadharNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files3;\n                    return handleFileUpload(((_e$target$files3 = e.target.files) === null || _e$target$files3 === void 0 ? void 0 : _e$target$files3[0]) || null, 'document', 'aadharFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 19\n                }, this), documentDetails.aadharFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.aadharFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files4;\n                    return handleFileUpload(((_e$target$files4 = e.target.files) === null || _e$target$files4 === void 0 ? void 0 : _e$target$files4[0]) || null, 'document', 'passportFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 19\n                }, this), documentDetails.passportFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.passportFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Language Known\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 15\n            }, this), languageSkills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"language-skill-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1011,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: skill.language,\n                  onChange: e => {\n                    const newSkills = [...languageSkills];\n                    newSkills[index].language = e.target.value;\n                    setLanguageSkills(newSkills);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.speak,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].speak = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 23\n                  }, this), \"Speak\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.read,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].read = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this), \"Read\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.write,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].write = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1048,\n                    columnNumber: 23\n                  }, this), \"Write\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1022,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1009,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addLanguageSkill,\n              className: \"btn-add\",\n              children: \"Add Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"FAMILY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1071,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Family Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 15\n            }, this), familyMembers.map((member, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"family-member-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.name,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].name = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1080,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1078,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Relationship\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.relationship,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].relationship = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1092,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date of Birth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1103,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: member.dateOfBirth,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].dateOfBirth = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1102,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Qualification\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1115,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.qualification,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].qualification = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1116,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1114,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Occupation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1127,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.occupation,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].occupation = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1128,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1126,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Organisation and Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1139,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.organisationAndPosition,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].organisationAndPosition = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1140,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1076,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addFamilyMember,\n              className: \"btn-add\",\n              children: \"Add Family Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1073,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1070,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"HEALTH DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Physical Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Height\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.height,\n                  onChange: e => handlePhysicalDetailsChange('height', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1170,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Weight\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.weight,\n                  onChange: e => handlePhysicalDetailsChange('weight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1178,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Blood Group\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: physicalDetails.bloodGroup,\n                  onChange: e => handlePhysicalDetailsChange('bloodGroup', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Blood Group\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A+\",\n                    children: \"A+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A-\",\n                    children: \"A-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B+\",\n                    children: \"B+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1193,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B-\",\n                    children: \"B-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1194,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB+\",\n                    children: \"AB+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1195,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB-\",\n                    children: \"AB-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1196,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O+\",\n                    children: \"O+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1197,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O-\",\n                    children: \"O-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1198,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightRight,\n                  onChange: e => handlePhysicalDetailsChange('eyesightRight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightLeft,\n                  onChange: e => handlePhysicalDetailsChange('eyesightLeft', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Physical Disability\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.physicalDisability,\n                  onChange: e => handlePhysicalDetailsChange('physicalDisability', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Identification Mark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.identificationMark,\n                  onChange: e => handlePhysicalDetailsChange('identificationMark', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1162,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"EDUCATION DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1241,\n            columnNumber: 13\n          }, this), education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"education-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Education \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Examination Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.examination,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].examination = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1249,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Specialisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.specialisation,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].specialisation = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"School/College/Institution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.schoolCollege,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].schoolCollege = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"University/Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.university,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].university = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1285,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Full Time/Part Time/Correspondence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: edu.fullTimePartTime,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].fullTimePartTime = e.target.value;\n                    setEducation(newEducation);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1305,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time\",\n                    children: \"Full Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1306,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time\",\n                    children: \"Part Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1307,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Correspondence\",\n                    children: \"Correspondence\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1308,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Duration of Course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.duration,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].duration = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Month & Year of Passing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.monthYearPassing,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].monthYearPassing = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Grade/% Marks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.gradeMarks,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].gradeMarks = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1337,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Distinctions/Scholarships/Prizes Won\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: edu.distinctions,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].distinctions = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Certificate/Document Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1359,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files5;\n                    return handleFileUpload(((_e$target$files5 = e.target.files) === null || _e$target$files5 === void 0 ? void 0 : _e$target$files5[0]) || null, 'education', 'certificateFile', index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 21\n                }, this), edu.certificateFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: edu.certificateFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1367,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1366,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1246,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1244,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addEducation,\n            className: \"btn-add\",\n            children: \"Add Education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1240,\n          columnNumber: 11\n        }, this);\n      case 5:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"WORK HISTORY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1383,\n            columnNumber: 13\n          }, this), workExperience.map((experience, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"work-experience-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Work Experience \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Employer's Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.employerName,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].employerName = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1391,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1389,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1402,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.address,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].address = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1403,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1401,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"From Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1413,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.fromDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fromDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1414,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"To Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1425,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.toDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].toDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1426,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Position Held\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.lastPosition,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastPosition = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Designation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1449,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.designation,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].designation = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1450,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nature of Duties\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.natureOfDuties,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].natureOfDuties = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1462,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1460,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Name & Designation of Immediate Superior\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.immediateSuperior,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].immediateSuperior = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1473,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Emoluments (Rs. Per month at joining)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1484,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossEmoluments,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossEmoluments = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1485,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1483,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Drawn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1496,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.lastDrawn,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastDrawn = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1497,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Basic Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1508,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.basicSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].basicSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1509,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1507,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fixed Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.fixedSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fixedSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1521,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Variable Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1532,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.variableSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].variableSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1533,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1531,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1545,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1388,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1386,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addWorkExperience,\n            className: \"btn-add\",\n            children: \"Add Work Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1558,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1382,\n          columnNumber: 11\n        }, this);\n      case 6:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"GENERAL DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1567,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Previous Interview with MH Group\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1570,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Have you ever been interviewed by any of the MH Group of Companies?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1573,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"true\",\n                      checked: previousEmployment.hasBeenInterviewed === true,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1576,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1575,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"false\",\n                      checked: previousEmployment.hasBeenInterviewed === false,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1586,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1585,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1574,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1572,\n                columnNumber: 17\n              }, this), previousEmployment.hasBeenInterviewed && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date/Year\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1600,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewDate,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewDate: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1601,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1608,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewPosition,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewPosition: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1609,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1607,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Company\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1616,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewCompany,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewCompany: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1617,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1615,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1571,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1569,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"References & Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1629,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relatives/Acquaintance in MH Group - Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1632,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.name,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      name: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1633,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1631,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relationship\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1643,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.relationship,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      relationship: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1644,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1642,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1654,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.position,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      position: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1655,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1653,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Company & Phone No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1665,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.companyAndPhone,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      companyAndPhone: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1666,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1664,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"How did you come to know of this position?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1676,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.howDidYouKnow,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    howDidYouKnow: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1677,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1675,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Are you engaged in any personal business?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1683,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"true\",\n                      checked: references.personalBusiness === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1686,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1685,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"false\",\n                      checked: references.personalBusiness === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1696,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1695,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1682,\n                columnNumber: 17\n              }, this), references.personalBusiness && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, indicate nature of business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1709,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.businessNature,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    businessNature: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1710,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1708,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Do you have any contract/bond with your previous employer?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1718,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"true\",\n                      checked: references.contractWithPreviousEmployer === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1721,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1720,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"false\",\n                      checked: references.contractWithPreviousEmployer === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1731,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1730,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1719,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1717,\n                columnNumber: 17\n              }, this), references.contractWithPreviousEmployer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, Give Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1744,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.contractDetails,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    contractDetails: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1745,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If selected, when can you join?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1753,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: references.whenCanJoin,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    whenCanJoin: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1754,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1752,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1630,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1566,\n          columnNumber: 11\n        }, this);\n      case 7:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"CRIMINAL RECORDS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1768,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been involved in any criminal proceedings?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1772,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenInvolved === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1775,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1774,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenInvolved === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1785,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1784,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1773,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1771,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenInvolved && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1798,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.details,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  details: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1799,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1797,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been convicted of any offence?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1807,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenConvicted === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1810,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1809,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenConvicted === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1820,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1819,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1808,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1806,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenConvicted && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1833,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.convictionDetails,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  convictionDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1834,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1832,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1770,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1767,\n          columnNumber: 11\n        }, this);\n      case 8:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"DECLARATION FORM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1847,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-text\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"I hereby declare that:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1852,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1852,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1851,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1850,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: declaration.notConnectedToDirectors,\n                  onChange: e => setDeclaration(prev => ({\n                    ...prev,\n                    notConnectedToDirectors: e.target.checked\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1858,\n                  columnNumber: 19\n                }, this), \"I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1857,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1856,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"OR I am a partner or relative of a Director of the Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1868,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"true\",\n                    checked: declaration.isPartnerOrRelative === true,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1871,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1870,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"false\",\n                    checked: declaration.isPartnerOrRelative === false,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1881,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1880,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1869,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1867,\n              columnNumber: 15\n            }, this), declaration.isPartnerOrRelative && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, provide details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1895,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: declaration.partnerRelativeDetails,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  partnerRelativeDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1896,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1894,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1904,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: declaration.place,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  place: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1905,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1903,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1913,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: declaration.date,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  date: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1914,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1912,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Applicant's Signature\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1922,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Type your full name as signature\",\n                value: declaration.applicantSignature,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  applicantSignature: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1923,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1921,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-note\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"em\", {\n                    children: \"Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1933,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1933,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1932,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1931,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1849,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1846,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Step \", currentStep]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1941,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"onboarding-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Employee Onboarding Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1954,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-fill\",\n          style: {\n            width: `${currentStep / totalSteps * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1956,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1955,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Step \", currentStep, \" of \", totalSteps, \": \", getStepTitle(currentStep)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1961,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1953,\n      columnNumber: 7\n    }, this), (successMessage || errorMessage) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: successMessage ? \"success-message\" : \"error-message\",\n          children: successMessage || errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1969,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1972,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1968,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1967,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: renderStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1977,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-left\",\n        children: currentStep > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: prevStep,\n          className: \"btn-secondary\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1984,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1982,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: testApiConnection,\n          className: \"btn-test\",\n          children: \"Test API\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1991,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: fillSampleData,\n          className: \"btn-sample\",\n          children: \"Fill Sample Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1994,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: previewFormData,\n          className: \"btn-preview\",\n          children: \"Preview JSON\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1997,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1990,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-right\",\n        children: currentStep < totalSteps ? /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: nextStep,\n          className: \"btn-primary\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2004,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => {\n            const form = document.querySelector('form');\n            if (form) {\n              form.requestSubmit();\n            }\n          },\n          className: \"btn-primary\",\n          children: \"Submit Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2008,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2002,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1981,\n      columnNumber: 7\n    }, this), showJsonPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content json-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Form Data JSON Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2024,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"json-preview\",\n          children: jsonPreviewData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2025,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowJsonPreview(false),\n            className: \"modal-close\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2029,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              navigator.clipboard.writeText(jsonPreviewData);\n              alert('JSON copied to clipboard!');\n            },\n            className: \"btn-copy\",\n            children: \"Copy JSON\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2035,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2028,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2023,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2022,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1952,\n    columnNumber: 5\n  }, this);\n};\n_s(OnboardingForm, \"QR9AKn+UqHk31SUQxGwmtCYLZEg=\", false, function () {\n  return [useParams];\n});\n_c = OnboardingForm;\nexport default OnboardingForm;\nvar _c;\n$RefreshReg$(_c, \"OnboardingForm\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OnboardingForm", "_s", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "showJsonPreview", "setShowJsonPreview", "jsonPreviewData", "setJsonPreviewData", "token", "personalDetails", "setPersonalDetails", "firstName", "middleName", "lastName", "dateOfBirth", "gender", "birthPlace", "age", "marriageDate", "maritalStatus", "nationality", "religion", "nativeState", "contactNumber", "emergencyContactNo", "stateOfDomicile", "email", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "present<PERSON>in", "permanent<PERSON>in", "passportPhoto", "languageSkills", "setLanguageSkills", "language", "speak", "read", "write", "documentDetails", "setDocumentDetails", "passportNo", "passportIssueDate", "validUptoDate", "countryOfIssue", "validVisaDetails", "panNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passportFile", "panFile", "a<PERSON>har<PERSON><PERSON>", "family<PERSON><PERSON>bers", "setFamilyMembers", "name", "relationship", "qualification", "occupation", "organisationAndPosition", "physicalDetails", "setPhysicalDetails", "height", "weight", "bloodGroup", "eyesightRight", "eyesightLeft", "physicalDisability", "identificationMark", "previousEmployment", "setPreviousEmployment", "hasBeenInterviewed", "interviewDate", "interviewPosition", "interviewCompany", "references", "setReferences", "relativesInCompany", "position", "companyAndPhone", "howDidYouKnow", "personalBusiness", "businessNature", "contractWithPreviousEmployer", "contractDetails", "whenCanJoin", "workExperience", "setWorkExperience", "employerName", "address", "duration", "fromDate", "toDate", "lastPosition", "designation", "natureOfDuties", "immediateSuperior", "grossEmoluments", "lastDrawn", "basicSalary", "fixedSalary", "variableSalary", "grossSalary", "education", "setEducation", "examination", "specialisation", "schoolCollege", "university", "fullTimePartTime", "monthYearPassing", "gradeMarks", "distinctions", "certificateFile", "currentStep", "setCurrentStep", "totalSteps", "criminalRecords", "setCriminalRecords", "hasBeenInvolved", "details", "hasBeenConvicted", "convictionDetails", "declaration", "setDeclaration", "notConnectedToDirectors", "isPartnerOrRelative", "partnerRelativeDetails", "place", "date", "applicantSignature", "handlePersonalDetailsChange", "field", "value", "prev", "handleDocumentDetailsChange", "handlePhysicalDetailsChange", "fileToBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "error", "handleFileUpload", "section", "index", "maxSize", "size", "alert", "undefined", "newEducation", "addLanguageSkill", "addFamilyMember", "addWorkExperience", "addEducation", "getStepTitle", "step", "titles", "nextStep", "console", "log", "prevStep", "validateRequired<PERSON><PERSON>s", "_personalDetails$firs", "_personalDetails$last", "_personalDetails$date", "_personalDetails$gend", "_personalDetails$nati", "_personalDetails$cont", "_personalDetails$emer", "requiredFields", "trim", "every", "Boolean", "handleSubmit", "event", "preventDefault", "processedPersonalDetails", "processedDocumentDetails", "processedEducation", "all", "map", "edu", "processedEdu", "completeFormData", "submissionTimestamp", "Date", "toISOString", "formVersion", "JSON", "stringify", "url", "length", "response", "post", "headers", "timeout", "oldFormatData", "form_data", "data", "status", "downloadJsonFile", "Error", "_error$response$data", "message", "request", "jsonString", "blob", "Blob", "type", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "testApiConnection", "testData", "test", "timestamp", "fillSampleData", "previewFormData", "previewData", "renderStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "width", "viewBox", "fill", "stroke", "strokeWidth", "d", "cx", "cy", "r", "accept", "required", "onChange", "e", "_e$target$files", "target", "files", "_e$target$files2", "_e$target$files3", "_e$target$files4", "skill", "newSkills", "checked", "onClick", "member", "newMembers", "_e$target$files5", "experience", "newExperience", "placeholder", "closeModal", "style", "onSubmit", "form", "querySelector", "requestSubmit", "navigator", "clipboard", "writeText", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\n\ninterface PersonalDetails {\n  firstName: string;\n  middleName: string;\n  lastName: string;\n  dateOfBirth: string;\n  gender: string;\n  birthPlace: string;\n  age: string;\n  marriageDate: string;\n  maritalStatus: string;\n  nationality: string;\n  religion: string;\n  nativeState: string;\n  contactNumber: string;\n  emergencyContactNo: string;\n  stateOfDomicile: string;\n  email: string;\n  presentAddress: string;\n  permanentAddress: string;\n  presentPin: string;\n  permanentPin: string;\n  passportPhoto: File | null;\n}\n\ninterface LanguageSkills {\n  language: string;\n  speak: boolean;\n  read: boolean;\n  write: boolean;\n}\n\ninterface DocumentDetails {\n  passportNo: string;\n  passportIssueDate: string;\n  validUptoDate: string;\n  countryOfIssue: string;\n  validVisaDetails: string;\n  panNumber: string;\n  aadharNumber: string;\n  passportFile: File | null;\n  panFile: File | null;\n  aadharFile: File | null;\n}\n\ninterface FamilyMember {\n  name: string;\n  relationship: string;\n  dateOfBirth: string;\n  qualification: string;\n  occupation: string;\n  organisationAndPosition: string;\n}\n\ninterface PhysicalDetails {\n  height: string;\n  weight: string;\n  bloodGroup: string;\n  eyesightRight: string;\n  eyesightLeft: string;\n  physicalDisability: string;\n  identificationMark: string;\n}\n\ninterface PreviousEmployment {\n  hasBeenInterviewed: boolean;\n  interviewDate: string;\n  interviewPosition: string;\n  interviewCompany: string;\n}\n\ninterface References {\n  relativesInCompany: {\n    name: string;\n    relationship: string;\n    position: string;\n    companyAndPhone: string;\n  };\n  howDidYouKnow: string;\n  personalBusiness: boolean;\n  businessNature: string;\n  contractWithPreviousEmployer: boolean;\n  contractDetails: string;\n  whenCanJoin: string;\n}\n\ninterface WorkExperience {\n  employerName: string;\n  address: string;\n  duration: string;\n  fromDate: string;\n  toDate: string;\n  lastPosition: string;\n  designation: string;\n  natureOfDuties: string;\n  immediateSuperior: string;\n  grossEmoluments: string;\n  lastDrawn: string;\n  basicSalary: string;\n  fixedSalary: string;\n  variableSalary: string;\n  grossSalary: string;\n}\n\ninterface Education {\n  examination: string;\n  specialisation: string;\n  schoolCollege: string;\n  university: string;\n  fullTimePartTime: string;\n  duration: string;\n  monthYearPassing: string;\n  gradeMarks: string;\n  distinctions: string;\n  certificateFile: File | null;\n}\n\nconst OnboardingForm: React.FC = () => {\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [errorMessage, setErrorMessage] = useState<string | null>(null);\n  const [showJsonPreview, setShowJsonPreview] = useState<boolean>(false);\n  const [jsonPreviewData, setJsonPreviewData] = useState<string>('');\n  const { token } = useParams<{ token: string }>();\n  const [personalDetails, setPersonalDetails] = useState<PersonalDetails>({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n\n  const [languageSkills, setLanguageSkills] = useState<LanguageSkills[]>([\n    { language: '', speak: false, read: false, write: false }\n  ]);\n\n  const [documentDetails, setDocumentDetails] = useState<DocumentDetails>({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n\n  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([\n    { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }\n  ]);\n\n  const [physicalDetails, setPhysicalDetails] = useState<PhysicalDetails>({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n\n  const [previousEmployment, setPreviousEmployment] = useState<PreviousEmployment>({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n\n  const [references, setReferences] = useState<References>({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n\n  const [workExperience, setWorkExperience] = useState<WorkExperience[]>([\n    {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }\n  ]);\n\n  const [education, setEducation] = useState<Education[]>([\n    {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }\n  ]);\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n\n  const handlePersonalDetailsChange = (field: keyof PersonalDetails, value: string) => {\n    setPersonalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleDocumentDetailsChange = (field: keyof DocumentDetails, value: string) => {\n    setDocumentDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handlePhysicalDetailsChange = (field: keyof PhysicalDetails, value: string) => {\n    setPhysicalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  // Helper function to convert file to base64\n  const fileToBase64 = (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result as string);\n      reader.onerror = error => reject(error);\n    });\n  };\n\n  const handleFileUpload = (file: File | null, section: 'personal' | 'document' | 'education', field: string, index?: number) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = { ...newEducation[index], [field]: file };\n        return newEducation;\n      });\n    }\n  };\n\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, { language: '', speak: false, read: false, write: false }]);\n  };\n\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }]);\n  };\n\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n\n  const getStepTitle = (step: number) => {\n    const titles = [\n      '',\n      'PERSONAL DETAILS',\n      'FAMILY DATA',\n      'HEALTH DATA',\n      'EDUCATION DETAILS',\n      'WORK HISTORY DATA',\n      'GENERAL DATA',\n      'CRIMINAL RECORDS',\n      'DECLARATION FORM'\n    ];\n    return titles[step] || '';\n  };\n\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    const requiredFields = [\n      personalDetails.firstName?.trim(),\n      personalDetails.lastName?.trim(),\n      personalDetails.dateOfBirth?.trim(),\n      personalDetails.gender?.trim(),\n      personalDetails.nationality?.trim(),\n      personalDetails.contactNumber?.trim(),\n      personalDetails.emergencyContactNo?.trim(),\n    ];\n    return requiredFields.every(Boolean);\n  };\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n\n    try {\n      // Convert all files to base64\n      const processedPersonalDetails = { ...personalDetails };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = await fileToBase64(personalDetails.passportPhoto) as any;\n      }\n\n      const processedDocumentDetails = { ...documentDetails };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = await fileToBase64(documentDetails.passportFile) as any;\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = await fileToBase64(documentDetails.panFile) as any;\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = await fileToBase64(documentDetails.aadharFile) as any;\n      }\n\n      const processedEducation = await Promise.all(\n        education.map(async (edu) => {\n          const processedEdu = { ...edu };\n          if (edu.certificateFile) {\n            processedEdu.certificateFile = await fileToBase64(edu.certificateFile) as any;\n          }\n          return processedEdu;\n        })\n      );\n\n      // Create the complete JSON object\n      const completeFormData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Log the complete JSON for debugging\n      console.log(\"Complete Form Data JSON:\", JSON.stringify(completeFormData, null, 2));\n\n      // Send to API\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n\n      console.log(\"Sending data to URL:\", url);\n      console.log(\"Token:\", token);\n      console.log(\"Data size:\", JSON.stringify(completeFormData).length, \"characters\");\n\n      // Try both new format and old format for backward compatibility\n      let response;\n      try {\n        // First try the new complete format\n        response = await axios.post(url, completeFormData, {\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          timeout: 30000 // 30 second timeout\n        });\n      } catch (error: any) {\n        console.log(\"New format failed, trying old format...\");\n        // If new format fails, try the old format\n        const oldFormatData = { form_data: completeFormData };\n        response = await axios.post(url, oldFormatData, {\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          timeout: 30000\n        });\n      }\n\n      console.log(\"API Response:\", response.data);\n      console.log(\"Response Status:\", response.status);\n\n      if (response.status === 200 || response.status === 201) {\n        setSuccessMessage(\"Form submitted successfully!\");\n        setErrorMessage(null);\n\n        // Optional: Download JSON file locally\n        downloadJsonFile(completeFormData);\n      } else {\n        throw new Error(`Unexpected response status: ${response.status}`);\n      }\n\n    } catch (error: any) {\n      console.error(\"Form submission error:\", error);\n\n      // Detailed error logging\n      if (error.response) {\n        console.error(\"Error Response Data:\", error.response.data);\n        console.error(\"Error Response Status:\", error.response.status);\n        console.error(\"Error Response Headers:\", error.response.headers);\n        setErrorMessage(`Server Error (${error.response.status}): ${error.response.data?.message || 'Unknown error'}`);\n      } else if (error.request) {\n        console.error(\"No response received:\", error.request);\n        setErrorMessage(\"No response from server. Please check your internet connection and try again.\");\n      } else {\n        console.error(\"Request setup error:\", error.message);\n        setErrorMessage(`Request Error: ${error.message}`);\n      }\n\n      setSuccessMessage(null);\n    }\n  };\n\n  // Function to download JSON file locally\n  const downloadJsonFile = (data: any) => {\n    const jsonString = JSON.stringify(data, null, 2);\n    const blob = new Blob([jsonString], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `onboarding-form-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  // Function to test API connection\n  const testApiConnection = async () => {\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      console.log(\"Testing API connection to:\", url);\n\n      // Send a simple test request\n      const testData = {\n        test: true,\n        timestamp: new Date().toISOString(),\n        message: \"API connection test\"\n      };\n\n      const response = await axios.post(url, testData, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000\n      });\n\n      console.log(\"API Test Response:\", response);\n      alert(`API Connection Successful!\\nStatus: ${response.status}\\nResponse: ${JSON.stringify(response.data)}`);\n\n    } catch (error: any) {\n      console.error(\"API Test Error:\", error);\n\n      if (error.response) {\n        alert(`API Test Failed!\\nStatus: ${error.response.status}\\nError: ${JSON.stringify(error.response.data)}`);\n      } else if (error.request) {\n        alert(\"API Test Failed!\\nNo response from server. Check if the backend is running and accessible.\");\n      } else {\n        alert(`API Test Failed!\\nError: ${error.message}`);\n      }\n    }\n  };\n\n  // Function to fill sample data for testing\n  const fillSampleData = () => {\n    setPersonalDetails({\n      firstName: 'John',\n      middleName: 'Michael',\n      lastName: 'Doe',\n      dateOfBirth: '1990-01-15',\n      gender: 'Male',\n      birthPlace: 'New York',\n      age: '34',\n      marriageDate: '2020-06-15',\n      maritalStatus: 'Married',\n      nationality: 'American',\n      religion: 'Christian',\n      nativeState: 'New York',\n      contactNumber: '+1-555-0123',\n      emergencyContactNo: '+1-555-0124',\n      stateOfDomicile: 'New York',\n      email: '<EMAIL>',\n      presentAddress: '123 Main Street, Apartment 4B, New York, NY 10001',\n      permanentAddress: '456 Oak Avenue, Hometown, NY 12345',\n      presentPin: '10001',\n      permanentPin: '12345',\n      passportPhoto: null\n    });\n\n    setLanguageSkills([\n      { language: 'English', speak: true, read: true, write: true },\n      { language: 'Spanish', speak: true, read: false, write: false }\n    ]);\n\n    setDocumentDetails({\n      passportNo: '*********',\n      passportIssueDate: '2020-01-01',\n      validUptoDate: '2030-01-01',\n      countryOfIssue: 'USA',\n      validVisaDetails: 'Valid until 2025',\n      panNumber: '**********',\n      aadharNumber: '1234-5678-9012',\n      passportFile: null,\n      panFile: null,\n      aadharFile: null\n    });\n\n    alert('Sample data filled! You can now preview the JSON structure.');\n  };\n\n  // Function to preview current form data as JSON\n  const previewFormData = async () => {\n    try {\n      // Convert files to base64 for preview\n      const processedPersonalDetails = { ...personalDetails };\n      if (personalDetails.passportPhoto) {\n        processedPersonalDetails.passportPhoto = `[FILE: ${personalDetails.passportPhoto.name}]` as any;\n      }\n\n      const processedDocumentDetails = { ...documentDetails };\n      if (documentDetails.passportFile) {\n        processedDocumentDetails.passportFile = `[FILE: ${documentDetails.passportFile.name}]` as any;\n      }\n      if (documentDetails.panFile) {\n        processedDocumentDetails.panFile = `[FILE: ${documentDetails.panFile.name}]` as any;\n      }\n      if (documentDetails.aadharFile) {\n        processedDocumentDetails.aadharFile = `[FILE: ${documentDetails.aadharFile.name}]` as any;\n      }\n\n      const processedEducation = education.map((edu) => {\n        const processedEdu = { ...edu };\n        if (edu.certificateFile) {\n          processedEdu.certificateFile = `[FILE: ${edu.certificateFile.name}]` as any;\n        }\n        return processedEdu;\n      });\n\n      const previewData = {\n        personalDetails: processedPersonalDetails,\n        languageSkills,\n        documentDetails: processedDocumentDetails,\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: processedEducation,\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Show JSON in console and modal\n      const jsonString = JSON.stringify(previewData, null, 2);\n      console.log(\"Form Data Preview:\", jsonString);\n      setJsonPreviewData(jsonString);\n      setShowJsonPreview(true);\n\n      // Optional: Download preview JSON\n      downloadJsonFile(previewData);\n    } catch (error) {\n      console.error(\"Preview error:\", error);\n      alert(\"Error generating preview\");\n    }\n  };\n\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"form-section\">\n            <h3>PERSONAL DETAILS</h3>\n\n            {/* Passport Photo Upload Section */}\n            <div className=\"photo-upload-section\">\n              <div className=\"photo-placeholder\">\n                <div className=\"photo-frame\">\n                  {personalDetails.passportPhoto ? (\n                    <img\n                      src={URL.createObjectURL(personalDetails.passportPhoto)}\n                      alt=\"Passport Photo\"\n                      className=\"uploaded-photo\"\n                    />\n                  ) : (\n                    <div className=\"placeholder-icon\">\n                      <svg width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n                      </svg>\n                    </div>\n                  )}\n                </div>\n                <span className=\"photo-label\">Uploaded Photo</span>\n              </div>\n              <div className=\"upload-area\">\n                <div className=\"upload-content\">\n                  <p className=\"upload-text\">Please upload a image less than 100kb</p>\n                  <label className=\"upload-button\">\n                    Choose a file\n                    <input\n                      type=\"file\"\n                      accept=\"image/*\"\n                      required\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'personal', 'passportPhoto')}\n                      className=\"hidden-file-input\"\n                    />\n                  </label>\n                  {personalDetails.passportPhoto && (\n                    <p className=\"file-info\">{personalDetails.passportPhoto.name}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>First Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.firstName}\n                  onChange={(e) => handlePersonalDetailsChange('firstName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Middle Name</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.middleName}\n                  onChange={(e) => handlePersonalDetailsChange('middleName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Last Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.lastName}\n                  onChange={(e) => handlePersonalDetailsChange('lastName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Date of Birth *</label>\n                <input\n                  type=\"date\"\n                  required\n                  value={personalDetails.dateOfBirth}\n                  onChange={(e) => handlePersonalDetailsChange('dateOfBirth', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Gender *</label>\n                <select\n                  required\n                  value={personalDetails.gender}\n                  onChange={(e) => handlePersonalDetailsChange('gender', e.target.value)}\n                >\n                  <option value=\"\">Select Gender</option>\n                  <option value=\"Male\">Male</option>\n                  <option value=\"Female\">Female</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Birth Place</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.birthPlace}\n                  onChange={(e) => handlePersonalDetailsChange('birthPlace', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Age</label>\n                <input\n                  type=\"number\"\n                  value={personalDetails.age}\n                  onChange={(e) => handlePersonalDetailsChange('age', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marriage Date</label>\n                <input\n                  type=\"date\"\n                  value={personalDetails.marriageDate}\n                  onChange={(e) => handlePersonalDetailsChange('marriageDate', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marital Status</label>\n                <select\n                  value={personalDetails.maritalStatus}\n                  onChange={(e) => handlePersonalDetailsChange('maritalStatus', e.target.value)}\n                >\n                  <option value=\"\">Select Status</option>\n                  <option value=\"Single\">Single</option>\n                  <option value=\"Married\">Married</option>\n                  <option value=\"Divorced\">Divorced</option>\n                  <option value=\"Widowed\">Widowed</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Nationality *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.nationality}\n                  onChange={(e) => handlePersonalDetailsChange('nationality', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Religion</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.religion}\n                  onChange={(e) => handlePersonalDetailsChange('religion', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Native State</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.nativeState}\n                  onChange={(e) => handlePersonalDetailsChange('nativeState', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Contact Number *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.contactNumber}\n                  onChange={(e) => handlePersonalDetailsChange('contactNumber', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Emergency Contact No. *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.emergencyContactNo}\n                  onChange={(e) => handlePersonalDetailsChange('emergencyContactNo', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>State of Domicile</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.stateOfDomicile}\n                  onChange={(e) => handlePersonalDetailsChange('stateOfDomicile', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Email *</label>\n                <input\n                  type=\"email\"\n                  required\n                  value={personalDetails.email}\n                  onChange={(e) => handlePersonalDetailsChange('email', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Present Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.presentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('presentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Permanent Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.permanentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('permanentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Present PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.presentPin}\n                  onChange={(e) => handlePersonalDetailsChange('presentPin', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Permanent PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.permanentPin}\n                  onChange={(e) => handlePersonalDetailsChange('permanentPin', e.target.value)}\n                />\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Document Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Passport No.</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.passportNo}\n                    onChange={(e) => handleDocumentDetailsChange('passportNo', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Issue Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.passportIssueDate}\n                    onChange={(e) => handleDocumentDetailsChange('passportIssueDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Upto Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.validUptoDate}\n                    onChange={(e) => handleDocumentDetailsChange('validUptoDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Country of Issue</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.countryOfIssue}\n                    onChange={(e) => handleDocumentDetailsChange('countryOfIssue', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Visa Details</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.validVisaDetails}\n                    onChange={(e) => handleDocumentDetailsChange('validVisaDetails', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.panNumber}\n                    onChange={(e) => handleDocumentDetailsChange('panNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'panFile')}\n                  />\n                  {documentDetails.panFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.panFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.aadharNumber}\n                    onChange={(e) => handleDocumentDetailsChange('aadharNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'aadharFile')}\n                  />\n                  {documentDetails.aadharFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.aadharFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'passportFile')}\n                  />\n                  {documentDetails.passportFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.passportFile.name}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Language Known</h4>\n              {languageSkills.map((skill, index) => (\n                <div key={index} className=\"language-skill-row\">\n                  <div className=\"form-group\">\n                    <label>Language</label>\n                    <input\n                      type=\"text\"\n                      value={skill.language}\n                      onChange={(e) => {\n                        const newSkills = [...languageSkills];\n                        newSkills[index].language = e.target.value;\n                        setLanguageSkills(newSkills);\n                      }}\n                    />\n                  </div>\n                  <div className=\"checkbox-group\">\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.speak}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].speak = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Speak\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.read}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].read = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Read\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.write}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].write = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Write\n                    </label>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addLanguageSkill} className=\"btn-add\">\n                Add Language\n              </button>\n            </div>\n          </div>\n        );\n      case 2:\n        return (\n          <div className=\"form-section\">\n            <h3>FAMILY DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Family Members</h4>\n              {familyMembers.map((member, index) => (\n                <div key={index} className=\"family-member-section\">\n                  <div className=\"form-grid\">\n                    <div className=\"form-group\">\n                      <label>Name</label>\n                      <input\n                        type=\"text\"\n                        value={member.name}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].name = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Relationship</label>\n                      <input\n                        type=\"text\"\n                        value={member.relationship}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].relationship = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Date of Birth</label>\n                      <input\n                        type=\"date\"\n                        value={member.dateOfBirth}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].dateOfBirth = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Qualification</label>\n                      <input\n                        type=\"text\"\n                        value={member.qualification}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].qualification = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Occupation</label>\n                      <input\n                        type=\"text\"\n                        value={member.occupation}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].occupation = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Organisation and Position</label>\n                      <input\n                        type=\"text\"\n                        value={member.organisationAndPosition}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].organisationAndPosition = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addFamilyMember} className=\"btn-add\">\n                Add Family Member\n              </button>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"form-section\">\n            <h3>HEALTH DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Physical Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Height</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.height}\n                    onChange={(e) => handlePhysicalDetailsChange('height', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Weight</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.weight}\n                    onChange={(e) => handlePhysicalDetailsChange('weight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Blood Group</label>\n                  <select\n                    value={physicalDetails.bloodGroup}\n                    onChange={(e) => handlePhysicalDetailsChange('bloodGroup', e.target.value)}\n                  >\n                    <option value=\"\">Select Blood Group</option>\n                    <option value=\"A+\">A+</option>\n                    <option value=\"A-\">A-</option>\n                    <option value=\"B+\">B+</option>\n                    <option value=\"B-\">B-</option>\n                    <option value=\"AB+\">AB+</option>\n                    <option value=\"AB-\">AB-</option>\n                    <option value=\"O+\">O+</option>\n                    <option value=\"O-\">O-</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Right</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightRight}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightRight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Left</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightLeft}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightLeft', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Physical Disability</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.physicalDisability}\n                    onChange={(e) => handlePhysicalDetailsChange('physicalDisability', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Identification Mark</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.identificationMark}\n                    onChange={(e) => handlePhysicalDetailsChange('identificationMark', e.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"form-section\">\n            <h3>EDUCATION DETAILS</h3>\n\n            {education.map((edu, index) => (\n              <div key={index} className=\"education-section\">\n                <h4>Education {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Examination Passed</label>\n                    <input\n                      type=\"text\"\n                      value={edu.examination}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].examination = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Specialisation</label>\n                    <input\n                      type=\"text\"\n                      value={edu.specialisation}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].specialisation = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>School/College/Institution</label>\n                    <input\n                      type=\"text\"\n                      value={edu.schoolCollege}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].schoolCollege = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>University/Board</label>\n                    <input\n                      type=\"text\"\n                      value={edu.university}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].university = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Full Time/Part Time/Correspondence</label>\n                    <select\n                      value={edu.fullTimePartTime}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].fullTimePartTime = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    >\n                      <option value=\"\">Select Type</option>\n                      <option value=\"Full Time\">Full Time</option>\n                      <option value=\"Part Time\">Part Time</option>\n                      <option value=\"Correspondence\">Correspondence</option>\n                    </select>\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Duration of Course</label>\n                    <input\n                      type=\"text\"\n                      value={edu.duration}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].duration = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Month & Year of Passing</label>\n                    <input\n                      type=\"text\"\n                      value={edu.monthYearPassing}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].monthYearPassing = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Grade/% Marks</label>\n                    <input\n                      type=\"text\"\n                      value={edu.gradeMarks}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].gradeMarks = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Distinctions/Scholarships/Prizes Won</label>\n                    <textarea\n                      value={edu.distinctions}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].distinctions = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Certificate/Document Upload</label>\n                    <input\n                      type=\"file\"\n                      accept=\"image/*,.pdf\"\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'education', 'certificateFile', index)}\n                    />\n                    {edu.certificateFile && (\n                      <div className=\"file-preview\">\n                        <span className=\"file-name\">{edu.certificateFile.name}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addEducation} className=\"btn-add\">\n              Add Education\n            </button>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"form-section\">\n            <h3>WORK HISTORY DATA</h3>\n\n            {workExperience.map((experience, index) => (\n              <div key={index} className=\"work-experience-section\">\n                <h4>Work Experience {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Employer's Name</label>\n                    <input\n                      type=\"text\"\n                      value={experience.employerName}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].employerName = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Address</label>\n                    <textarea\n                      value={experience.address}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].address = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>From Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.fromDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fromDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>To Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.toDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].toDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Position Held</label>\n                    <input\n                      type=\"text\"\n                      value={experience.lastPosition}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastPosition = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Designation</label>\n                    <input\n                      type=\"text\"\n                      value={experience.designation}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].designation = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Nature of Duties</label>\n                    <textarea\n                      value={experience.natureOfDuties}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].natureOfDuties = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Name & Designation of Immediate Superior</label>\n                    <input\n                      type=\"text\"\n                      value={experience.immediateSuperior}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].immediateSuperior = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Emoluments (Rs. Per month at joining)</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossEmoluments}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossEmoluments = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Drawn</label>\n                    <input\n                      type=\"number\"\n                      value={experience.lastDrawn}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastDrawn = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Basic Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.basicSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].basicSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Fixed Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.fixedSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fixedSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Variable Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.variableSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].variableSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addWorkExperience} className=\"btn-add\">\n              Add Work Experience\n            </button>\n          </div>\n        );\n\n      case 6:\n        return (\n          <div className=\"form-section\">\n            <h3>GENERAL DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Previous Interview with MH Group</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Have you ever been interviewed by any of the MH Group of Companies?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"true\"\n                        checked={previousEmployment.hasBeenInterviewed === true}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"false\"\n                        checked={previousEmployment.hasBeenInterviewed === false}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {previousEmployment.hasBeenInterviewed && (\n                  <>\n                    <div className=\"form-group\">\n                      <label>Date/Year</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewDate}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewDate: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Position</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewPosition}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewPosition: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Company</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewCompany}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewCompany: e.target.value }))}\n                      />\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>References & Additional Information</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Relatives/Acquaintance in MH Group - Name</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.name}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, name: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Relationship</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.relationship}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, relationship: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Position</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.position}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, position: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Company & Phone No.</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.companyAndPhone}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, companyAndPhone: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group full-width\">\n                  <label>How did you come to know of this position?</label>\n                  <textarea\n                    value={references.howDidYouKnow}\n                    onChange={(e) => setReferences(prev => ({ ...prev, howDidYouKnow: e.target.value }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Are you engaged in any personal business?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"true\"\n                        checked={references.personalBusiness === true}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"false\"\n                        checked={references.personalBusiness === false}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.personalBusiness && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, indicate nature of business</label>\n                    <textarea\n                      value={references.businessNature}\n                      onChange={(e) => setReferences(prev => ({ ...prev, businessNature: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>Do you have any contract/bond with your previous employer?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"true\"\n                        checked={references.contractWithPreviousEmployer === true}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"false\"\n                        checked={references.contractWithPreviousEmployer === false}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.contractWithPreviousEmployer && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, Give Details</label>\n                    <textarea\n                      value={references.contractDetails}\n                      onChange={(e) => setReferences(prev => ({ ...prev, contractDetails: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>If selected, when can you join?</label>\n                  <input\n                    type=\"date\"\n                    value={references.whenCanJoin}\n                    onChange={(e) => setReferences(prev => ({ ...prev, whenCanJoin: e.target.value }))}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 7:\n        return (\n          <div className=\"form-section\">\n            <h3>CRIMINAL RECORDS</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Have you ever been involved in any criminal proceedings?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenInvolved === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenInvolved === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenInvolved && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.details}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, details: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Have you ever been convicted of any offence?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenConvicted === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenConvicted === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenConvicted && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.convictionDetails}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, convictionDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        );\n\n      case 8:\n        return (\n          <div className=\"form-section\">\n            <h3>DECLARATION FORM</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-text\">\n                  <p><strong>I hereby declare that:</strong></p>\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>\n                  <input\n                    type=\"checkbox\"\n                    checked={declaration.notConnectedToDirectors}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, notConnectedToDirectors: e.target.checked }))}\n                  />\n                  I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\n                </label>\n              </div>\n\n              <div className=\"form-group\">\n                <label>OR I am a partner or relative of a Director of the Company</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"true\"\n                      checked={declaration.isPartnerOrRelative === true}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"false\"\n                      checked={declaration.isPartnerOrRelative === false}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n\n              {declaration.isPartnerOrRelative && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, provide details</label>\n                  <textarea\n                    value={declaration.partnerRelativeDetails}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, partnerRelativeDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Place</label>\n                <input\n                  type=\"text\"\n                  value={declaration.place}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, place: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label>Date</label>\n                <input\n                  type=\"date\"\n                  value={declaration.date}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, date: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>Applicant's Signature</label>\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your full name as signature\"\n                  value={declaration.applicantSignature}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, applicantSignature: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-note\">\n                  <p><em>Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.</em></p>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step {currentStep}</div>;\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n\n  return (\n    <div className=\"onboarding-form\">\n      <div className=\"form-header\">\n        <h2>Employee Onboarding Form</h2>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress-fill\"\n            style={{ width: `${(currentStep / totalSteps) * 100}%` }}\n          ></div>\n        </div>\n        <p>Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}</p>\n      </div>\n\n\n      {/* Popup Modal for feedback messages */}\n      {(successMessage || errorMessage) && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content\">\n            <div className={successMessage ? \"success-message\" : \"error-message\"}>\n              {successMessage || errorMessage}\n            </div>\n            <button className=\"modal-close\" onClick={closeModal}>Close</button>\n          </div>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        {renderStep()}\n      </form>\n\n      <div className=\"form-navigation\">\n        <div className=\"nav-left\">\n          {currentStep > 1 && (\n            <button type=\"button\" onClick={prevStep} className=\"btn-secondary\">\n              Previous\n            </button>\n          )}\n        </div>\n\n        <div className=\"nav-center\">\n          <button type=\"button\" onClick={testApiConnection} className=\"btn-test\">\n            Test API\n          </button>\n          <button type=\"button\" onClick={fillSampleData} className=\"btn-sample\">\n            Fill Sample Data\n          </button>\n          <button type=\"button\" onClick={previewFormData} className=\"btn-preview\">\n            Preview JSON\n          </button>\n        </div>\n\n        <div className=\"nav-right\">\n          {currentStep < totalSteps ? (\n            <button type=\"button\" onClick={nextStep} className=\"btn-primary\">\n              Next\n            </button>\n          ) : (\n            <button type=\"button\" onClick={() => {\n              const form = document.querySelector('form');\n              if (form) {\n                form.requestSubmit();\n              }\n            }} className=\"btn-primary\">\n              Submit Form\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* JSON Preview Modal */}\n      {showJsonPreview && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content json-modal\">\n            <h3>Form Data JSON Preview</h3>\n            <pre className=\"json-preview\">\n              {jsonPreviewData}\n            </pre>\n            <div className=\"modal-actions\">\n              <button\n                onClick={() => setShowJsonPreview(false)}\n                className=\"modal-close\"\n              >\n                Close\n              </button>\n              <button\n                onClick={() => {\n                  navigator.clipboard.writeText(jsonPreviewData);\n                  alert('JSON copied to clipboard!');\n                }}\n                className=\"btn-copy\"\n              >\n                Copy JSON\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default OnboardingForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAC7B,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsH7C,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAU,KAAK,CAAC;EACtE,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM;IAAEiB;EAAM,CAAC,GAAGf,SAAS,CAAoB,CAAC;EAChD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAkB;IACtEoB,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,GAAG,EAAE,EAAE;IACPC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAmB,CACrE;IAAE2C,QAAQ,EAAE,EAAE;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC1D,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAkB;IACtEiD,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAiB,CACjE;IAAE6D,IAAI,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEvC,WAAW,EAAE,EAAE;IAAEwC,aAAa,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,uBAAuB,EAAE;EAAG,CAAC,CAChH,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAkB;IACtEoE,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5E,QAAQ,CAAqB;IAC/E6E,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,EAAE;IACrBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAa;IACvDmF,kBAAkB,EAAE;MAClBtB,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBsB,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC;IACDC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,cAAc,EAAE,EAAE;IAClBC,4BAA4B,EAAE,KAAK;IACnCC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7F,QAAQ,CAAmB,CACrE;IACE8F,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAc,CACtD;IACE+G,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBnB,QAAQ,EAAE,EAAE;IACZoB,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE;EACnB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM0H,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC;IACrD6H,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,KAAK;IACvBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlI,QAAQ,CAAC;IAC7CmI,uBAAuB,EAAE,KAAK;IAC9BC,mBAAmB,EAAE,KAAK;IAC1BC,sBAAsB,EAAE,EAAE;IAC1BC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,2BAA2B,GAAGA,CAACC,KAA4B,EAAEC,KAAa,KAAK;IACnFxH,kBAAkB,CAACyH,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,2BAA2B,GAAGA,CAACH,KAA4B,EAAEC,KAAa,KAAK;IACnF3F,kBAAkB,CAAC4F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,2BAA2B,GAAGA,CAACJ,KAA4B,EAAEC,KAAa,KAAK;IACnFxE,kBAAkB,CAACyE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMI,YAAY,GAAIC,IAAU,IAAsB;IACpD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC1BI,MAAM,CAACG,MAAM,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAgB,CAAC;MACtDJ,MAAM,CAACK,OAAO,GAAGC,KAAK,IAAIP,MAAM,CAACO,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACX,IAAiB,EAAEY,OAA8C,EAAElB,KAAa,EAAEmB,KAAc,KAAK;IAC7H;IACA,IAAIb,IAAI,IAAIY,OAAO,KAAK,UAAU,IAAIlB,KAAK,KAAK,eAAe,EAAE;MAC/D,MAAMoB,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;MAC5B,IAAId,IAAI,CAACe,IAAI,GAAGD,OAAO,EAAE;QACvBE,KAAK,CAAC,wCAAwC,CAAC;QAC/C;MACF;IACF;IAEA,IAAIJ,OAAO,KAAK,UAAU,EAAE;MAC1BzI,kBAAkB,CAACyH,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIY,OAAO,KAAK,UAAU,EAAE;MACjC5G,kBAAkB,CAAC4F,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIY,OAAO,KAAK,WAAW,IAAIC,KAAK,KAAKI,SAAS,EAAE;MACzDnD,YAAY,CAAC8B,IAAI,IAAI;QACnB,MAAMsB,YAAY,GAAG,CAAC,GAAGtB,IAAI,CAAC;QAC9BsB,YAAY,CAACL,KAAK,CAAC,GAAG;UAAE,GAAGK,YAAY,CAACL,KAAK,CAAC;UAAE,CAACnB,KAAK,GAAGM;QAAK,CAAC;QAC/D,OAAOkB,YAAY;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzH,iBAAiB,CAACkG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEjG,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,CAAC,CAAC;EACjG,CAAC;EAED,MAAMsH,eAAe,GAAGA,CAAA,KAAM;IAC5BxG,gBAAgB,CAACgF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAE/E,IAAI,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEvC,WAAW,EAAE,EAAE;MAAEwC,aAAa,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,uBAAuB,EAAE;IAAG,CAAC,CAAC,CAAC;EACtJ,CAAC;EAED,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxE,iBAAiB,CAAC+C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAClC9C,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IACzBxD,YAAY,CAAC8B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC7B7B,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,EAAE;MACpBnB,QAAQ,EAAE,EAAE;MACZoB,gBAAgB,EAAE,EAAE;MACpBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgD,YAAY,GAAIC,IAAY,IAAK;IACrC,MAAMC,MAAM,GAAG,CACb,EAAE,EACF,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,kBAAkB,CACnB;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAI,EAAE;EAC3B,CAAC;EAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEpD,WAAW,EAAE,cAAc,EAAEE,UAAU,CAAC;IACtF,IAAIF,WAAW,GAAGE,UAAU,EAAE;MAC5BD,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;MAC/BmD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEpD,WAAW,GAAG,CAAC,CAAC;IACjD,CAAC,MAAM;MACLmD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIrD,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMsD,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACnC,MAAMC,cAAc,GAAG,EAAAP,qBAAA,GACrB7J,eAAe,CAACE,SAAS,cAAA2J,qBAAA,uBAAzBA,qBAAA,CAA2BQ,IAAI,CAAC,CAAC,GAAAP,qBAAA,GACjC9J,eAAe,CAACI,QAAQ,cAAA0J,qBAAA,uBAAxBA,qBAAA,CAA0BO,IAAI,CAAC,CAAC,GAAAN,qBAAA,GAChC/J,eAAe,CAACK,WAAW,cAAA0J,qBAAA,uBAA3BA,qBAAA,CAA6BM,IAAI,CAAC,CAAC,GAAAL,qBAAA,GACnChK,eAAe,CAACM,MAAM,cAAA0J,qBAAA,uBAAtBA,qBAAA,CAAwBK,IAAI,CAAC,CAAC,GAAAJ,qBAAA,GAC9BjK,eAAe,CAACW,WAAW,cAAAsJ,qBAAA,uBAA3BA,qBAAA,CAA6BI,IAAI,CAAC,CAAC,GAAAH,qBAAA,GACnClK,eAAe,CAACc,aAAa,cAAAoJ,qBAAA,uBAA7BA,qBAAA,CAA+BG,IAAI,CAAC,CAAC,GAAAF,qBAAA,GACrCnK,eAAe,CAACe,kBAAkB,cAAAoJ,qBAAA,uBAAlCA,qBAAA,CAAoCE,IAAI,CAAC,CAAC,CAC3C;IACD,OAAOD,cAAc,CAACE,KAAK,CAACC,OAAO,CAAC;EACtC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,KAAsB,IAAK;IACrDA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBlL,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAI,CAACkK,sBAAsB,CAAC,CAAC,EAAE;MAC7BlK,eAAe,CAAC,2EAA2E,CAAC;MAC5F;IACF;IACA,IAAI4G,WAAW,KAAKE,UAAU,EAAE;MAC9B9G,eAAe,CAAC,uDAAuD,CAAC;MACxE;IACF;IAEA,IAAI;MACF;MACA,MAAMiL,wBAAwB,GAAG;QAAE,GAAG3K;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACsB,aAAa,EAAE;QACjCqJ,wBAAwB,CAACrJ,aAAa,GAAG,MAAMuG,YAAY,CAAC7H,eAAe,CAACsB,aAAa,CAAQ;MACnG;MAEA,MAAMsJ,wBAAwB,GAAG;QAAE,GAAG/I;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACS,YAAY,EAAE;QAChCsI,wBAAwB,CAACtI,YAAY,GAAG,MAAMuF,YAAY,CAAChG,eAAe,CAACS,YAAY,CAAQ;MACjG;MACA,IAAIT,eAAe,CAACU,OAAO,EAAE;QAC3BqI,wBAAwB,CAACrI,OAAO,GAAG,MAAMsF,YAAY,CAAChG,eAAe,CAACU,OAAO,CAAQ;MACvF;MACA,IAAIV,eAAe,CAACW,UAAU,EAAE;QAC9BoI,wBAAwB,CAACpI,UAAU,GAAG,MAAMqF,YAAY,CAAChG,eAAe,CAACW,UAAU,CAAQ;MAC7F;MAEA,MAAMqI,kBAAkB,GAAG,MAAM9C,OAAO,CAAC+C,GAAG,CAC1CnF,SAAS,CAACoF,GAAG,CAAC,MAAOC,GAAG,IAAK;QAC3B,MAAMC,YAAY,GAAG;UAAE,GAAGD;QAAI,CAAC;QAC/B,IAAIA,GAAG,CAAC3E,eAAe,EAAE;UACvB4E,YAAY,CAAC5E,eAAe,GAAG,MAAMwB,YAAY,CAACmD,GAAG,CAAC3E,eAAe,CAAQ;QAC/E;QACA,OAAO4E,YAAY;MACrB,CAAC,CACH,CAAC;;MAED;MACA,MAAMC,gBAAgB,GAAG;QACvBlL,eAAe,EAAE2K,wBAAwB;QACzCpJ,cAAc;QACdM,eAAe,EAAE+I,wBAAwB;QACzCnI,aAAa;QACbO,eAAe;QACfS,kBAAkB;QAClBM,UAAU;QACVW,cAAc;QACdiB,SAAS,EAAEkF,kBAAkB;QAC7BpE,eAAe;QACfM,WAAW;QACXoE,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7CC,WAAW,EAAE;MACf,CAAC;;MAED;MACA7B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6B,IAAI,CAACC,SAAS,CAACN,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAElF;MACA,MAAMO,GAAG,GAAG,wDAAwD1L,KAAK,GAAG;MAE5E0J,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+B,GAAG,CAAC;MACxChC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE3J,KAAK,CAAC;MAC5B0J,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6B,IAAI,CAACC,SAAS,CAACN,gBAAgB,CAAC,CAACQ,MAAM,EAAE,YAAY,CAAC;;MAEhF;MACA,IAAIC,QAAQ;MACZ,IAAI;QACF;QACAA,QAAQ,GAAG,MAAM5M,KAAK,CAAC6M,IAAI,CAACH,GAAG,EAAEP,gBAAgB,EAAE;UACjDW,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,OAAO,EAAE,KAAK,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOtD,KAAU,EAAE;QACnBiB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD;QACA,MAAMqC,aAAa,GAAG;UAAEC,SAAS,EAAEd;QAAiB,CAAC;QACrDS,QAAQ,GAAG,MAAM5M,KAAK,CAAC6M,IAAI,CAACH,GAAG,EAAEM,aAAa,EAAE;UAC9CF,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MAEArC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiC,QAAQ,CAACM,IAAI,CAAC;MAC3CxC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiC,QAAQ,CAACO,MAAM,CAAC;MAEhD,IAAIP,QAAQ,CAACO,MAAM,KAAK,GAAG,IAAIP,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;QACtD1M,iBAAiB,CAAC,8BAA8B,CAAC;QACjDE,eAAe,CAAC,IAAI,CAAC;;QAErB;QACAyM,gBAAgB,CAACjB,gBAAgB,CAAC;MACpC,CAAC,MAAM;QACL,MAAM,IAAIkB,KAAK,CAAC,+BAA+BT,QAAQ,CAACO,MAAM,EAAE,CAAC;MACnE;IAEF,CAAC,CAAC,OAAO1D,KAAU,EAAE;MACnBiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,IAAIA,KAAK,CAACmD,QAAQ,EAAE;QAAA,IAAAU,oBAAA;QAClB5C,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACmD,QAAQ,CAACM,IAAI,CAAC;QAC1DxC,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACmD,QAAQ,CAACO,MAAM,CAAC;QAC9DzC,OAAO,CAACjB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAACmD,QAAQ,CAACE,OAAO,CAAC;QAChEnM,eAAe,CAAC,iBAAiB8I,KAAK,CAACmD,QAAQ,CAACO,MAAM,MAAM,EAAAG,oBAAA,GAAA7D,KAAK,CAACmD,QAAQ,CAACM,IAAI,cAAAI,oBAAA,uBAAnBA,oBAAA,CAAqBC,OAAO,KAAI,eAAe,EAAE,CAAC;MAChH,CAAC,MAAM,IAAI9D,KAAK,CAAC+D,OAAO,EAAE;QACxB9C,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC+D,OAAO,CAAC;QACrD7M,eAAe,CAAC,+EAA+E,CAAC;MAClG,CAAC,MAAM;QACL+J,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC8D,OAAO,CAAC;QACpD5M,eAAe,CAAC,kBAAkB8I,KAAK,CAAC8D,OAAO,EAAE,CAAC;MACpD;MAEA9M,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM2M,gBAAgB,GAAIF,IAAS,IAAK;IACtC,MAAMO,UAAU,GAAGjB,IAAI,CAACC,SAAS,CAACS,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAChD,MAAMQ,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAmB,CAAC,CAAC;IACjE,MAAMlB,GAAG,GAAGmB,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGxB,GAAG;IACfqB,IAAI,CAACI,QAAQ,GAAG,mBAAmB,IAAI9B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAChFJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;IACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAC/BF,GAAG,CAACY,eAAe,CAAC/B,GAAG,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMgC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMhC,GAAG,GAAG,wDAAwD1L,KAAK,GAAG;MAC5E0J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE+B,GAAG,CAAC;;MAE9C;MACA,MAAMiC,QAAQ,GAAG;QACfC,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,IAAIxC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCiB,OAAO,EAAE;MACX,CAAC;MAED,MAAMX,QAAQ,GAAG,MAAM5M,KAAK,CAAC6M,IAAI,CAACH,GAAG,EAAEiC,QAAQ,EAAE;QAC/C7B,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFrC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiC,QAAQ,CAAC;MAC3C7C,KAAK,CAAC,uCAAuC6C,QAAQ,CAACO,MAAM,eAAeX,IAAI,CAACC,SAAS,CAACG,QAAQ,CAACM,IAAI,CAAC,EAAE,CAAC;IAE7G,CAAC,CAAC,OAAOzD,KAAU,EAAE;MACnBiB,OAAO,CAACjB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MAEvC,IAAIA,KAAK,CAACmD,QAAQ,EAAE;QAClB7C,KAAK,CAAC,6BAA6BN,KAAK,CAACmD,QAAQ,CAACO,MAAM,YAAYX,IAAI,CAACC,SAAS,CAAChD,KAAK,CAACmD,QAAQ,CAACM,IAAI,CAAC,EAAE,CAAC;MAC5G,CAAC,MAAM,IAAIzD,KAAK,CAAC+D,OAAO,EAAE;QACxBzD,KAAK,CAAC,4FAA4F,CAAC;MACrG,CAAC,MAAM;QACLA,KAAK,CAAC,4BAA4BN,KAAK,CAAC8D,OAAO,EAAE,CAAC;MACpD;IACF;EACF,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3B5N,kBAAkB,CAAC;MACjBC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE,SAAS;MACrBC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,YAAY;MACzBC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,UAAU;MACtBC,GAAG,EAAE,IAAI;MACTC,YAAY,EAAE,YAAY;MAC1BC,aAAa,EAAE,SAAS;MACxBC,WAAW,EAAE,UAAU;MACvBC,QAAQ,EAAE,WAAW;MACrBC,WAAW,EAAE,UAAU;MACvBC,aAAa,EAAE,aAAa;MAC5BC,kBAAkB,EAAE,aAAa;MACjCC,eAAe,EAAE,UAAU;MAC3BC,KAAK,EAAE,sBAAsB;MAC7BC,cAAc,EAAE,mDAAmD;MACnEC,gBAAgB,EAAE,oCAAoC;MACtDC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,OAAO;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC;IAEFE,iBAAiB,CAAC,CAChB;MAAEC,QAAQ,EAAE,SAAS;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC7D;MAAEH,QAAQ,EAAE,SAAS;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,CAChE,CAAC;IAEFE,kBAAkB,CAAC;MACjBC,UAAU,EAAE,WAAW;MACvBC,iBAAiB,EAAE,YAAY;MAC/BC,aAAa,EAAE,YAAY;MAC3BC,cAAc,EAAE,KAAK;MACrBC,gBAAgB,EAAE,kBAAkB;MACpCC,SAAS,EAAE,YAAY;MACvBC,YAAY,EAAE,gBAAgB;MAC9BC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;IACd,CAAC,CAAC;IAEFsG,KAAK,CAAC,6DAA6D,CAAC;EACtE,CAAC;;EAED;EACA,MAAMgF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA,MAAMnD,wBAAwB,GAAG;QAAE,GAAG3K;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACsB,aAAa,EAAE;QACjCqJ,wBAAwB,CAACrJ,aAAa,GAAG,UAAUtB,eAAe,CAACsB,aAAa,CAACqB,IAAI,GAAU;MACjG;MAEA,MAAMiI,wBAAwB,GAAG;QAAE,GAAG/I;MAAgB,CAAC;MACvD,IAAIA,eAAe,CAACS,YAAY,EAAE;QAChCsI,wBAAwB,CAACtI,YAAY,GAAG,UAAUT,eAAe,CAACS,YAAY,CAACK,IAAI,GAAU;MAC/F;MACA,IAAId,eAAe,CAACU,OAAO,EAAE;QAC3BqI,wBAAwB,CAACrI,OAAO,GAAG,UAAUV,eAAe,CAACU,OAAO,CAACI,IAAI,GAAU;MACrF;MACA,IAAId,eAAe,CAACW,UAAU,EAAE;QAC9BoI,wBAAwB,CAACpI,UAAU,GAAG,UAAUX,eAAe,CAACW,UAAU,CAACG,IAAI,GAAU;MAC3F;MAEA,MAAMkI,kBAAkB,GAAGlF,SAAS,CAACoF,GAAG,CAAEC,GAAG,IAAK;QAChD,MAAMC,YAAY,GAAG;UAAE,GAAGD;QAAI,CAAC;QAC/B,IAAIA,GAAG,CAAC3E,eAAe,EAAE;UACvB4E,YAAY,CAAC5E,eAAe,GAAG,UAAU2E,GAAG,CAAC3E,eAAe,CAAC1D,IAAI,GAAU;QAC7E;QACA,OAAOsI,YAAY;MACrB,CAAC,CAAC;MAEF,MAAM8C,WAAW,GAAG;QAClB/N,eAAe,EAAE2K,wBAAwB;QACzCpJ,cAAc;QACdM,eAAe,EAAE+I,wBAAwB;QACzCnI,aAAa;QACbO,eAAe;QACfS,kBAAkB;QAClBM,UAAU;QACVW,cAAc;QACdiB,SAAS,EAAEkF,kBAAkB;QAC7BpE,eAAe;QACfM,WAAW;QACXoE,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7CC,WAAW,EAAE;MACf,CAAC;;MAED;MACA,MAAMkB,UAAU,GAAGjB,IAAI,CAACC,SAAS,CAACuC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;MACvDtE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8C,UAAU,CAAC;MAC7C1M,kBAAkB,CAAC0M,UAAU,CAAC;MAC9B5M,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACAuM,gBAAgB,CAAC4B,WAAW,CAAC;IAC/B,CAAC,CAAC,OAAOvF,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCM,KAAK,CAAC,0BAA0B,CAAC;IACnC;EACF,CAAC;EAED,MAAMkF,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ1H,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEpH,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGzBpP,OAAA;YAAK+O,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnChP,OAAA;cAAK+O,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChP,OAAA;gBAAK+O,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBlO,eAAe,CAACsB,aAAa,gBAC5BpC,OAAA;kBACEqP,GAAG,EAAE3B,GAAG,CAACC,eAAe,CAAC7M,eAAe,CAACsB,aAAa,CAAE;kBACxDkN,GAAG,EAAC,gBAAgB;kBACpBP,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,gBAEFpP,OAAA;kBAAK+O,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BhP,OAAA;oBAAKuP,KAAK,EAAC,IAAI;oBAACvL,MAAM,EAAC,IAAI;oBAACwL,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAAX,QAAA,gBAC/FhP,OAAA;sBAAM4P,CAAC,EAAC;oBAA2C;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3DpP,OAAA;sBAAQ6P,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAG;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNpP,OAAA;gBAAM+O,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BhP,OAAA;gBAAK+O,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhP,OAAA;kBAAG+O,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpEpP,OAAA;kBAAO+O,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,eAE/B,eAAAhP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXuC,MAAM,EAAC,SAAS;oBAChBC,QAAQ;oBACRC,QAAQ,EAAGC,CAAC;sBAAA,IAAAC,eAAA;sBAAA,OAAK7G,gBAAgB,CAAC,EAAA6G,eAAA,GAAAD,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,eAAe,CAAC;oBAAA,CAAC;oBAC5FrB,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EACPtO,eAAe,CAACsB,aAAa,iBAC5BpC,OAAA;kBAAG+O,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAElO,eAAe,CAACsB,aAAa,CAACqB;gBAAI;kBAAAwL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACjE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpP,OAAA;YAAK+O,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXwC,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACE,SAAU;gBACjCkP,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,WAAW,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACG,UAAW;gBAClCiP,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,YAAY,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXwC,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACI,QAAS;gBAChCgP,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,UAAU,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXwC,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACK,WAAY;gBACnC+O,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,aAAa,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBpP,OAAA;gBACEiQ,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACM,MAAO;gBAC9B8O,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,QAAQ,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK,CAAE;gBAAAyG,QAAA,gBAEvEhP,OAAA;kBAAQuI,KAAK,EAAC,EAAE;kBAAAyG,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCpP,OAAA;kBAAQuI,KAAK,EAAC,MAAM;kBAAAyG,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCpP,OAAA;kBAAQuI,KAAK,EAAC,QAAQ;kBAAAyG,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCpP,OAAA;kBAAQuI,KAAK,EAAC,OAAO;kBAAAyG,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACO,UAAW;gBAClC6O,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,YAAY,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClBpP,OAAA;gBACEyN,IAAI,EAAC,QAAQ;gBACblF,KAAK,EAAEzH,eAAe,CAACQ,GAAI;gBAC3B4O,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,KAAK,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACS,YAAa;gBACpC2O,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,cAAc,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7BpP,OAAA;gBACEuI,KAAK,EAAEzH,eAAe,CAACU,aAAc;gBACrC0O,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,eAAe,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK,CAAE;gBAAAyG,QAAA,gBAE9EhP,OAAA;kBAAQuI,KAAK,EAAC,EAAE;kBAAAyG,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCpP,OAAA;kBAAQuI,KAAK,EAAC,QAAQ;kBAAAyG,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCpP,OAAA;kBAAQuI,KAAK,EAAC,SAAS;kBAAAyG,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCpP,OAAA;kBAAQuI,KAAK,EAAC,UAAU;kBAAAyG,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpP,OAAA;kBAAQuI,KAAK,EAAC,SAAS;kBAAAyG,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXwC,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACW,WAAY;gBACnCyO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,aAAa,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACY,QAAS;gBAChCwO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,UAAU,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACa,WAAY;gBACnCuO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,aAAa,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BpP,OAAA;gBACEyN,IAAI,EAAC,KAAK;gBACVwC,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACc,aAAc;gBACrCsO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,eAAe,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCpP,OAAA;gBACEyN,IAAI,EAAC,KAAK;gBACVwC,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACe,kBAAmB;gBAC1CqO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,oBAAoB,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACgB,eAAgB;gBACvCoO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,iBAAiB,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBpP,OAAA;gBACEyN,IAAI,EAAC,OAAO;gBACZwC,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACiB,KAAM;gBAC7BmO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,OAAO,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpChP,OAAA;gBAAAgP,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCpP,OAAA;gBACEiQ,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACkB,cAAe;gBACtCkO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,gBAAgB,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpChP,OAAA;gBAAAgP,QAAA,EAAO;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCpP,OAAA;gBACEiQ,QAAQ;gBACR1H,KAAK,EAAEzH,eAAe,CAACmB,gBAAiB;gBACxCiO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,kBAAkB,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACoB,UAAW;gBAClCgO,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,YAAY,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEzH,eAAe,CAACqB,YAAa;gBACpC+N,QAAQ,EAAGC,CAAC,IAAK9H,2BAA2B,CAAC,cAAc,EAAE8H,CAAC,CAACE,MAAM,CAAC9H,KAAK;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpP,OAAA;YAAK+O,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhP,OAAA;cAAAgP,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBpP,OAAA;cAAK+O,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE5F,eAAe,CAACE,UAAW;kBAClCqN,QAAQ,EAAGC,CAAC,IAAK1H,2BAA2B,CAAC,YAAY,EAAE0H,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE5F,eAAe,CAACG,iBAAkB;kBACzCoN,QAAQ,EAAGC,CAAC,IAAK1H,2BAA2B,CAAC,mBAAmB,EAAE0H,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE5F,eAAe,CAACI,aAAc;kBACrCmN,QAAQ,EAAGC,CAAC,IAAK1H,2BAA2B,CAAC,eAAe,EAAE0H,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE5F,eAAe,CAACK,cAAe;kBACtCkN,QAAQ,EAAGC,CAAC,IAAK1H,2BAA2B,CAAC,gBAAgB,EAAE0H,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE5F,eAAe,CAACM,gBAAiB;kBACxCiN,QAAQ,EAAGC,CAAC,IAAK1H,2BAA2B,CAAC,kBAAkB,EAAE0H,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXwC,QAAQ;kBACR1H,KAAK,EAAE5F,eAAe,CAACO,SAAU;kBACjCgN,QAAQ,EAAGC,CAAC,IAAK1H,2BAA2B,CAAC,WAAW,EAAE0H,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXuC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAI,gBAAA;oBAAA,OAAKhH,gBAAgB,CAAC,EAAAgH,gBAAA,GAAAJ,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAC,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC;kBAAA;gBAAC;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC,EACDzM,eAAe,CAACU,OAAO,iBACtBrD,OAAA;kBAAK+O,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BhP,OAAA;oBAAM+O,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAErM,eAAe,CAACU,OAAO,CAACI;kBAAI;oBAAAwL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXwC,QAAQ;kBACR1H,KAAK,EAAE5F,eAAe,CAACQ,YAAa;kBACpC+M,QAAQ,EAAGC,CAAC,IAAK1H,2BAA2B,CAAC,cAAc,EAAE0H,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXuC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAK,gBAAA;oBAAA,OAAKjH,gBAAgB,CAAC,EAAAiH,gBAAA,GAAAL,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAE,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC;kBAAA;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,EACDzM,eAAe,CAACW,UAAU,iBACzBtD,OAAA;kBAAK+O,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BhP,OAAA;oBAAM+O,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAErM,eAAe,CAACW,UAAU,CAACG;kBAAI;oBAAAwL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXuC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAM,gBAAA;oBAAA,OAAKlH,gBAAgB,CAAC,EAAAkH,gBAAA,GAAAN,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAG,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC;kBAAA;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,EACDzM,eAAe,CAACS,YAAY,iBAC3BpD,OAAA;kBAAK+O,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BhP,OAAA;oBAAM+O,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAErM,eAAe,CAACS,YAAY,CAACK;kBAAI;oBAAAwL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpP,OAAA;YAAK+O,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhP,OAAA;cAAAgP,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtB/M,cAAc,CAACwJ,GAAG,CAAC,CAAC6E,KAAK,EAAEjH,KAAK,kBAC/BzJ,OAAA;cAAiB+O,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBAC7ChP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEmI,KAAK,CAACnO,QAAS;kBACtB2N,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGtO,cAAc,CAAC;oBACrCsO,SAAS,CAAClH,KAAK,CAAC,CAAClH,QAAQ,GAAG4N,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC1CjG,iBAAiB,CAACqO,SAAS,CAAC;kBAC9B;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,UAAU;oBACfmD,OAAO,EAAEF,KAAK,CAAClO,KAAM;oBACrB0N,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGtO,cAAc,CAAC;sBACrCsO,SAAS,CAAClH,KAAK,CAAC,CAACjH,KAAK,GAAG2N,CAAC,CAACE,MAAM,CAACO,OAAO;sBACzCtO,iBAAiB,CAACqO,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,UAAU;oBACfmD,OAAO,EAAEF,KAAK,CAACjO,IAAK;oBACpByN,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGtO,cAAc,CAAC;sBACrCsO,SAAS,CAAClH,KAAK,CAAC,CAAChH,IAAI,GAAG0N,CAAC,CAACE,MAAM,CAACO,OAAO;sBACxCtO,iBAAiB,CAACqO,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,QAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,UAAU;oBACfmD,OAAO,EAAEF,KAAK,CAAChO,KAAM;oBACrBwN,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGtO,cAAc,CAAC;sBACrCsO,SAAS,CAAClH,KAAK,CAAC,CAAC/G,KAAK,GAAGyN,CAAC,CAACE,MAAM,CAACO,OAAO;sBACzCtO,iBAAiB,CAACqO,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAlDE3F,KAAK;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDV,CACN,CAAC,eACFpP,OAAA;cAAQyN,IAAI,EAAC,QAAQ;cAACoD,OAAO,EAAE9G,gBAAiB;cAACgF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,oBACEpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBpP,OAAA;YAAK+O,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhP,OAAA;cAAAgP,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtB7L,aAAa,CAACsI,GAAG,CAAC,CAACiF,MAAM,EAAErH,KAAK,kBAC/BzJ,OAAA;cAAiB+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAChDhP,OAAA;gBAAK+O,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBhP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEuI,MAAM,CAACrN,IAAK;oBACnByM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGxN,aAAa,CAAC;sBACrCwN,UAAU,CAACtH,KAAK,CAAC,CAAChG,IAAI,GAAG0M,CAAC,CAACE,MAAM,CAAC9H,KAAK;sBACvC/E,gBAAgB,CAACuN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEuI,MAAM,CAACpN,YAAa;oBAC3BwM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGxN,aAAa,CAAC;sBACrCwN,UAAU,CAACtH,KAAK,CAAC,CAAC/F,YAAY,GAAGyM,CAAC,CAACE,MAAM,CAAC9H,KAAK;sBAC/C/E,gBAAgB,CAACuN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEuI,MAAM,CAAC3P,WAAY;oBAC1B+O,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGxN,aAAa,CAAC;sBACrCwN,UAAU,CAACtH,KAAK,CAAC,CAACtI,WAAW,GAAGgP,CAAC,CAACE,MAAM,CAAC9H,KAAK;sBAC9C/E,gBAAgB,CAACuN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEuI,MAAM,CAACnN,aAAc;oBAC5BuM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGxN,aAAa,CAAC;sBACrCwN,UAAU,CAACtH,KAAK,CAAC,CAAC9F,aAAa,GAAGwM,CAAC,CAACE,MAAM,CAAC9H,KAAK;sBAChD/E,gBAAgB,CAACuN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzBpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEuI,MAAM,CAAClN,UAAW;oBACzBsM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGxN,aAAa,CAAC;sBACrCwN,UAAU,CAACtH,KAAK,CAAC,CAAC7F,UAAU,GAAGuM,CAAC,CAACE,MAAM,CAAC9H,KAAK;sBAC7C/E,gBAAgB,CAACuN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxCpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEuI,MAAM,CAACjN,uBAAwB;oBACtCqM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAGxN,aAAa,CAAC;sBACrCwN,UAAU,CAACtH,KAAK,CAAC,CAAC5F,uBAAuB,GAAGsM,CAAC,CAACE,MAAM,CAAC9H,KAAK;sBAC1D/E,gBAAgB,CAACuN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1EE3F,KAAK;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2EV,CACN,CAAC,eACFpP,OAAA;cAAQyN,IAAI,EAAC,QAAQ;cAACoD,OAAO,EAAE7G,eAAgB;cAAC+E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBpP,OAAA;YAAK+O,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhP,OAAA;cAAAgP,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBpP,OAAA;cAAK+O,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEzE,eAAe,CAACE,MAAO;kBAC9BkM,QAAQ,EAAGC,CAAC,IAAKzH,2BAA2B,CAAC,QAAQ,EAAEyH,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEzE,eAAe,CAACG,MAAO;kBAC9BiM,QAAQ,EAAGC,CAAC,IAAKzH,2BAA2B,CAAC,QAAQ,EAAEyH,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BpP,OAAA;kBACEuI,KAAK,EAAEzE,eAAe,CAACI,UAAW;kBAClCgM,QAAQ,EAAGC,CAAC,IAAKzH,2BAA2B,CAAC,YAAY,EAAEyH,CAAC,CAACE,MAAM,CAAC9H,KAAK,CAAE;kBAAAyG,QAAA,gBAE3EhP,OAAA;oBAAQuI,KAAK,EAAC,EAAE;oBAAAyG,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CpP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAAyG,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BpP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAAyG,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BpP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAAyG,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BpP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAAyG,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BpP,OAAA;oBAAQuI,KAAK,EAAC,KAAK;oBAAAyG,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCpP,OAAA;oBAAQuI,KAAK,EAAC,KAAK;oBAAAyG,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCpP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAAyG,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BpP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAAyG,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEzE,eAAe,CAACK,aAAc;kBACrC+L,QAAQ,EAAGC,CAAC,IAAKzH,2BAA2B,CAAC,eAAe,EAAEyH,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEzE,eAAe,CAACM,YAAa;kBACpC8L,QAAQ,EAAGC,CAAC,IAAKzH,2BAA2B,CAAC,cAAc,EAAEyH,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEzE,eAAe,CAACO,kBAAmB;kBAC1C6L,QAAQ,EAAGC,CAAC,IAAKzH,2BAA2B,CAAC,oBAAoB,EAAEyH,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEzE,eAAe,CAACQ,kBAAmB;kBAC1C4L,QAAQ,EAAGC,CAAC,IAAKzH,2BAA2B,CAAC,oBAAoB,EAAEyH,CAAC,CAACE,MAAM,CAAC9H,KAAK;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzB3I,SAAS,CAACoF,GAAG,CAAC,CAACC,GAAG,EAAErC,KAAK,kBACxBzJ,OAAA;YAAiB+O,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC5ChP,OAAA;cAAAgP,QAAA,GAAI,YAAU,EAACvF,KAAK,GAAG,CAAC;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9BpP,OAAA;cAAK+O,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEuD,GAAG,CAACnF,WAAY;kBACvBuJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC9C,WAAW,GAAGwJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAChD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEuD,GAAG,CAAClF,cAAe;kBAC1BsJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC7C,cAAc,GAAGuJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACnD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEuD,GAAG,CAACjF,aAAc;kBACzBqJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC5C,aAAa,GAAGsJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAClD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEuD,GAAG,CAAChF,UAAW;kBACtBoJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC3C,UAAU,GAAGqJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC/C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDpP,OAAA;kBACEuI,KAAK,EAAEuD,GAAG,CAAC/E,gBAAiB;kBAC5BmJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC1C,gBAAgB,GAAGoJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACrD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B,CAAE;kBAAAkF,QAAA,gBAEFhP,OAAA;oBAAQuI,KAAK,EAAC,EAAE;oBAAAyG,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCpP,OAAA;oBAAQuI,KAAK,EAAC,WAAW;oBAAAyG,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CpP,OAAA;oBAAQuI,KAAK,EAAC,WAAW;oBAAAyG,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CpP,OAAA;oBAAQuI,KAAK,EAAC,gBAAgB;oBAAAyG,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEuD,GAAG,CAAClG,QAAS;kBACpBsK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC7D,QAAQ,GAAGuK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC7C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEuD,GAAG,CAAC9E,gBAAiB;kBAC5BkJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACzC,gBAAgB,GAAGmJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACrD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAEuD,GAAG,CAAC7E,UAAW;kBACtBiJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACxC,UAAU,GAAGkJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC/C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpChP,OAAA;kBAAAgP,QAAA,EAAO;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDpP,OAAA;kBACEuI,KAAK,EAAEuD,GAAG,CAAC5E,YAAa;kBACxBgJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMrG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACvC,YAAY,GAAGiJ,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACjD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXuC,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAa,gBAAA;oBAAA,OAAKzH,gBAAgB,CAAC,EAAAyH,gBAAA,GAAAb,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAU,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAEvH,KAAK,CAAC;kBAAA;gBAAC;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,EACDtD,GAAG,CAAC3E,eAAe,iBAClBnH,OAAA;kBAAK+O,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BhP,OAAA;oBAAM+O,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAElD,GAAG,CAAC3E,eAAe,CAAC1D;kBAAI;oBAAAwL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/HE3F,KAAK;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgIV,CACN,CAAC,eACFpP,OAAA;YAAQyN,IAAI,EAAC,QAAQ;YAACoD,OAAO,EAAE3G,YAAa;YAAC6E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzB5J,cAAc,CAACqG,GAAG,CAAC,CAACoF,UAAU,EAAExH,KAAK,kBACpCzJ,OAAA;YAAiB+O,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAClDhP,OAAA;cAAAgP,QAAA,GAAI,kBAAgB,EAACvF,KAAK,GAAG,CAAC;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCpP,OAAA;cAAK+O,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE0I,UAAU,CAACvL,YAAa;kBAC/BwK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAAC/D,YAAY,GAAGyK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAClD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpChP,OAAA;kBAAAgP,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBpP,OAAA;kBACEuI,KAAK,EAAE0I,UAAU,CAACtL,OAAQ;kBAC1BuK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAAC9D,OAAO,GAAGwK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC7C9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE0I,UAAU,CAACpL,QAAS;kBAC3BqK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAAC5D,QAAQ,GAAGsK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC9C9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE0I,UAAU,CAACnL,MAAO;kBACzBoK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAAC3D,MAAM,GAAGqK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC5C9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE0I,UAAU,CAAClL,YAAa;kBAC/BmK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAAC1D,YAAY,GAAGoK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAClD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE0I,UAAU,CAACjL,WAAY;kBAC9BkK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACzD,WAAW,GAAGmK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACjD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpChP,OAAA;kBAAAgP,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BpP,OAAA;kBACEuI,KAAK,EAAE0I,UAAU,CAAChL,cAAe;kBACjCiK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACxD,cAAc,GAAGkK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACpD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE0I,UAAU,CAAC/K,iBAAkB;kBACpCgK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACvD,iBAAiB,GAAGiK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACvD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DpP,OAAA;kBACEyN,IAAI,EAAC,QAAQ;kBACblF,KAAK,EAAE0I,UAAU,CAAC9K,eAAgB;kBAClC+J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACtD,eAAe,GAAGgK,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACrD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBpP,OAAA;kBACEyN,IAAI,EAAC,QAAQ;kBACblF,KAAK,EAAE0I,UAAU,CAAC7K,SAAU;kBAC5B8J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACrD,SAAS,GAAG+J,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBAC/C9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BpP,OAAA;kBACEyN,IAAI,EAAC,QAAQ;kBACblF,KAAK,EAAE0I,UAAU,CAAC5K,WAAY;kBAC9B6J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACpD,WAAW,GAAG8J,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACjD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BpP,OAAA;kBACEyN,IAAI,EAAC,QAAQ;kBACblF,KAAK,EAAE0I,UAAU,CAAC3K,WAAY;kBAC9B4J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACnD,WAAW,GAAG6J,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACjD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BpP,OAAA;kBACEyN,IAAI,EAAC,QAAQ;kBACblF,KAAK,EAAE0I,UAAU,CAAC1K,cAAe;kBACjC2J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAAClD,cAAc,GAAG4J,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACpD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BpP,OAAA;kBACEyN,IAAI,EAAC,QAAQ;kBACblF,KAAK,EAAE0I,UAAU,CAACzK,WAAY;kBAC9B0J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG1L,cAAc,CAAC;oBACzC0L,aAAa,CAACzH,KAAK,CAAC,CAACjD,WAAW,GAAG2J,CAAC,CAACE,MAAM,CAAC9H,KAAK;oBACjD9C,iBAAiB,CAACyL,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAzKE3F,KAAK;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0KV,CACN,CAAC,eACFpP,OAAA;YAAQyN,IAAI,EAAC,QAAQ;YAACoD,OAAO,EAAE5G,iBAAkB;YAAC8E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAErBpP,OAAA;YAAK+O,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhP,OAAA;cAAAgP,QAAA,EAAI;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCpP,OAAA;cAAK+O,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAmE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClFpP,OAAA;kBAAK+O,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhP,OAAA;oBAAAgP,QAAA,gBACEhP,OAAA;sBACEyN,IAAI,EAAC,OAAO;sBACZhK,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,MAAM;sBACZqI,OAAO,EAAErM,kBAAkB,CAACE,kBAAkB,KAAK,IAAK;sBACxDyL,QAAQ,EAAEA,CAAA,KAAM1L,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAwK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRpP,OAAA;oBAAAgP,QAAA,gBACEhP,OAAA;sBACEyN,IAAI,EAAC,OAAO;sBACZhK,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,OAAO;sBACbqI,OAAO,EAAErM,kBAAkB,CAACE,kBAAkB,KAAK,KAAM;sBACzDyL,QAAQ,EAAEA,CAAA,KAAM1L,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAwK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL7K,kBAAkB,CAACE,kBAAkB,iBACpCzE,OAAA,CAAAE,SAAA;gBAAA8O,QAAA,gBACEhP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxBpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEhE,kBAAkB,CAACG,aAAc;oBACxCwL,QAAQ,EAAGC,CAAC,IAAK3L,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE9D,aAAa,EAAEyL,CAAC,CAACE,MAAM,CAAC9H;oBAAM,CAAC,CAAC;kBAAE;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvBpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEhE,kBAAkB,CAACI,iBAAkB;oBAC5CuL,QAAQ,EAAGC,CAAC,IAAK3L,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE7D,iBAAiB,EAAEwL,CAAC,CAACE,MAAM,CAAC9H;oBAAM,CAAC,CAAC;kBAAE;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpP,OAAA;kBAAK+O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhP,OAAA;oBAAAgP,QAAA,EAAO;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtBpP,OAAA;oBACEyN,IAAI,EAAC,MAAM;oBACXlF,KAAK,EAAEhE,kBAAkB,CAACK,gBAAiB;oBAC3CsL,QAAQ,EAAGC,CAAC,IAAK3L,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE5D,gBAAgB,EAAEuL,CAAC,CAACE,MAAM,CAAC9H;oBAAM,CAAC,CAAC;kBAAE;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpP,OAAA;YAAK+O,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhP,OAAA;cAAAgP,QAAA,EAAI;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CpP,OAAA;cAAK+O,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACtB,IAAK;kBAC1CyM,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEtB,IAAI,EAAE0M,CAAC,CAACE,MAAM,CAAC9H;oBAAM;kBACzE,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACrB,YAAa;kBAClDwM,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAErB,YAAY,EAAEyM,CAAC,CAACE,MAAM,CAAC9H;oBAAM;kBACjF,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACC,QAAS;kBAC9CkL,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEC,QAAQ,EAAEmL,CAAC,CAACE,MAAM,CAAC9H;oBAAM;kBAC7E,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACE,eAAgB;kBACrDiL,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEE,eAAe,EAAEkL,CAAC,CAACE,MAAM,CAAC9H;oBAAM;kBACpF,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpChP,OAAA;kBAAAgP,QAAA,EAAO;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDpP,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACK,aAAc;kBAChCgL,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtD,aAAa,EAAEiL,CAAC,CAACE,MAAM,CAAC9H;kBAAM,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDpP,OAAA;kBAAK+O,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhP,OAAA;oBAAAgP,QAAA,gBACEhP,OAAA;sBACEyN,IAAI,EAAC,OAAO;sBACZhK,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,MAAM;sBACZqI,OAAO,EAAE/L,UAAU,CAACM,gBAAgB,KAAK,IAAK;sBAC9C+K,QAAQ,EAAEA,CAAA,KAAMpL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRpP,OAAA;oBAAAgP,QAAA,gBACEhP,OAAA;sBACEyN,IAAI,EAAC,OAAO;sBACZhK,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,OAAO;sBACbqI,OAAO,EAAE/L,UAAU,CAACM,gBAAgB,KAAK,KAAM;sBAC/C+K,QAAQ,EAAEA,CAAA,KAAMpL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLvK,UAAU,CAACM,gBAAgB,iBAC1BnF,OAAA;gBAAK+O,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpChP,OAAA;kBAAAgP,QAAA,EAAO;gBAAmC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDpP,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACO,cAAe;kBACjC8K,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEpD,cAAc,EAAE+K,CAAC,CAACE,MAAM,CAAC9H;kBAAM,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEpP,OAAA;kBAAK+O,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhP,OAAA;oBAAAgP,QAAA,gBACEhP,OAAA;sBACEyN,IAAI,EAAC,OAAO;sBACZhK,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,MAAM;sBACZqI,OAAO,EAAE/L,UAAU,CAACQ,4BAA4B,KAAK,IAAK;sBAC1D6K,QAAQ,EAAEA,CAAA,KAAMpL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAA4J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRpP,OAAA;oBAAAgP,QAAA,gBACEhP,OAAA;sBACEyN,IAAI,EAAC,OAAO;sBACZhK,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,OAAO;sBACbqI,OAAO,EAAE/L,UAAU,CAACQ,4BAA4B,KAAK,KAAM;sBAC3D6K,QAAQ,EAAEA,CAAA,KAAMpL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAA4J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLvK,UAAU,CAACQ,4BAA4B,iBACtCrF,OAAA;gBAAK+O,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpChP,OAAA;kBAAAgP,QAAA,EAAO;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnCpP,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACS,eAAgB;kBAClC4K,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAElD,eAAe,EAAE6K,CAAC,CAACE,MAAM,CAAC9H;kBAAM,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDpP,OAAA;gBAAK+O,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhP,OAAA;kBAAAgP,QAAA,EAAO;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CpP,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXlF,KAAK,EAAE1D,UAAU,CAACU,WAAY;kBAC9B2K,QAAQ,EAAGC,CAAC,IAAKrL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjD,WAAW,EAAE4K,CAAC,CAACE,MAAM,CAAC9H;kBAAM,CAAC,CAAC;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBpP,OAAA;YAAK+O,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAwD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvEpP,OAAA;gBAAK+O,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,OAAO;oBACZhK,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,MAAM;oBACZqI,OAAO,EAAErJ,eAAe,CAACE,eAAe,KAAK,IAAK;oBAClDyI,QAAQ,EAAEA,CAAA,KAAM1I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,OAAO;oBACZhK,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,OAAO;oBACbqI,OAAO,EAAErJ,eAAe,CAACE,eAAe,KAAK,KAAM;oBACnDyI,QAAQ,EAAEA,CAAA,KAAM1I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL7H,eAAe,CAACE,eAAe,iBAC9BzH,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpChP,OAAA;gBAAAgP,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCpP,OAAA;gBACEuI,KAAK,EAAEhB,eAAe,CAACG,OAAQ;gBAC/BwI,QAAQ,EAAGC,CAAC,IAAK3I,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEd,OAAO,EAAEyI,CAAC,CAACE,MAAM,CAAC9H;gBAAM,CAAC,CAAC;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DpP,OAAA;gBAAK+O,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,OAAO;oBACZhK,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,MAAM;oBACZqI,OAAO,EAAErJ,eAAe,CAACI,gBAAgB,KAAK,IAAK;oBACnDuI,QAAQ,EAAEA,CAAA,KAAM1I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,OAAO;oBACZhK,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,OAAO;oBACbqI,OAAO,EAAErJ,eAAe,CAACI,gBAAgB,KAAK,KAAM;oBACpDuI,QAAQ,EAAEA,CAAA,KAAM1I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL7H,eAAe,CAACI,gBAAgB,iBAC/B3H,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpChP,OAAA;gBAAAgP,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCpP,OAAA;gBACEuI,KAAK,EAAEhB,eAAe,CAACK,iBAAkB;gBACzCsI,QAAQ,EAAGC,CAAC,IAAK3I,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEZ,iBAAiB,EAAEuI,CAAC,CAACE,MAAM,CAAC9H;gBAAM,CAAC,CAAC;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhP,OAAA;YAAAgP,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBpP,OAAA;YAAK+O,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhP,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpChP,OAAA;gBAAK+O,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BhP,OAAA;kBAAAgP,QAAA,eAAGhP,OAAA;oBAAAgP,QAAA,EAAQ;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpP,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpChP,OAAA;gBAAAgP,QAAA,gBACEhP,OAAA;kBACEyN,IAAI,EAAC,UAAU;kBACfmD,OAAO,EAAE/I,WAAW,CAACE,uBAAwB;kBAC7CmI,QAAQ,EAAGC,CAAC,IAAKrI,cAAc,CAACU,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAET,uBAAuB,EAAEoI,CAAC,CAACE,MAAM,CAACO;kBAAQ,CAAC,CAAC;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,mJAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEpP,OAAA;gBAAK+O,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,OAAO;oBACZhK,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,MAAM;oBACZqI,OAAO,EAAE/I,WAAW,CAACG,mBAAmB,KAAK,IAAK;oBAClDkI,QAAQ,EAAEA,CAAA,KAAMpI,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpP,OAAA;kBAAAgP,QAAA,gBACEhP,OAAA;oBACEyN,IAAI,EAAC,OAAO;oBACZhK,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,OAAO;oBACbqI,OAAO,EAAE/I,WAAW,CAACG,mBAAmB,KAAK,KAAM;oBACnDkI,QAAQ,EAAEA,CAAA,KAAMpI,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELvH,WAAW,CAACG,mBAAmB,iBAC9BhI,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpChP,OAAA;gBAAAgP,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCpP,OAAA;gBACEuI,KAAK,EAAEV,WAAW,CAACI,sBAAuB;gBAC1CiI,QAAQ,EAAGC,CAAC,IAAKrI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEP,sBAAsB,EAAEkI,CAAC,CAACE,MAAM,CAAC9H;gBAAM,CAAC,CAAC;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpBpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEV,WAAW,CAACK,KAAM;gBACzBgI,QAAQ,EAAGC,CAAC,IAAKrI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEN,KAAK,EAAEiI,CAAC,CAACE,MAAM,CAAC9H;gBAAM,CAAC,CAAC;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENpP,OAAA;cAAK+O,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhP,OAAA;gBAAAgP,QAAA,EAAO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnBpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACXlF,KAAK,EAAEV,WAAW,CAACM,IAAK;gBACxB+H,QAAQ,EAAGC,CAAC,IAAKrI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEL,IAAI,EAAEgI,CAAC,CAACE,MAAM,CAAC9H;gBAAM,CAAC,CAAC;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENpP,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpChP,OAAA;gBAAAgP,QAAA,EAAO;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCpP,OAAA;gBACEyN,IAAI,EAAC,MAAM;gBACX0D,WAAW,EAAC,kCAAkC;gBAC9C5I,KAAK,EAAEV,WAAW,CAACO,kBAAmB;gBACtC8H,QAAQ,EAAGC,CAAC,IAAKrI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEJ,kBAAkB,EAAE+H,CAAC,CAACE,MAAM,CAAC9H;gBAAM,CAAC,CAAC;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENpP,OAAA;cAAK+O,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpChP,OAAA;gBAAK+O,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BhP,OAAA;kBAAAgP,QAAA,eAAGhP,OAAA;oBAAAgP,QAAA,EAAI;kBAA0I;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAOpP,OAAA;UAAAgP,QAAA,GAAK,OAAK,EAAC5H,WAAW;QAAA;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAGA,CAAA,KAAM;IACvB9Q,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACER,OAAA;IAAK+O,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BhP,OAAA;MAAK+O,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhP,OAAA;QAAAgP,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCpP,OAAA;QAAK+O,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BhP,OAAA;UACE+O,SAAS,EAAC,eAAe;UACzBsC,KAAK,EAAE;YAAE9B,KAAK,EAAE,GAAInI,WAAW,GAAGE,UAAU,GAAI,GAAG;UAAI;QAAE;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpP,OAAA;QAAAgP,QAAA,GAAG,OAAK,EAAC5H,WAAW,EAAC,MAAI,EAACE,UAAU,EAAC,IAAE,EAAC6C,YAAY,CAAC/C,WAAW,CAAC;MAAA;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAIL,CAAC/O,cAAc,IAAIE,YAAY,kBAC9BP,OAAA;MAAK+O,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BhP,OAAA;QAAK+O,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhP,OAAA;UAAK+O,SAAS,EAAE1O,cAAc,GAAG,iBAAiB,GAAG,eAAgB;UAAA2O,QAAA,EAClE3O,cAAc,IAAIE;QAAY;UAAA0O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNpP,OAAA;UAAQ+O,SAAS,EAAC,aAAa;UAAC8B,OAAO,EAAEO,UAAW;UAAApC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpP,OAAA;MAAMsR,QAAQ,EAAEhG,YAAa;MAAA0D,QAAA,EAC1BF,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEPpP,OAAA;MAAK+O,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BhP,OAAA;QAAK+O,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtB5H,WAAW,GAAG,CAAC,iBACdpH,OAAA;UAAQyN,IAAI,EAAC,QAAQ;UAACoD,OAAO,EAAEpG,QAAS;UAACsE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENpP,OAAA;QAAK+O,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhP,OAAA;UAAQyN,IAAI,EAAC,QAAQ;UAACoD,OAAO,EAAEtC,iBAAkB;UAACQ,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpP,OAAA;UAAQyN,IAAI,EAAC,QAAQ;UAACoD,OAAO,EAAElC,cAAe;UAACI,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpP,OAAA;UAAQyN,IAAI,EAAC,QAAQ;UAACoD,OAAO,EAAEjC,eAAgB;UAACG,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpP,OAAA;QAAK+O,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB5H,WAAW,GAAGE,UAAU,gBACvBtH,OAAA;UAAQyN,IAAI,EAAC,QAAQ;UAACoD,OAAO,EAAEvG,QAAS;UAACyE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETpP,OAAA;UAAQyN,IAAI,EAAC,QAAQ;UAACoD,OAAO,EAAEA,CAAA,KAAM;YACnC,MAAMU,IAAI,GAAG1D,QAAQ,CAAC2D,aAAa,CAAC,MAAM,CAAC;YAC3C,IAAID,IAAI,EAAE;cACRA,IAAI,CAACE,aAAa,CAAC,CAAC;YACtB;UACF,CAAE;UAAC1C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3O,eAAe,iBACdT,OAAA;MAAK+O,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BhP,OAAA;QAAK+O,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvChP,OAAA;UAAAgP,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BpP,OAAA;UAAK+O,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BrO;QAAe;UAAAsO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNpP,OAAA;UAAK+O,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BhP,OAAA;YACE6Q,OAAO,EAAEA,CAAA,KAAMnQ,kBAAkB,CAAC,KAAK,CAAE;YACzCqO,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpP,OAAA;YACE6Q,OAAO,EAAEA,CAAA,KAAM;cACba,SAAS,CAACC,SAAS,CAACC,SAAS,CAACjR,eAAe,CAAC;cAC9CiJ,KAAK,CAAC,2BAA2B,CAAC;YACpC,CAAE;YACFmF,SAAS,EAAC,UAAU;YAAAC,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChP,EAAA,CAx4DID,cAAwB;EAAA,QAMVL,SAAS;AAAA;AAAA+R,EAAA,GANvB1R,cAAwB;AA04D9B,eAAeA,cAAc;AAAC,IAAA0R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}