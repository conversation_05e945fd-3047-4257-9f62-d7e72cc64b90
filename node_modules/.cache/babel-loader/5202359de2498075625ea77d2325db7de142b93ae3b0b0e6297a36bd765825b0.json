{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OnboardingForm = () => {\n  _s();\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [errorMessage, setErrorMessage] = useState(null);\n  const [showJsonPreview, setShowJsonPreview] = useState(false);\n  const [jsonPreviewData, setJsonPreviewData] = useState('');\n  const {\n    token\n  } = useParams();\n  const [personalDetails, setPersonalDetails] = useState({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n  const [languageSkills, setLanguageSkills] = useState([{\n    language: '',\n    speak: false,\n    read: false,\n    write: false\n  }]);\n  const [documentDetails, setDocumentDetails] = useState({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n  const [familyMembers, setFamilyMembers] = useState([{\n    name: '',\n    relationship: '',\n    dateOfBirth: '',\n    qualification: '',\n    occupation: '',\n    organisationAndPosition: ''\n  }]);\n  const [physicalDetails, setPhysicalDetails] = useState({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n  const [previousEmployment, setPreviousEmployment] = useState({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n  const [references, setReferences] = useState({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n  const [workExperience, setWorkExperience] = useState([{\n    employerName: '',\n    address: '',\n    duration: '',\n    fromDate: '',\n    toDate: '',\n    lastPosition: '',\n    designation: '',\n    natureOfDuties: '',\n    immediateSuperior: '',\n    grossEmoluments: '',\n    lastDrawn: '',\n    basicSalary: '',\n    fixedSalary: '',\n    variableSalary: '',\n    grossSalary: ''\n  }]);\n  const [education, setEducation] = useState([{\n    examination: '',\n    specialisation: '',\n    schoolCollege: '',\n    university: '',\n    fullTimePartTime: '',\n    duration: '',\n    monthYearPassing: '',\n    gradeMarks: '',\n    distinctions: '',\n    certificateFile: null\n  }]);\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n  const handlePersonalDetailsChange = (field, value) => {\n    setPersonalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleDocumentDetailsChange = (field, value) => {\n    setDocumentDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handlePhysicalDetailsChange = (field, value) => {\n    setPhysicalDetails(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Helper function to convert file to base64\n  const fileToBase64 = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  };\n  const handleFileUpload = (file, section, field, index) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({\n        ...prev,\n        [field]: file\n      }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = {\n          ...newEducation[index],\n          [field]: file\n        };\n        return newEducation;\n      });\n    }\n  };\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, {\n      language: '',\n      speak: false,\n      read: false,\n      write: false\n    }]);\n  };\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, {\n      name: '',\n      relationship: '',\n      dateOfBirth: '',\n      qualification: '',\n      occupation: '',\n      organisationAndPosition: ''\n    }]);\n  };\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n  const getStepTitle = step => {\n    const titles = ['', 'PERSONAL DETAILS', 'FAMILY DATA', 'HEALTH DATA', 'EDUCATION DETAILS', 'WORK HISTORY DATA', 'GENERAL DATA', 'CRIMINAL RECORDS', 'DECLARATION FORM'];\n    return titles[step] || '';\n  };\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    var _personalDetails$firs, _personalDetails$last, _personalDetails$date, _personalDetails$gend, _personalDetails$nati, _personalDetails$cont, _personalDetails$emer;\n    const requiredFields = [(_personalDetails$firs = personalDetails.firstName) === null || _personalDetails$firs === void 0 ? void 0 : _personalDetails$firs.trim(), (_personalDetails$last = personalDetails.lastName) === null || _personalDetails$last === void 0 ? void 0 : _personalDetails$last.trim(), (_personalDetails$date = personalDetails.dateOfBirth) === null || _personalDetails$date === void 0 ? void 0 : _personalDetails$date.trim(), (_personalDetails$gend = personalDetails.gender) === null || _personalDetails$gend === void 0 ? void 0 : _personalDetails$gend.trim(), (_personalDetails$nati = personalDetails.nationality) === null || _personalDetails$nati === void 0 ? void 0 : _personalDetails$nati.trim(), (_personalDetails$cont = personalDetails.contactNumber) === null || _personalDetails$cont === void 0 ? void 0 : _personalDetails$cont.trim(), (_personalDetails$emer = personalDetails.emergencyContactNo) === null || _personalDetails$emer === void 0 ? void 0 : _personalDetails$emer.trim()];\n    return requiredFields.every(Boolean);\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n    try {\n      // Create FormData for file uploads\n      const formData = new FormData();\n\n      // Add text data as JSON\n      const textData = {\n        personalDetails: {\n          ...personalDetails,\n          passportPhoto: null // Remove file reference\n        },\n        languageSkills,\n        documentDetails: {\n          ...documentDetails,\n          passportFile: null,\n          panFile: null,\n          aadharFile: null\n        },\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: education.map(edu => ({\n          ...edu,\n          certificateFile: null\n        })),\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Add JSON data\n      formData.append('form_data', JSON.stringify(textData));\n\n      // Add files with descriptive names\n      if (personalDetails.passportPhoto) {\n        formData.append('passport_photo', personalDetails.passportPhoto, 'passport_photo.jpg');\n      }\n      if (documentDetails.passportFile) {\n        formData.append('passport_document', documentDetails.passportFile, 'passport_document.pdf');\n      }\n      if (documentDetails.panFile) {\n        formData.append('pan_document', documentDetails.panFile, 'pan_document.pdf');\n      }\n      if (documentDetails.aadharFile) {\n        formData.append('aadhar_document', documentDetails.aadharFile, 'aadhar_document.pdf');\n      }\n\n      // Add education certificates\n      education.forEach((edu, index) => {\n        if (edu.certificateFile) {\n          formData.append(`education_certificate_${index}`, edu.certificateFile, `education_certificate_${index}.pdf`);\n        }\n      });\n\n      // Log the form data for debugging\n      console.log(\"Form Data Contents:\");\n      console.log(\"Text data:\", JSON.stringify(textData, null, 2));\n\n      // Count files\n      let fileCount = 0;\n      if (personalDetails.passportPhoto) fileCount++;\n      if (documentDetails.passportFile) fileCount++;\n      if (documentDetails.panFile) fileCount++;\n      if (documentDetails.aadharFile) fileCount++;\n      education.forEach(edu => {\n        if (edu.certificateFile) fileCount++;\n      });\n      console.log(`Total files to upload: ${fileCount}`);\n\n      // Send to API\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      console.log(\"Sending FormData to URL:\", url);\n      console.log(\"Token:\", token);\n      const response = await axios.post(url, formData, {\n        headers: {\n          // Don't set Content-Type for FormData - let browser set it with boundary\n        },\n        timeout: 60000 // 60 second timeout for file uploads\n      });\n      console.log(\"API Response:\", response.data);\n      console.log(\"Response Status:\", response.status);\n      if (response.status === 200 || response.status === 201) {\n        setSuccessMessage(\"Form submitted successfully!\");\n        setErrorMessage(null);\n\n        // Optional: Download JSON file locally\n        downloadJsonFile(textData);\n      } else {\n        throw new Error(`Unexpected response status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error(\"Form submission error:\", error);\n\n      // Detailed error logging\n      if (error.response) {\n        var _error$response$data;\n        console.error(\"Error Response Data:\", error.response.data);\n        console.error(\"Error Response Status:\", error.response.status);\n        console.error(\"Error Response Headers:\", error.response.headers);\n        setErrorMessage(`Server Error (${error.response.status}): ${((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Unknown error'}`);\n      } else if (error.request) {\n        console.error(\"No response received:\", error.request);\n        setErrorMessage(\"No response from server. Please check your internet connection and try again.\");\n      } else {\n        console.error(\"Request setup error:\", error.message);\n        setErrorMessage(`Request Error: ${error.message}`);\n      }\n      setSuccessMessage(null);\n    }\n  };\n\n  // Function to download JSON file locally\n  const downloadJsonFile = data => {\n    const jsonString = JSON.stringify(data, null, 2);\n    const blob = new Blob([jsonString], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `onboarding-form-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  // Function to test API connection\n  const testApiConnection = async () => {\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      console.log(\"Testing API connection to:\", url);\n\n      // Create test FormData\n      const testFormData = new FormData();\n      testFormData.append('form_data', JSON.stringify({\n        test: true,\n        timestamp: new Date().toISOString(),\n        message: \"API connection test\"\n      }));\n      const response = await axios.post(url, testFormData, {\n        timeout: 10000\n      });\n      console.log(\"API Test Response:\", response);\n      alert(`API Connection Successful!\\nStatus: ${response.status}\\nResponse: ${JSON.stringify(response.data)}`);\n    } catch (error) {\n      console.error(\"API Test Error:\", error);\n      if (error.response) {\n        alert(`API Test Failed!\\nStatus: ${error.response.status}\\nError: ${JSON.stringify(error.response.data)}`);\n      } else if (error.request) {\n        alert(\"API Test Failed!\\nNo response from server. Check if the backend is running and accessible.\");\n      } else {\n        alert(`API Test Failed!\\nError: ${error.message}`);\n      }\n    }\n  };\n\n  // Function to fill sample data for testing\n  const fillSampleData = () => {\n    setPersonalDetails({\n      firstName: 'John',\n      middleName: 'Michael',\n      lastName: 'Doe',\n      dateOfBirth: '1990-01-15',\n      gender: 'Male',\n      birthPlace: 'New York',\n      age: '34',\n      marriageDate: '2020-06-15',\n      maritalStatus: 'Married',\n      nationality: 'American',\n      religion: 'Christian',\n      nativeState: 'New York',\n      contactNumber: '+1-555-0123',\n      emergencyContactNo: '+1-555-0124',\n      stateOfDomicile: 'New York',\n      email: '<EMAIL>',\n      presentAddress: '123 Main Street, Apartment 4B, New York, NY 10001',\n      permanentAddress: '456 Oak Avenue, Hometown, NY 12345',\n      presentPin: '10001',\n      permanentPin: '12345',\n      passportPhoto: null\n    });\n    setLanguageSkills([{\n      language: 'English',\n      speak: true,\n      read: true,\n      write: true\n    }, {\n      language: 'Spanish',\n      speak: true,\n      read: false,\n      write: false\n    }]);\n    setDocumentDetails({\n      passportNo: '*********',\n      passportIssueDate: '2020-01-01',\n      validUptoDate: '2030-01-01',\n      countryOfIssue: 'USA',\n      validVisaDetails: 'Valid until 2025',\n      panNumber: '**********',\n      aadharNumber: '1234-5678-9012',\n      passportFile: null,\n      panFile: null,\n      aadharFile: null\n    });\n    alert('Sample data filled! You can now preview the JSON structure.');\n  };\n\n  // Function to preview current form data as JSON\n  const previewFormData = async () => {\n    try {\n      // Create preview data showing file information\n      const previewData = {\n        personalDetails: {\n          ...personalDetails,\n          passportPhoto: personalDetails.passportPhoto ? `[FILE: ${personalDetails.passportPhoto.name} (${personalDetails.passportPhoto.size} bytes)]` : null\n        },\n        languageSkills,\n        documentDetails: {\n          ...documentDetails,\n          passportFile: documentDetails.passportFile ? `[FILE: ${documentDetails.passportFile.name} (${documentDetails.passportFile.size} bytes)]` : null,\n          panFile: documentDetails.panFile ? `[FILE: ${documentDetails.panFile.name} (${documentDetails.panFile.size} bytes)]` : null,\n          aadharFile: documentDetails.aadharFile ? `[FILE: ${documentDetails.aadharFile.name} (${documentDetails.aadharFile.size} bytes)]` : null\n        },\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: education.map(edu => ({\n          ...edu,\n          certificateFile: edu.certificateFile ? `[FILE: ${edu.certificateFile.name} (${edu.certificateFile.size} bytes)]` : null\n        })),\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\",\n        fileUploadInfo: {\n          totalFiles: [personalDetails.passportPhoto, documentDetails.passportFile, documentDetails.panFile, documentDetails.aadharFile, ...education.map(edu => edu.certificateFile)].filter(Boolean).length,\n          uploadMethod: \"FormData (multipart/form-data)\"\n        }\n      };\n\n      // Show JSON in console and modal\n      const jsonString = JSON.stringify(previewData, null, 2);\n      console.log(\"Form Data Preview:\", jsonString);\n      setJsonPreviewData(jsonString);\n      setShowJsonPreview(true);\n\n      // Optional: Download preview JSON\n      downloadJsonFile(previewData);\n    } catch (error) {\n      console.error(\"Preview error:\", error);\n      alert(\"Error generating preview\");\n    }\n  };\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"PERSONAL DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"photo-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"photo-placeholder\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"photo-frame\",\n                children: personalDetails.passportPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(personalDetails.passportPhoto),\n                  alt: \"Passport Photo\",\n                  className: \"uploaded-photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"placeholder-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"40\",\n                    height: \"40\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"7\",\n                      r: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 704,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"photo-label\",\n                children: \"Uploaded Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"upload-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"upload-text\",\n                  children: \"Please upload a image less than 100kb\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"upload-button\",\n                  children: [\"Choose a file\", /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"image/*\",\n                    required: true,\n                    onChange: e => {\n                      var _e$target$files;\n                      return handleFileUpload(((_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0]) || null, 'personal', 'passportPhoto');\n                    },\n                    className: \"hidden-file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this), personalDetails.passportPhoto && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"file-info\",\n                  children: personalDetails.passportPhoto.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"First Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.firstName,\n                onChange: e => handlePersonalDetailsChange('firstName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Middle Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.middleName,\n                onChange: e => handlePersonalDetailsChange('middleName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Last Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.lastName,\n                onChange: e => handlePersonalDetailsChange('lastName', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date of Birth *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                required: true,\n                value: personalDetails.dateOfBirth,\n                onChange: e => handlePersonalDetailsChange('dateOfBirth', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Gender *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                required: true,\n                value: personalDetails.gender,\n                onChange: e => handlePersonalDetailsChange('gender', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Birth Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.birthPlace,\n                onChange: e => handlePersonalDetailsChange('birthPlace', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Age\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: personalDetails.age,\n                onChange: e => handlePersonalDetailsChange('age', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marriage Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: personalDetails.marriageDate,\n                onChange: e => handlePersonalDetailsChange('marriageDate', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Marital Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: personalDetails.maritalStatus,\n                onChange: e => handlePersonalDetailsChange('maritalStatus', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Single\",\n                  children: \"Single\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Married\",\n                  children: \"Married\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Divorced\",\n                  children: \"Divorced\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Widowed\",\n                  children: \"Widowed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Nationality *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: personalDetails.nationality,\n                onChange: e => handlePersonalDetailsChange('nationality', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Religion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.religion,\n                onChange: e => handlePersonalDetailsChange('religion', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Native State\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.nativeState,\n                onChange: e => handlePersonalDetailsChange('nativeState', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Contact Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.contactNumber,\n                onChange: e => handlePersonalDetailsChange('contactNumber', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Emergency Contact No. *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                required: true,\n                value: personalDetails.emergencyContactNo,\n                onChange: e => handlePersonalDetailsChange('emergencyContactNo', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"State of Domicile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.stateOfDomicile,\n                onChange: e => handlePersonalDetailsChange('stateOfDomicile', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                required: true,\n                value: personalDetails.email,\n                onChange: e => handlePersonalDetailsChange('email', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.presentAddress,\n                onChange: e => handlePersonalDetailsChange('presentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                required: true,\n                value: personalDetails.permanentAddress,\n                onChange: e => handlePersonalDetailsChange('permanentAddress', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Present PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.presentPin,\n                onChange: e => handlePersonalDetailsChange('presentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Permanent PIN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: personalDetails.permanentPin,\n                onChange: e => handlePersonalDetailsChange('permanentPin', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Document Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 912,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.passportNo,\n                  onChange: e => handleDocumentDetailsChange('passportNo', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 916,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Issue Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.passportIssueDate,\n                  onChange: e => handleDocumentDetailsChange('passportIssueDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Upto Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: documentDetails.validUptoDate,\n                  onChange: e => handleDocumentDetailsChange('validUptoDate', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Country of Issue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.countryOfIssue,\n                  onChange: e => handleDocumentDetailsChange('countryOfIssue', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Valid Visa Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: documentDetails.validVisaDetails,\n                  onChange: e => handleDocumentDetailsChange('validVisaDetails', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.panNumber,\n                  onChange: e => handleDocumentDetailsChange('panNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"PAN Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files2;\n                    return handleFileUpload(((_e$target$files2 = e.target.files) === null || _e$target$files2 === void 0 ? void 0 : _e$target$files2[0]) || null, 'document', 'panFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 19\n                }, this), documentDetails.panFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.panFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 977,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  required: true,\n                  value: documentDetails.aadharNumber,\n                  onChange: e => handleDocumentDetailsChange('aadharNumber', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Aadhar Card Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files3;\n                    return handleFileUpload(((_e$target$files3 = e.target.files) === null || _e$target$files3 === void 0 ? void 0 : _e$target$files3[0]) || null, 'document', 'aadharFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 19\n                }, this), documentDetails.aadharFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.aadharFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 985,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Passport Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files4;\n                    return handleFileUpload(((_e$target$files4 = e.target.files) === null || _e$target$files4 === void 0 ? void 0 : _e$target$files4[0]) || null, 'document', 'passportFile');\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1000,\n                  columnNumber: 19\n                }, this), documentDetails.passportFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: documentDetails.passportFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1007,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1006,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Language Known\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 15\n            }, this), languageSkills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"language-skill-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: skill.language,\n                  onChange: e => {\n                    const newSkills = [...languageSkills];\n                    newSkills[index].language = e.target.value;\n                    setLanguageSkills(newSkills);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.speak,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].speak = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 23\n                  }, this), \"Speak\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.read,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].read = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1044,\n                    columnNumber: 23\n                  }, this), \"Read\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: skill.write,\n                    onChange: e => {\n                      const newSkills = [...languageSkills];\n                      newSkills[index].write = e.target.checked;\n                      setLanguageSkills(newSkills);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1056,\n                    columnNumber: 23\n                  }, this), \"Write\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1017,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addLanguageSkill,\n              className: \"btn-add\",\n              children: \"Add Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1070,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1014,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"FAMILY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1079,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Family Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 15\n            }, this), familyMembers.map((member, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"family-member-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.name,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].name = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1088,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Relationship\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1099,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.relationship,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].relationship = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1098,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date of Birth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: member.dateOfBirth,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].dateOfBirth = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1112,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Qualification\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1123,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.qualification,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].qualification = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1122,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Occupation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1135,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.occupation,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].occupation = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1136,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1134,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Organisation and Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: member.organisationAndPosition,\n                    onChange: e => {\n                      const newMembers = [...familyMembers];\n                      newMembers[index].organisationAndPosition = e.target.value;\n                      setFamilyMembers(newMembers);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1146,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1085,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addFamilyMember,\n              className: \"btn-add\",\n              children: \"Add Family Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1078,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"HEALTH DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Physical Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Height\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.height,\n                  onChange: e => handlePhysicalDetailsChange('height', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1178,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Weight\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.weight,\n                  onChange: e => handlePhysicalDetailsChange('weight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Blood Group\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: physicalDetails.bloodGroup,\n                  onChange: e => handlePhysicalDetailsChange('bloodGroup', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Blood Group\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A+\",\n                    children: \"A+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1199,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A-\",\n                    children: \"A-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1200,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B+\",\n                    children: \"B+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1201,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"B-\",\n                    children: \"B-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1202,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB+\",\n                    children: \"AB+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1203,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"AB-\",\n                    children: \"AB-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O+\",\n                    children: \"O+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"O-\",\n                    children: \"O-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1206,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1194,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightRight,\n                  onChange: e => handlePhysicalDetailsChange('eyesightRight', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Eyesight Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.eyesightLeft,\n                  onChange: e => handlePhysicalDetailsChange('eyesightLeft', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Physical Disability\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.physicalDisability,\n                  onChange: e => handlePhysicalDetailsChange('physicalDisability', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Identification Mark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: physicalDetails.identificationMark,\n                  onChange: e => handlePhysicalDetailsChange('identificationMark', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"EDUCATION DETAILS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1249,\n            columnNumber: 13\n          }, this), education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"education-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Education \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Examination Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.examination,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].examination = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1257,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Specialisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1268,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.specialisation,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].specialisation = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1269,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1267,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"School/College/Institution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.schoolCollege,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].schoolCollege = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"University/Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1292,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.university,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].university = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1293,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Full Time/Part Time/Correspondence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1304,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: edu.fullTimePartTime,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].fullTimePartTime = e.target.value;\n                    setEducation(newEducation);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1313,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time\",\n                    children: \"Full Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1314,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time\",\n                    children: \"Part Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1315,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Correspondence\",\n                    children: \"Correspondence\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1316,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1305,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Duration of Course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.duration,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].duration = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Month & Year of Passing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.monthYearPassing,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].monthYearPassing = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1333,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Grade/% Marks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: edu.gradeMarks,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].gradeMarks = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1345,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Distinctions/Scholarships/Prizes Won\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1356,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: edu.distinctions,\n                  onChange: e => {\n                    const newEducation = [...education];\n                    newEducation[index].distinctions = e.target.value;\n                    setEducation(newEducation);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Certificate/Document Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1367,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*,.pdf\",\n                  onChange: e => {\n                    var _e$target$files5;\n                    return handleFileUpload(((_e$target$files5 = e.target.files) === null || _e$target$files5 === void 0 ? void 0 : _e$target$files5[0]) || null, 'education', 'certificateFile', index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 21\n                }, this), edu.certificateFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-name\",\n                    children: edu.certificateFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1375,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1374,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1366,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1254,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1252,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addEducation,\n            className: \"btn-add\",\n            children: \"Add Education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1382,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1248,\n          columnNumber: 11\n        }, this);\n      case 5:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"WORK HISTORY DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1391,\n            columnNumber: 13\n          }, this), workExperience.map((experience, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"work-experience-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Work Experience \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1395,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Employer's Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.employerName,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].employerName = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1399,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1397,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.address,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].address = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"From Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.fromDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fromDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1422,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"To Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: experience.toDate,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].toDate = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1434,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1432,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Position Held\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.lastPosition,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastPosition = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Designation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1457,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.designation,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].designation = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1458,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nature of Duties\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: experience.natureOfDuties,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].natureOfDuties = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1468,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Name & Designation of Immediate Superior\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: experience.immediateSuperior,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].immediateSuperior = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1481,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Emoluments (Rs. Per month at joining)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1492,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossEmoluments,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossEmoluments = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1493,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1491,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Drawn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1504,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.lastDrawn,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].lastDrawn = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1505,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Basic Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1516,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.basicSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].basicSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1517,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fixed Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1528,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.fixedSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].fixedSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1529,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Variable Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1540,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.variableSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].variableSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1541,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1539,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Gross Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1552,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: experience.grossSalary,\n                  onChange: e => {\n                    const newExperience = [...workExperience];\n                    newExperience[index].grossSalary = e.target.value;\n                    setWorkExperience(newExperience);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1553,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1551,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1396,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1394,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: addWorkExperience,\n            className: \"btn-add\",\n            children: \"Add Work Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1566,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1390,\n          columnNumber: 11\n        }, this);\n      case 6:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"GENERAL DATA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Previous Interview with MH Group\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Have you ever been interviewed by any of the MH Group of Companies?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1581,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"true\",\n                      checked: previousEmployment.hasBeenInterviewed === true,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1584,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1583,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"hasBeenInterviewed\",\n                      value: \"false\",\n                      checked: previousEmployment.hasBeenInterviewed === false,\n                      onChange: () => setPreviousEmployment(prev => ({\n                        ...prev,\n                        hasBeenInterviewed: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1594,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1593,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1582,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1580,\n                columnNumber: 17\n              }, this), previousEmployment.hasBeenInterviewed && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Date/Year\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1608,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewDate,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewDate: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1609,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1607,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1616,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewPosition,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewPosition: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1617,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1615,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Company\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1624,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: previousEmployment.interviewCompany,\n                    onChange: e => setPreviousEmployment(prev => ({\n                      ...prev,\n                      interviewCompany: e.target.value\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1625,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1623,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1579,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subsection\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"References & Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1637,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relatives/Acquaintance in MH Group - Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.name,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      name: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1641,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1639,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Relationship\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1651,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.relationship,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      relationship: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1652,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1650,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1662,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.position,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      position: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1663,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1661,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Company & Phone No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1673,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: references.relativesInCompany.companyAndPhone,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    relativesInCompany: {\n                      ...prev.relativesInCompany,\n                      companyAndPhone: e.target.value\n                    }\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1674,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1672,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"How did you come to know of this position?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.howDidYouKnow,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    howDidYouKnow: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1685,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1683,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Are you engaged in any personal business?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1691,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"true\",\n                      checked: references.personalBusiness === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1694,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1693,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"personalBusiness\",\n                      value: \"false\",\n                      checked: references.personalBusiness === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        personalBusiness: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1704,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1703,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1692,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1690,\n                columnNumber: 17\n              }, this), references.personalBusiness && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, indicate nature of business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1717,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.businessNature,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    businessNature: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1718,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1716,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Do you have any contract/bond with your previous employer?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1726,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"radio-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"true\",\n                      checked: references.contractWithPreviousEmployer === true,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: true\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1729,\n                      columnNumber: 23\n                    }, this), \"Yes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1728,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"contractWithPreviousEmployer\",\n                      value: \"false\",\n                      checked: references.contractWithPreviousEmployer === false,\n                      onChange: () => setReferences(prev => ({\n                        ...prev,\n                        contractWithPreviousEmployer: false\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1739,\n                      columnNumber: 23\n                    }, this), \"No\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1738,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1727,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1725,\n                columnNumber: 17\n              }, this), references.contractWithPreviousEmployer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If YES, Give Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1752,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: references.contractDetails,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    contractDetails: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1753,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1751,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"If selected, when can you join?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1761,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: references.whenCanJoin,\n                  onChange: e => setReferences(prev => ({\n                    ...prev,\n                    whenCanJoin: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1762,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1760,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1636,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1574,\n          columnNumber: 11\n        }, this);\n      case 7:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"CRIMINAL RECORDS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1776,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been involved in any criminal proceedings?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1780,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenInvolved === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1783,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1782,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenInvolved\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenInvolved === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenInvolved: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1793,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1792,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1781,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1779,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenInvolved && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1806,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.details,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  details: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1807,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1805,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Have you ever been convicted of any offence?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1815,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"true\",\n                    checked: criminalRecords.hasBeenConvicted === true,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1818,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1817,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"hasBeenConvicted\",\n                    value: \"false\",\n                    checked: criminalRecords.hasBeenConvicted === false,\n                    onChange: () => setCriminalRecords(prev => ({\n                      ...prev,\n                      hasBeenConvicted: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1828,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1827,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1816,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1814,\n              columnNumber: 15\n            }, this), criminalRecords.hasBeenConvicted && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, give details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1841,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: criminalRecords.convictionDetails,\n                onChange: e => setCriminalRecords(prev => ({\n                  ...prev,\n                  convictionDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1842,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1840,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1778,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1775,\n          columnNumber: 11\n        }, this);\n      case 8:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"DECLARATION FORM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-text\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"I hereby declare that:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1860,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1860,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1859,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1858,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: declaration.notConnectedToDirectors,\n                  onChange: e => setDeclaration(prev => ({\n                    ...prev,\n                    notConnectedToDirectors: e.target.checked\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1866,\n                  columnNumber: 19\n                }, this), \"I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1865,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1864,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"OR I am a partner or relative of a Director of the Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1876,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radio-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"true\",\n                    checked: declaration.isPartnerOrRelative === true,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: true\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1879,\n                    columnNumber: 21\n                  }, this), \"Yes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1878,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"isPartnerOrRelative\",\n                    value: \"false\",\n                    checked: declaration.isPartnerOrRelative === false,\n                    onChange: () => setDeclaration(prev => ({\n                      ...prev,\n                      isPartnerOrRelative: false\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1889,\n                    columnNumber: 21\n                  }, this), \"No\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1888,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1877,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1875,\n              columnNumber: 15\n            }, this), declaration.isPartnerOrRelative && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"If YES, provide details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1903,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: declaration.partnerRelativeDetails,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  partnerRelativeDetails: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1904,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1902,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Place\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1912,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: declaration.place,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  place: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1913,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1911,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1921,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: declaration.date,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  date: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1922,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1920,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Applicant's Signature\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1930,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Type your full name as signature\",\n                value: declaration.applicantSignature,\n                onChange: e => setDeclaration(prev => ({\n                  ...prev,\n                  applicantSignature: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1931,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1929,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"declaration-note\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"em\", {\n                    children: \"Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1941,\n                    columnNumber: 22\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1941,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1940,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1939,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1857,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1854,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Step \", currentStep]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1949,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"onboarding-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Employee Onboarding Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1962,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-fill\",\n          style: {\n            width: `${currentStep / totalSteps * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1964,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1963,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Step \", currentStep, \" of \", totalSteps, \": \", getStepTitle(currentStep)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1969,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1961,\n      columnNumber: 7\n    }, this), (successMessage || errorMessage) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: successMessage ? \"success-message\" : \"error-message\",\n          children: successMessage || errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1977,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1980,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1976,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1975,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: renderStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1985,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-left\",\n        children: currentStep > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: prevStep,\n          className: \"btn-secondary\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1992,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1990,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: testApiConnection,\n          className: \"btn-test\",\n          children: \"Test API\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1999,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: fillSampleData,\n          className: \"btn-sample\",\n          children: \"Fill Sample Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2002,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: previewFormData,\n          className: \"btn-preview\",\n          children: \"Preview JSON\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2005,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1998,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-right\",\n        children: currentStep < totalSteps ? /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: nextStep,\n          className: \"btn-primary\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2012,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => {\n            const form = document.querySelector('form');\n            if (form) {\n              form.requestSubmit();\n            }\n          },\n          className: \"btn-primary\",\n          children: \"Submit Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2016,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2010,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1989,\n      columnNumber: 7\n    }, this), showJsonPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content json-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Form Data JSON Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2032,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"json-preview\",\n          children: jsonPreviewData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2033,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowJsonPreview(false),\n            className: \"modal-close\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2037,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              navigator.clipboard.writeText(jsonPreviewData);\n              alert('JSON copied to clipboard!');\n            },\n            className: \"btn-copy\",\n            children: \"Copy JSON\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2043,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2036,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2031,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2030,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1960,\n    columnNumber: 5\n  }, this);\n};\n_s(OnboardingForm, \"QR9AKn+UqHk31SUQxGwmtCYLZEg=\", false, function () {\n  return [useParams];\n});\n_c = OnboardingForm;\nexport default OnboardingForm;\nvar _c;\n$RefreshReg$(_c, \"OnboardingForm\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OnboardingForm", "_s", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "showJsonPreview", "setShowJsonPreview", "jsonPreviewData", "setJsonPreviewData", "token", "personalDetails", "setPersonalDetails", "firstName", "middleName", "lastName", "dateOfBirth", "gender", "birthPlace", "age", "marriageDate", "maritalStatus", "nationality", "religion", "nativeState", "contactNumber", "emergencyContactNo", "stateOfDomicile", "email", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "present<PERSON>in", "permanent<PERSON>in", "passportPhoto", "languageSkills", "setLanguageSkills", "language", "speak", "read", "write", "documentDetails", "setDocumentDetails", "passportNo", "passportIssueDate", "validUptoDate", "countryOfIssue", "validVisaDetails", "panNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passportFile", "panFile", "a<PERSON>har<PERSON><PERSON>", "family<PERSON><PERSON>bers", "setFamilyMembers", "name", "relationship", "qualification", "occupation", "organisationAndPosition", "physicalDetails", "setPhysicalDetails", "height", "weight", "bloodGroup", "eyesightRight", "eyesightLeft", "physicalDisability", "identificationMark", "previousEmployment", "setPreviousEmployment", "hasBeenInterviewed", "interviewDate", "interviewPosition", "interviewCompany", "references", "setReferences", "relativesInCompany", "position", "companyAndPhone", "howDidYouKnow", "personalBusiness", "businessNature", "contractWithPreviousEmployer", "contractDetails", "whenCanJoin", "workExperience", "setWorkExperience", "employerName", "address", "duration", "fromDate", "toDate", "lastPosition", "designation", "natureOfDuties", "immediateSuperior", "grossEmoluments", "lastDrawn", "basicSalary", "fixedSalary", "variableSalary", "grossSalary", "education", "setEducation", "examination", "specialisation", "schoolCollege", "university", "fullTimePartTime", "monthYearPassing", "gradeMarks", "distinctions", "certificateFile", "currentStep", "setCurrentStep", "totalSteps", "criminalRecords", "setCriminalRecords", "hasBeenInvolved", "details", "hasBeenConvicted", "convictionDetails", "declaration", "setDeclaration", "notConnectedToDirectors", "isPartnerOrRelative", "partnerRelativeDetails", "place", "date", "applicantSignature", "handlePersonalDetailsChange", "field", "value", "prev", "handleDocumentDetailsChange", "handlePhysicalDetailsChange", "fileToBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "error", "handleFileUpload", "section", "index", "maxSize", "size", "alert", "undefined", "newEducation", "addLanguageSkill", "addFamilyMember", "addWorkExperience", "addEducation", "getStepTitle", "step", "titles", "nextStep", "console", "log", "prevStep", "validateRequired<PERSON><PERSON>s", "_personalDetails$firs", "_personalDetails$last", "_personalDetails$date", "_personalDetails$gend", "_personalDetails$nati", "_personalDetails$cont", "_personalDetails$emer", "requiredFields", "trim", "every", "Boolean", "handleSubmit", "event", "preventDefault", "formData", "FormData", "textData", "map", "edu", "submissionTimestamp", "Date", "toISOString", "formVersion", "append", "JSON", "stringify", "for<PERSON>ach", "fileCount", "url", "response", "post", "headers", "timeout", "data", "status", "downloadJsonFile", "Error", "_error$response$data", "message", "request", "jsonString", "blob", "Blob", "type", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "testApiConnection", "testFormData", "test", "timestamp", "fillSampleData", "previewFormData", "previewData", "fileUploadInfo", "totalFiles", "filter", "length", "uploadMethod", "renderStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "width", "viewBox", "fill", "stroke", "strokeWidth", "d", "cx", "cy", "r", "accept", "required", "onChange", "e", "_e$target$files", "target", "files", "_e$target$files2", "_e$target$files3", "_e$target$files4", "skill", "newSkills", "checked", "onClick", "member", "newMembers", "_e$target$files5", "experience", "newExperience", "placeholder", "closeModal", "style", "onSubmit", "form", "querySelector", "requestSubmit", "navigator", "clipboard", "writeText", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/On-boarding_Form_App/src/OnboardingForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport './OnboardingForm.css';\nimport { useParams } from 'react-router-dom';\n\ninterface PersonalDetails {\n  firstName: string;\n  middleName: string;\n  lastName: string;\n  dateOfBirth: string;\n  gender: string;\n  birthPlace: string;\n  age: string;\n  marriageDate: string;\n  maritalStatus: string;\n  nationality: string;\n  religion: string;\n  nativeState: string;\n  contactNumber: string;\n  emergencyContactNo: string;\n  stateOfDomicile: string;\n  email: string;\n  presentAddress: string;\n  permanentAddress: string;\n  presentPin: string;\n  permanentPin: string;\n  passportPhoto: File | null;\n}\n\ninterface LanguageSkills {\n  language: string;\n  speak: boolean;\n  read: boolean;\n  write: boolean;\n}\n\ninterface DocumentDetails {\n  passportNo: string;\n  passportIssueDate: string;\n  validUptoDate: string;\n  countryOfIssue: string;\n  validVisaDetails: string;\n  panNumber: string;\n  aadharNumber: string;\n  passportFile: File | null;\n  panFile: File | null;\n  aadharFile: File | null;\n}\n\ninterface FamilyMember {\n  name: string;\n  relationship: string;\n  dateOfBirth: string;\n  qualification: string;\n  occupation: string;\n  organisationAndPosition: string;\n}\n\ninterface PhysicalDetails {\n  height: string;\n  weight: string;\n  bloodGroup: string;\n  eyesightRight: string;\n  eyesightLeft: string;\n  physicalDisability: string;\n  identificationMark: string;\n}\n\ninterface PreviousEmployment {\n  hasBeenInterviewed: boolean;\n  interviewDate: string;\n  interviewPosition: string;\n  interviewCompany: string;\n}\n\ninterface References {\n  relativesInCompany: {\n    name: string;\n    relationship: string;\n    position: string;\n    companyAndPhone: string;\n  };\n  howDidYouKnow: string;\n  personalBusiness: boolean;\n  businessNature: string;\n  contractWithPreviousEmployer: boolean;\n  contractDetails: string;\n  whenCanJoin: string;\n}\n\ninterface WorkExperience {\n  employerName: string;\n  address: string;\n  duration: string;\n  fromDate: string;\n  toDate: string;\n  lastPosition: string;\n  designation: string;\n  natureOfDuties: string;\n  immediateSuperior: string;\n  grossEmoluments: string;\n  lastDrawn: string;\n  basicSalary: string;\n  fixedSalary: string;\n  variableSalary: string;\n  grossSalary: string;\n}\n\ninterface Education {\n  examination: string;\n  specialisation: string;\n  schoolCollege: string;\n  university: string;\n  fullTimePartTime: string;\n  duration: string;\n  monthYearPassing: string;\n  gradeMarks: string;\n  distinctions: string;\n  certificateFile: File | null;\n}\n\nconst OnboardingForm: React.FC = () => {\n  // State for user feedback\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [errorMessage, setErrorMessage] = useState<string | null>(null);\n  const [showJsonPreview, setShowJsonPreview] = useState<boolean>(false);\n  const [jsonPreviewData, setJsonPreviewData] = useState<string>('');\n  const { token } = useParams<{ token: string }>();\n  const [personalDetails, setPersonalDetails] = useState<PersonalDetails>({\n    firstName: ' ',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    birthPlace: '',\n    age: '',\n    marriageDate: '',\n    maritalStatus: '',\n    nationality: '',\n    religion: '',\n    nativeState: '',\n    contactNumber: '',\n    emergencyContactNo: '',\n    stateOfDomicile: '',\n    email: '',\n    presentAddress: '',\n    permanentAddress: '',\n    presentPin: '',\n    permanentPin: '',\n    passportPhoto: null\n  });\n\n  const [languageSkills, setLanguageSkills] = useState<LanguageSkills[]>([\n    { language: '', speak: false, read: false, write: false }\n  ]);\n\n  const [documentDetails, setDocumentDetails] = useState<DocumentDetails>({\n    passportNo: '',\n    passportIssueDate: '',\n    validUptoDate: '',\n    countryOfIssue: '',\n    validVisaDetails: '',\n    panNumber: '',\n    aadharNumber: '',\n    passportFile: null,\n    panFile: null,\n    aadharFile: null\n  });\n\n  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([\n    { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }\n  ]);\n\n  const [physicalDetails, setPhysicalDetails] = useState<PhysicalDetails>({\n    height: '',\n    weight: '',\n    bloodGroup: '',\n    eyesightRight: '',\n    eyesightLeft: '',\n    physicalDisability: '',\n    identificationMark: ''\n  });\n\n  const [previousEmployment, setPreviousEmployment] = useState<PreviousEmployment>({\n    hasBeenInterviewed: false,\n    interviewDate: '',\n    interviewPosition: '',\n    interviewCompany: ''\n  });\n\n  const [references, setReferences] = useState<References>({\n    relativesInCompany: {\n      name: '',\n      relationship: '',\n      position: '',\n      companyAndPhone: ''\n    },\n    howDidYouKnow: '',\n    personalBusiness: false,\n    businessNature: '',\n    contractWithPreviousEmployer: false,\n    contractDetails: '',\n    whenCanJoin: ''\n  });\n\n  const [workExperience, setWorkExperience] = useState<WorkExperience[]>([\n    {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }\n  ]);\n\n  const [education, setEducation] = useState<Education[]>([\n    {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }\n  ]);\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 8;\n\n  // Additional state for new sections\n  const [criminalRecords, setCriminalRecords] = useState({\n    hasBeenInvolved: false,\n    details: '',\n    hasBeenConvicted: false,\n    convictionDetails: ''\n  });\n\n  const [declaration, setDeclaration] = useState({\n    notConnectedToDirectors: false,\n    isPartnerOrRelative: false,\n    partnerRelativeDetails: '',\n    place: '',\n    date: '',\n    applicantSignature: ''\n  });\n\n  const handlePersonalDetailsChange = (field: keyof PersonalDetails, value: string) => {\n    setPersonalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleDocumentDetailsChange = (field: keyof DocumentDetails, value: string) => {\n    setDocumentDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handlePhysicalDetailsChange = (field: keyof PhysicalDetails, value: string) => {\n    setPhysicalDetails(prev => ({ ...prev, [field]: value }));\n  };\n\n  // Helper function to convert file to base64\n  const fileToBase64 = (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result as string);\n      reader.onerror = error => reject(error);\n    });\n  };\n\n  const handleFileUpload = (file: File | null, section: 'personal' | 'document' | 'education', field: string, index?: number) => {\n    // Check file size for passport photo (100KB limit)\n    if (file && section === 'personal' && field === 'passportPhoto') {\n      const maxSize = 100 * 1024; // 100KB in bytes\n      if (file.size > maxSize) {\n        alert('Please upload an image less than 100KB');\n        return;\n      }\n    }\n\n    if (section === 'personal') {\n      setPersonalDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'document') {\n      setDocumentDetails(prev => ({ ...prev, [field]: file }));\n    } else if (section === 'education' && index !== undefined) {\n      setEducation(prev => {\n        const newEducation = [...prev];\n        newEducation[index] = { ...newEducation[index], [field]: file };\n        return newEducation;\n      });\n    }\n  };\n\n  const addLanguageSkill = () => {\n    setLanguageSkills(prev => [...prev, { language: '', speak: false, read: false, write: false }]);\n  };\n\n  const addFamilyMember = () => {\n    setFamilyMembers(prev => [...prev, { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }]);\n  };\n\n  const addWorkExperience = () => {\n    setWorkExperience(prev => [...prev, {\n      employerName: '',\n      address: '',\n      duration: '',\n      fromDate: '',\n      toDate: '',\n      lastPosition: '',\n      designation: '',\n      natureOfDuties: '',\n      immediateSuperior: '',\n      grossEmoluments: '',\n      lastDrawn: '',\n      basicSalary: '',\n      fixedSalary: '',\n      variableSalary: '',\n      grossSalary: ''\n    }]);\n  };\n\n  const addEducation = () => {\n    setEducation(prev => [...prev, {\n      examination: '',\n      specialisation: '',\n      schoolCollege: '',\n      university: '',\n      fullTimePartTime: '',\n      duration: '',\n      monthYearPassing: '',\n      gradeMarks: '',\n      distinctions: '',\n      certificateFile: null\n    }]);\n  };\n\n  const getStepTitle = (step: number) => {\n    const titles = [\n      '',\n      'PERSONAL DETAILS',\n      'FAMILY DATA',\n      'HEALTH DATA',\n      'EDUCATION DETAILS',\n      'WORK HISTORY DATA',\n      'GENERAL DATA',\n      'CRIMINAL RECORDS',\n      'DECLARATION FORM'\n    ];\n    return titles[step] || '';\n  };\n\n  const nextStep = () => {\n    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n      console.log('Moving to step:', currentStep + 1);\n    } else {\n      console.log('Already on final step, not moving forward');\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Helper: Validate required fields (add more as needed)\n  const validateRequiredFields = () => {\n    const requiredFields = [\n      personalDetails.firstName?.trim(),\n      personalDetails.lastName?.trim(),\n      personalDetails.dateOfBirth?.trim(),\n      personalDetails.gender?.trim(),\n      personalDetails.nationality?.trim(),\n      personalDetails.contactNumber?.trim(),\n      personalDetails.emergencyContactNo?.trim(),\n    ];\n    return requiredFields.every(Boolean);\n  };\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    setSuccessMessage(null);\n    setErrorMessage(null);\n\n    // Validate all required fields before submission\n    if (!validateRequiredFields()) {\n      setErrorMessage(\"Please fill all required fields marked with * before submitting the form.\");\n      return;\n    }\n    if (currentStep !== totalSteps) {\n      setErrorMessage(\"Please complete all steps before submitting the form.\");\n      return;\n    }\n\n    try {\n      // Create FormData for file uploads\n      const formData = new FormData();\n\n      // Add text data as JSON\n      const textData = {\n        personalDetails: {\n          ...personalDetails,\n          passportPhoto: null // Remove file reference\n        },\n        languageSkills,\n        documentDetails: {\n          ...documentDetails,\n          passportFile: null,\n          panFile: null,\n          aadharFile: null\n        },\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: education.map(edu => ({\n          ...edu,\n          certificateFile: null\n        })),\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\"\n      };\n\n      // Add JSON data\n      formData.append('form_data', JSON.stringify(textData));\n\n      // Add files with descriptive names\n      if (personalDetails.passportPhoto) {\n        formData.append('passport_photo', personalDetails.passportPhoto, 'passport_photo.jpg');\n      }\n\n      if (documentDetails.passportFile) {\n        formData.append('passport_document', documentDetails.passportFile, 'passport_document.pdf');\n      }\n\n      if (documentDetails.panFile) {\n        formData.append('pan_document', documentDetails.panFile, 'pan_document.pdf');\n      }\n\n      if (documentDetails.aadharFile) {\n        formData.append('aadhar_document', documentDetails.aadharFile, 'aadhar_document.pdf');\n      }\n\n      // Add education certificates\n      education.forEach((edu, index) => {\n        if (edu.certificateFile) {\n          formData.append(`education_certificate_${index}`, edu.certificateFile, `education_certificate_${index}.pdf`);\n        }\n      });\n\n      // Log the form data for debugging\n      console.log(\"Form Data Contents:\");\n      console.log(\"Text data:\", JSON.stringify(textData, null, 2));\n\n      // Count files\n      let fileCount = 0;\n      if (personalDetails.passportPhoto) fileCount++;\n      if (documentDetails.passportFile) fileCount++;\n      if (documentDetails.panFile) fileCount++;\n      if (documentDetails.aadharFile) fileCount++;\n      education.forEach(edu => { if (edu.certificateFile) fileCount++; });\n      console.log(`Total files to upload: ${fileCount}`);\n\n      // Send to API\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n\n      console.log(\"Sending FormData to URL:\", url);\n      console.log(\"Token:\", token);\n\n      const response = await axios.post(url, formData, {\n        headers: {\n          // Don't set Content-Type for FormData - let browser set it with boundary\n        },\n        timeout: 60000 // 60 second timeout for file uploads\n      });\n\n      console.log(\"API Response:\", response.data);\n      console.log(\"Response Status:\", response.status);\n\n      if (response.status === 200 || response.status === 201) {\n        setSuccessMessage(\"Form submitted successfully!\");\n        setErrorMessage(null);\n\n        // Optional: Download JSON file locally\n        downloadJsonFile(textData);\n      } else {\n        throw new Error(`Unexpected response status: ${response.status}`);\n      }\n\n    } catch (error: any) {\n      console.error(\"Form submission error:\", error);\n\n      // Detailed error logging\n      if (error.response) {\n        console.error(\"Error Response Data:\", error.response.data);\n        console.error(\"Error Response Status:\", error.response.status);\n        console.error(\"Error Response Headers:\", error.response.headers);\n        setErrorMessage(`Server Error (${error.response.status}): ${error.response.data?.message || 'Unknown error'}`);\n      } else if (error.request) {\n        console.error(\"No response received:\", error.request);\n        setErrorMessage(\"No response from server. Please check your internet connection and try again.\");\n      } else {\n        console.error(\"Request setup error:\", error.message);\n        setErrorMessage(`Request Error: ${error.message}`);\n      }\n\n      setSuccessMessage(null);\n    }\n  };\n\n  // Function to download JSON file locally\n  const downloadJsonFile = (data: any) => {\n    const jsonString = JSON.stringify(data, null, 2);\n    const blob = new Blob([jsonString], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `onboarding-form-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  // Function to test API connection\n  const testApiConnection = async () => {\n    try {\n      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;\n      console.log(\"Testing API connection to:\", url);\n\n      // Create test FormData\n      const testFormData = new FormData();\n      testFormData.append('form_data', JSON.stringify({\n        test: true,\n        timestamp: new Date().toISOString(),\n        message: \"API connection test\"\n      }));\n\n      const response = await axios.post(url, testFormData, {\n        timeout: 10000\n      });\n\n      console.log(\"API Test Response:\", response);\n      alert(`API Connection Successful!\\nStatus: ${response.status}\\nResponse: ${JSON.stringify(response.data)}`);\n\n    } catch (error: any) {\n      console.error(\"API Test Error:\", error);\n\n      if (error.response) {\n        alert(`API Test Failed!\\nStatus: ${error.response.status}\\nError: ${JSON.stringify(error.response.data)}`);\n      } else if (error.request) {\n        alert(\"API Test Failed!\\nNo response from server. Check if the backend is running and accessible.\");\n      } else {\n        alert(`API Test Failed!\\nError: ${error.message}`);\n      }\n    }\n  };\n\n  // Function to fill sample data for testing\n  const fillSampleData = () => {\n    setPersonalDetails({\n      firstName: 'John',\n      middleName: 'Michael',\n      lastName: 'Doe',\n      dateOfBirth: '1990-01-15',\n      gender: 'Male',\n      birthPlace: 'New York',\n      age: '34',\n      marriageDate: '2020-06-15',\n      maritalStatus: 'Married',\n      nationality: 'American',\n      religion: 'Christian',\n      nativeState: 'New York',\n      contactNumber: '+1-555-0123',\n      emergencyContactNo: '+1-555-0124',\n      stateOfDomicile: 'New York',\n      email: '<EMAIL>',\n      presentAddress: '123 Main Street, Apartment 4B, New York, NY 10001',\n      permanentAddress: '456 Oak Avenue, Hometown, NY 12345',\n      presentPin: '10001',\n      permanentPin: '12345',\n      passportPhoto: null\n    });\n\n    setLanguageSkills([\n      { language: 'English', speak: true, read: true, write: true },\n      { language: 'Spanish', speak: true, read: false, write: false }\n    ]);\n\n    setDocumentDetails({\n      passportNo: '*********',\n      passportIssueDate: '2020-01-01',\n      validUptoDate: '2030-01-01',\n      countryOfIssue: 'USA',\n      validVisaDetails: 'Valid until 2025',\n      panNumber: '**********',\n      aadharNumber: '1234-5678-9012',\n      passportFile: null,\n      panFile: null,\n      aadharFile: null\n    });\n\n    alert('Sample data filled! You can now preview the JSON structure.');\n  };\n\n  // Function to preview current form data as JSON\n  const previewFormData = async () => {\n    try {\n      // Create preview data showing file information\n      const previewData = {\n        personalDetails: {\n          ...personalDetails,\n          passportPhoto: personalDetails.passportPhoto ?\n            `[FILE: ${personalDetails.passportPhoto.name} (${personalDetails.passportPhoto.size} bytes)]` : null\n        },\n        languageSkills,\n        documentDetails: {\n          ...documentDetails,\n          passportFile: documentDetails.passportFile ?\n            `[FILE: ${documentDetails.passportFile.name} (${documentDetails.passportFile.size} bytes)]` : null,\n          panFile: documentDetails.panFile ?\n            `[FILE: ${documentDetails.panFile.name} (${documentDetails.panFile.size} bytes)]` : null,\n          aadharFile: documentDetails.aadharFile ?\n            `[FILE: ${documentDetails.aadharFile.name} (${documentDetails.aadharFile.size} bytes)]` : null\n        },\n        familyMembers,\n        physicalDetails,\n        previousEmployment,\n        references,\n        workExperience,\n        education: education.map((edu) => ({\n          ...edu,\n          certificateFile: edu.certificateFile ?\n            `[FILE: ${edu.certificateFile.name} (${edu.certificateFile.size} bytes)]` : null\n        })),\n        criminalRecords,\n        declaration,\n        submissionTimestamp: new Date().toISOString(),\n        formVersion: \"1.0\",\n        fileUploadInfo: {\n          totalFiles: [\n            personalDetails.passportPhoto,\n            documentDetails.passportFile,\n            documentDetails.panFile,\n            documentDetails.aadharFile,\n            ...education.map(edu => edu.certificateFile)\n          ].filter(Boolean).length,\n          uploadMethod: \"FormData (multipart/form-data)\"\n        }\n      };\n\n      // Show JSON in console and modal\n      const jsonString = JSON.stringify(previewData, null, 2);\n      console.log(\"Form Data Preview:\", jsonString);\n      setJsonPreviewData(jsonString);\n      setShowJsonPreview(true);\n\n      // Optional: Download preview JSON\n      downloadJsonFile(previewData);\n    } catch (error) {\n      console.error(\"Preview error:\", error);\n      alert(\"Error generating preview\");\n    }\n  };\n\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"form-section\">\n            <h3>PERSONAL DETAILS</h3>\n\n            {/* Passport Photo Upload Section */}\n            <div className=\"photo-upload-section\">\n              <div className=\"photo-placeholder\">\n                <div className=\"photo-frame\">\n                  {personalDetails.passportPhoto ? (\n                    <img\n                      src={URL.createObjectURL(personalDetails.passportPhoto)}\n                      alt=\"Passport Photo\"\n                      className=\"uploaded-photo\"\n                    />\n                  ) : (\n                    <div className=\"placeholder-icon\">\n                      <svg width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n                      </svg>\n                    </div>\n                  )}\n                </div>\n                <span className=\"photo-label\">Uploaded Photo</span>\n              </div>\n              <div className=\"upload-area\">\n                <div className=\"upload-content\">\n                  <p className=\"upload-text\">Please upload a image less than 100kb</p>\n                  <label className=\"upload-button\">\n                    Choose a file\n                    <input\n                      type=\"file\"\n                      accept=\"image/*\"\n                      required\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'personal', 'passportPhoto')}\n                      className=\"hidden-file-input\"\n                    />\n                  </label>\n                  {personalDetails.passportPhoto && (\n                    <p className=\"file-info\">{personalDetails.passportPhoto.name}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>First Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.firstName}\n                  onChange={(e) => handlePersonalDetailsChange('firstName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Middle Name</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.middleName}\n                  onChange={(e) => handlePersonalDetailsChange('middleName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Last Name *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.lastName}\n                  onChange={(e) => handlePersonalDetailsChange('lastName', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Date of Birth *</label>\n                <input\n                  type=\"date\"\n                  required\n                  value={personalDetails.dateOfBirth}\n                  onChange={(e) => handlePersonalDetailsChange('dateOfBirth', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Gender *</label>\n                <select\n                  required\n                  value={personalDetails.gender}\n                  onChange={(e) => handlePersonalDetailsChange('gender', e.target.value)}\n                >\n                  <option value=\"\">Select Gender</option>\n                  <option value=\"Male\">Male</option>\n                  <option value=\"Female\">Female</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Birth Place</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.birthPlace}\n                  onChange={(e) => handlePersonalDetailsChange('birthPlace', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Age</label>\n                <input\n                  type=\"number\"\n                  value={personalDetails.age}\n                  onChange={(e) => handlePersonalDetailsChange('age', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marriage Date</label>\n                <input\n                  type=\"date\"\n                  value={personalDetails.marriageDate}\n                  onChange={(e) => handlePersonalDetailsChange('marriageDate', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Marital Status</label>\n                <select\n                  value={personalDetails.maritalStatus}\n                  onChange={(e) => handlePersonalDetailsChange('maritalStatus', e.target.value)}\n                >\n                  <option value=\"\">Select Status</option>\n                  <option value=\"Single\">Single</option>\n                  <option value=\"Married\">Married</option>\n                  <option value=\"Divorced\">Divorced</option>\n                  <option value=\"Widowed\">Widowed</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Nationality *</label>\n                <input\n                  type=\"text\"\n                  required\n                  value={personalDetails.nationality}\n                  onChange={(e) => handlePersonalDetailsChange('nationality', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Religion</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.religion}\n                  onChange={(e) => handlePersonalDetailsChange('religion', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Native State</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.nativeState}\n                  onChange={(e) => handlePersonalDetailsChange('nativeState', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Contact Number *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.contactNumber}\n                  onChange={(e) => handlePersonalDetailsChange('contactNumber', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Emergency Contact No. *</label>\n                <input\n                  type=\"tel\"\n                  required\n                  value={personalDetails.emergencyContactNo}\n                  onChange={(e) => handlePersonalDetailsChange('emergencyContactNo', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>State of Domicile</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.stateOfDomicile}\n                  onChange={(e) => handlePersonalDetailsChange('stateOfDomicile', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Email *</label>\n                <input\n                  type=\"email\"\n                  required\n                  value={personalDetails.email}\n                  onChange={(e) => handlePersonalDetailsChange('email', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Present Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.presentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('presentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group full-width\">\n                <label>Permanent Address *</label>\n                <textarea\n                  required\n                  value={personalDetails.permanentAddress}\n                  onChange={(e) => handlePersonalDetailsChange('permanentAddress', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Present PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.presentPin}\n                  onChange={(e) => handlePersonalDetailsChange('presentPin', e.target.value)}\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Permanent PIN</label>\n                <input\n                  type=\"text\"\n                  value={personalDetails.permanentPin}\n                  onChange={(e) => handlePersonalDetailsChange('permanentPin', e.target.value)}\n                />\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Document Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Passport No.</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.passportNo}\n                    onChange={(e) => handleDocumentDetailsChange('passportNo', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Issue Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.passportIssueDate}\n                    onChange={(e) => handleDocumentDetailsChange('passportIssueDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Upto Date</label>\n                  <input\n                    type=\"date\"\n                    value={documentDetails.validUptoDate}\n                    onChange={(e) => handleDocumentDetailsChange('validUptoDate', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Country of Issue</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.countryOfIssue}\n                    onChange={(e) => handleDocumentDetailsChange('countryOfIssue', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Valid Visa Details</label>\n                  <input\n                    type=\"text\"\n                    value={documentDetails.validVisaDetails}\n                    onChange={(e) => handleDocumentDetailsChange('validVisaDetails', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.panNumber}\n                    onChange={(e) => handleDocumentDetailsChange('panNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>PAN Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'panFile')}\n                  />\n                  {documentDetails.panFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.panFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Number *</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={documentDetails.aadharNumber}\n                    onChange={(e) => handleDocumentDetailsChange('aadharNumber', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Aadhar Card Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'aadharFile')}\n                  />\n                  {documentDetails.aadharFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.aadharFile.name}</span>\n                    </div>\n                  )}\n                </div>\n                <div className=\"form-group\">\n                  <label>Passport Upload</label>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*,.pdf\"\n                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'passportFile')}\n                  />\n                  {documentDetails.passportFile && (\n                    <div className=\"file-preview\">\n                      <span className=\"file-name\">{documentDetails.passportFile.name}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>Language Known</h4>\n              {languageSkills.map((skill, index) => (\n                <div key={index} className=\"language-skill-row\">\n                  <div className=\"form-group\">\n                    <label>Language</label>\n                    <input\n                      type=\"text\"\n                      value={skill.language}\n                      onChange={(e) => {\n                        const newSkills = [...languageSkills];\n                        newSkills[index].language = e.target.value;\n                        setLanguageSkills(newSkills);\n                      }}\n                    />\n                  </div>\n                  <div className=\"checkbox-group\">\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.speak}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].speak = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Speak\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.read}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].read = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Read\n                    </label>\n                    <label>\n                      <input\n                        type=\"checkbox\"\n                        checked={skill.write}\n                        onChange={(e) => {\n                          const newSkills = [...languageSkills];\n                          newSkills[index].write = e.target.checked;\n                          setLanguageSkills(newSkills);\n                        }}\n                      />\n                      Write\n                    </label>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addLanguageSkill} className=\"btn-add\">\n                Add Language\n              </button>\n            </div>\n          </div>\n        );\n      case 2:\n        return (\n          <div className=\"form-section\">\n            <h3>FAMILY DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Family Members</h4>\n              {familyMembers.map((member, index) => (\n                <div key={index} className=\"family-member-section\">\n                  <div className=\"form-grid\">\n                    <div className=\"form-group\">\n                      <label>Name</label>\n                      <input\n                        type=\"text\"\n                        value={member.name}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].name = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Relationship</label>\n                      <input\n                        type=\"text\"\n                        value={member.relationship}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].relationship = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Date of Birth</label>\n                      <input\n                        type=\"date\"\n                        value={member.dateOfBirth}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].dateOfBirth = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Qualification</label>\n                      <input\n                        type=\"text\"\n                        value={member.qualification}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].qualification = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Occupation</label>\n                      <input\n                        type=\"text\"\n                        value={member.occupation}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].occupation = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Organisation and Position</label>\n                      <input\n                        type=\"text\"\n                        value={member.organisationAndPosition}\n                        onChange={(e) => {\n                          const newMembers = [...familyMembers];\n                          newMembers[index].organisationAndPosition = e.target.value;\n                          setFamilyMembers(newMembers);\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n              ))}\n              <button type=\"button\" onClick={addFamilyMember} className=\"btn-add\">\n                Add Family Member\n              </button>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"form-section\">\n            <h3>HEALTH DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Physical Details</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Height</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.height}\n                    onChange={(e) => handlePhysicalDetailsChange('height', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Weight</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.weight}\n                    onChange={(e) => handlePhysicalDetailsChange('weight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Blood Group</label>\n                  <select\n                    value={physicalDetails.bloodGroup}\n                    onChange={(e) => handlePhysicalDetailsChange('bloodGroup', e.target.value)}\n                  >\n                    <option value=\"\">Select Blood Group</option>\n                    <option value=\"A+\">A+</option>\n                    <option value=\"A-\">A-</option>\n                    <option value=\"B+\">B+</option>\n                    <option value=\"B-\">B-</option>\n                    <option value=\"AB+\">AB+</option>\n                    <option value=\"AB-\">AB-</option>\n                    <option value=\"O+\">O+</option>\n                    <option value=\"O-\">O-</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Right</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightRight}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightRight', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Eyesight Left</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.eyesightLeft}\n                    onChange={(e) => handlePhysicalDetailsChange('eyesightLeft', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Physical Disability</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.physicalDisability}\n                    onChange={(e) => handlePhysicalDetailsChange('physicalDisability', e.target.value)}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Identification Mark</label>\n                  <input\n                    type=\"text\"\n                    value={physicalDetails.identificationMark}\n                    onChange={(e) => handlePhysicalDetailsChange('identificationMark', e.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"form-section\">\n            <h3>EDUCATION DETAILS</h3>\n\n            {education.map((edu, index) => (\n              <div key={index} className=\"education-section\">\n                <h4>Education {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Examination Passed</label>\n                    <input\n                      type=\"text\"\n                      value={edu.examination}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].examination = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Specialisation</label>\n                    <input\n                      type=\"text\"\n                      value={edu.specialisation}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].specialisation = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>School/College/Institution</label>\n                    <input\n                      type=\"text\"\n                      value={edu.schoolCollege}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].schoolCollege = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>University/Board</label>\n                    <input\n                      type=\"text\"\n                      value={edu.university}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].university = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Full Time/Part Time/Correspondence</label>\n                    <select\n                      value={edu.fullTimePartTime}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].fullTimePartTime = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    >\n                      <option value=\"\">Select Type</option>\n                      <option value=\"Full Time\">Full Time</option>\n                      <option value=\"Part Time\">Part Time</option>\n                      <option value=\"Correspondence\">Correspondence</option>\n                    </select>\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Duration of Course</label>\n                    <input\n                      type=\"text\"\n                      value={edu.duration}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].duration = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Month & Year of Passing</label>\n                    <input\n                      type=\"text\"\n                      value={edu.monthYearPassing}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].monthYearPassing = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Grade/% Marks</label>\n                    <input\n                      type=\"text\"\n                      value={edu.gradeMarks}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].gradeMarks = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Distinctions/Scholarships/Prizes Won</label>\n                    <textarea\n                      value={edu.distinctions}\n                      onChange={(e) => {\n                        const newEducation = [...education];\n                        newEducation[index].distinctions = e.target.value;\n                        setEducation(newEducation);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Certificate/Document Upload</label>\n                    <input\n                      type=\"file\"\n                      accept=\"image/*,.pdf\"\n                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'education', 'certificateFile', index)}\n                    />\n                    {edu.certificateFile && (\n                      <div className=\"file-preview\">\n                        <span className=\"file-name\">{edu.certificateFile.name}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addEducation} className=\"btn-add\">\n              Add Education\n            </button>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"form-section\">\n            <h3>WORK HISTORY DATA</h3>\n\n            {workExperience.map((experience, index) => (\n              <div key={index} className=\"work-experience-section\">\n                <h4>Work Experience {index + 1}</h4>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Employer's Name</label>\n                    <input\n                      type=\"text\"\n                      value={experience.employerName}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].employerName = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Address</label>\n                    <textarea\n                      value={experience.address}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].address = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>From Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.fromDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fromDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>To Date</label>\n                    <input\n                      type=\"date\"\n                      value={experience.toDate}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].toDate = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Position Held</label>\n                    <input\n                      type=\"text\"\n                      value={experience.lastPosition}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastPosition = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Designation</label>\n                    <input\n                      type=\"text\"\n                      value={experience.designation}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].designation = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group full-width\">\n                    <label>Nature of Duties</label>\n                    <textarea\n                      value={experience.natureOfDuties}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].natureOfDuties = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Name & Designation of Immediate Superior</label>\n                    <input\n                      type=\"text\"\n                      value={experience.immediateSuperior}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].immediateSuperior = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Emoluments (Rs. Per month at joining)</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossEmoluments}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossEmoluments = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Last Drawn</label>\n                    <input\n                      type=\"number\"\n                      value={experience.lastDrawn}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].lastDrawn = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Basic Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.basicSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].basicSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Fixed Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.fixedSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].fixedSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Variable Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.variableSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].variableSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Gross Salary</label>\n                    <input\n                      type=\"number\"\n                      value={experience.grossSalary}\n                      onChange={(e) => {\n                        const newExperience = [...workExperience];\n                        newExperience[index].grossSalary = e.target.value;\n                        setWorkExperience(newExperience);\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addWorkExperience} className=\"btn-add\">\n              Add Work Experience\n            </button>\n          </div>\n        );\n\n      case 6:\n        return (\n          <div className=\"form-section\">\n            <h3>GENERAL DATA</h3>\n\n            <div className=\"subsection\">\n              <h4>Previous Interview with MH Group</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Have you ever been interviewed by any of the MH Group of Companies?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"true\"\n                        checked={previousEmployment.hasBeenInterviewed === true}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"hasBeenInterviewed\"\n                        value=\"false\"\n                        checked={previousEmployment.hasBeenInterviewed === false}\n                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {previousEmployment.hasBeenInterviewed && (\n                  <>\n                    <div className=\"form-group\">\n                      <label>Date/Year</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewDate}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewDate: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Position</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewPosition}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewPosition: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"form-group\">\n                      <label>Company</label>\n                      <input\n                        type=\"text\"\n                        value={previousEmployment.interviewCompany}\n                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewCompany: e.target.value }))}\n                      />\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n\n            <div className=\"subsection\">\n              <h4>References & Additional Information</h4>\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>Relatives/Acquaintance in MH Group - Name</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.name}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, name: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Relationship</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.relationship}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, relationship: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Position</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.position}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, position: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Company & Phone No.</label>\n                  <input\n                    type=\"text\"\n                    value={references.relativesInCompany.companyAndPhone}\n                    onChange={(e) => setReferences(prev => ({\n                      ...prev,\n                      relativesInCompany: { ...prev.relativesInCompany, companyAndPhone: e.target.value }\n                    }))}\n                  />\n                </div>\n                <div className=\"form-group full-width\">\n                  <label>How did you come to know of this position?</label>\n                  <textarea\n                    value={references.howDidYouKnow}\n                    onChange={(e) => setReferences(prev => ({ ...prev, howDidYouKnow: e.target.value }))}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Are you engaged in any personal business?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"true\"\n                        checked={references.personalBusiness === true}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"personalBusiness\"\n                        value=\"false\"\n                        checked={references.personalBusiness === false}\n                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.personalBusiness && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, indicate nature of business</label>\n                    <textarea\n                      value={references.businessNature}\n                      onChange={(e) => setReferences(prev => ({ ...prev, businessNature: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>Do you have any contract/bond with your previous employer?</label>\n                  <div className=\"radio-group\">\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"true\"\n                        checked={references.contractWithPreviousEmployer === true}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: true }))}\n                      />\n                      Yes\n                    </label>\n                    <label>\n                      <input\n                        type=\"radio\"\n                        name=\"contractWithPreviousEmployer\"\n                        value=\"false\"\n                        checked={references.contractWithPreviousEmployer === false}\n                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: false }))}\n                      />\n                      No\n                    </label>\n                  </div>\n                </div>\n                {references.contractWithPreviousEmployer && (\n                  <div className=\"form-group full-width\">\n                    <label>If YES, Give Details</label>\n                    <textarea\n                      value={references.contractDetails}\n                      onChange={(e) => setReferences(prev => ({ ...prev, contractDetails: e.target.value }))}\n                    />\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>If selected, when can you join?</label>\n                  <input\n                    type=\"date\"\n                    value={references.whenCanJoin}\n                    onChange={(e) => setReferences(prev => ({ ...prev, whenCanJoin: e.target.value }))}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 7:\n        return (\n          <div className=\"form-section\">\n            <h3>CRIMINAL RECORDS</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Have you ever been involved in any criminal proceedings?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenInvolved === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenInvolved\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenInvolved === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenInvolved && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.details}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, details: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Have you ever been convicted of any offence?</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"true\"\n                      checked={criminalRecords.hasBeenConvicted === true}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"hasBeenConvicted\"\n                      value=\"false\"\n                      checked={criminalRecords.hasBeenConvicted === false}\n                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n              {criminalRecords.hasBeenConvicted && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, give details</label>\n                  <textarea\n                    value={criminalRecords.convictionDetails}\n                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, convictionDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        );\n\n      case 8:\n        return (\n          <div className=\"form-section\">\n            <h3>DECLARATION FORM</h3>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-text\">\n                  <p><strong>I hereby declare that:</strong></p>\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>\n                  <input\n                    type=\"checkbox\"\n                    checked={declaration.notConnectedToDirectors}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, notConnectedToDirectors: e.target.checked }))}\n                  />\n                  I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956\n                </label>\n              </div>\n\n              <div className=\"form-group\">\n                <label>OR I am a partner or relative of a Director of the Company</label>\n                <div className=\"radio-group\">\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"true\"\n                      checked={declaration.isPartnerOrRelative === true}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: true }))}\n                    />\n                    Yes\n                  </label>\n                  <label>\n                    <input\n                      type=\"radio\"\n                      name=\"isPartnerOrRelative\"\n                      value=\"false\"\n                      checked={declaration.isPartnerOrRelative === false}\n                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: false }))}\n                    />\n                    No\n                  </label>\n                </div>\n              </div>\n\n              {declaration.isPartnerOrRelative && (\n                <div className=\"form-group full-width\">\n                  <label>If YES, provide details</label>\n                  <textarea\n                    value={declaration.partnerRelativeDetails}\n                    onChange={(e) => setDeclaration(prev => ({ ...prev, partnerRelativeDetails: e.target.value }))}\n                  />\n                </div>\n              )}\n\n              <div className=\"form-group\">\n                <label>Place</label>\n                <input\n                  type=\"text\"\n                  value={declaration.place}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, place: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label>Date</label>\n                <input\n                  type=\"date\"\n                  value={declaration.date}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, date: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>Applicant's Signature</label>\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your full name as signature\"\n                  value={declaration.applicantSignature}\n                  onChange={(e) => setDeclaration(prev => ({ ...prev, applicantSignature: e.target.value }))}\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <div className=\"declaration-note\">\n                  <p><em>Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.</em></p>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step {currentStep}</div>;\n    }\n  };\n\n  // Modal close handler (must be after useState hooks)\n  const closeModal = () => {\n    setSuccessMessage(null);\n    setErrorMessage(null);\n  };\n\n  return (\n    <div className=\"onboarding-form\">\n      <div className=\"form-header\">\n        <h2>Employee Onboarding Form</h2>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress-fill\"\n            style={{ width: `${(currentStep / totalSteps) * 100}%` }}\n          ></div>\n        </div>\n        <p>Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}</p>\n      </div>\n\n\n      {/* Popup Modal for feedback messages */}\n      {(successMessage || errorMessage) && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content\">\n            <div className={successMessage ? \"success-message\" : \"error-message\"}>\n              {successMessage || errorMessage}\n            </div>\n            <button className=\"modal-close\" onClick={closeModal}>Close</button>\n          </div>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        {renderStep()}\n      </form>\n\n      <div className=\"form-navigation\">\n        <div className=\"nav-left\">\n          {currentStep > 1 && (\n            <button type=\"button\" onClick={prevStep} className=\"btn-secondary\">\n              Previous\n            </button>\n          )}\n        </div>\n\n        <div className=\"nav-center\">\n          <button type=\"button\" onClick={testApiConnection} className=\"btn-test\">\n            Test API\n          </button>\n          <button type=\"button\" onClick={fillSampleData} className=\"btn-sample\">\n            Fill Sample Data\n          </button>\n          <button type=\"button\" onClick={previewFormData} className=\"btn-preview\">\n            Preview JSON\n          </button>\n        </div>\n\n        <div className=\"nav-right\">\n          {currentStep < totalSteps ? (\n            <button type=\"button\" onClick={nextStep} className=\"btn-primary\">\n              Next\n            </button>\n          ) : (\n            <button type=\"button\" onClick={() => {\n              const form = document.querySelector('form');\n              if (form) {\n                form.requestSubmit();\n              }\n            }} className=\"btn-primary\">\n              Submit Form\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* JSON Preview Modal */}\n      {showJsonPreview && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content json-modal\">\n            <h3>Form Data JSON Preview</h3>\n            <pre className=\"json-preview\">\n              {jsonPreviewData}\n            </pre>\n            <div className=\"modal-actions\">\n              <button\n                onClick={() => setShowJsonPreview(false)}\n                className=\"modal-close\"\n              >\n                Close\n              </button>\n              <button\n                onClick={() => {\n                  navigator.clipboard.writeText(jsonPreviewData);\n                  alert('JSON copied to clipboard!');\n                }}\n                className=\"btn-copy\"\n              >\n                Copy JSON\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default OnboardingForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAC7B,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsH7C,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAU,KAAK,CAAC;EACtE,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM;IAAEiB;EAAM,CAAC,GAAGf,SAAS,CAAoB,CAAC;EAChD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAkB;IACtEoB,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,GAAG,EAAE,EAAE;IACPC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAmB,CACrE;IAAE2C,QAAQ,EAAE,EAAE;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC1D,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAkB;IACtEiD,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAiB,CACjE;IAAE6D,IAAI,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEvC,WAAW,EAAE,EAAE;IAAEwC,aAAa,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,uBAAuB,EAAE;EAAG,CAAC,CAChH,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAkB;IACtEoE,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5E,QAAQ,CAAqB;IAC/E6E,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,EAAE;IACrBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAa;IACvDmF,kBAAkB,EAAE;MAClBtB,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBsB,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC;IACDC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,cAAc,EAAE,EAAE;IAClBC,4BAA4B,EAAE,KAAK;IACnCC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7F,QAAQ,CAAmB,CACrE;IACE8F,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAc,CACtD;IACE+G,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBnB,QAAQ,EAAE,EAAE;IACZoB,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE;EACnB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM0H,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC;IACrD6H,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,KAAK;IACvBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlI,QAAQ,CAAC;IAC7CmI,uBAAuB,EAAE,KAAK;IAC9BC,mBAAmB,EAAE,KAAK;IAC1BC,sBAAsB,EAAE,EAAE;IAC1BC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,2BAA2B,GAAGA,CAACC,KAA4B,EAAEC,KAAa,KAAK;IACnFxH,kBAAkB,CAACyH,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,2BAA2B,GAAGA,CAACH,KAA4B,EAAEC,KAAa,KAAK;IACnF3F,kBAAkB,CAAC4F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,2BAA2B,GAAGA,CAACJ,KAA4B,EAAEC,KAAa,KAAK;IACnFxE,kBAAkB,CAACyE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMI,YAAY,GAAIC,IAAU,IAAsB;IACpD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC1BI,MAAM,CAACG,MAAM,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAgB,CAAC;MACtDJ,MAAM,CAACK,OAAO,GAAGC,KAAK,IAAIP,MAAM,CAACO,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACX,IAAiB,EAAEY,OAA8C,EAAElB,KAAa,EAAEmB,KAAc,KAAK;IAC7H;IACA,IAAIb,IAAI,IAAIY,OAAO,KAAK,UAAU,IAAIlB,KAAK,KAAK,eAAe,EAAE;MAC/D,MAAMoB,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;MAC5B,IAAId,IAAI,CAACe,IAAI,GAAGD,OAAO,EAAE;QACvBE,KAAK,CAAC,wCAAwC,CAAC;QAC/C;MACF;IACF;IAEA,IAAIJ,OAAO,KAAK,UAAU,EAAE;MAC1BzI,kBAAkB,CAACyH,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIY,OAAO,KAAK,UAAU,EAAE;MACjC5G,kBAAkB,CAAC4F,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGM;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIY,OAAO,KAAK,WAAW,IAAIC,KAAK,KAAKI,SAAS,EAAE;MACzDnD,YAAY,CAAC8B,IAAI,IAAI;QACnB,MAAMsB,YAAY,GAAG,CAAC,GAAGtB,IAAI,CAAC;QAC9BsB,YAAY,CAACL,KAAK,CAAC,GAAG;UAAE,GAAGK,YAAY,CAACL,KAAK,CAAC;UAAE,CAACnB,KAAK,GAAGM;QAAK,CAAC;QAC/D,OAAOkB,YAAY;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzH,iBAAiB,CAACkG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEjG,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,CAAC,CAAC;EACjG,CAAC;EAED,MAAMsH,eAAe,GAAGA,CAAA,KAAM;IAC5BxG,gBAAgB,CAACgF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAE/E,IAAI,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEvC,WAAW,EAAE,EAAE;MAAEwC,aAAa,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,uBAAuB,EAAE;IAAG,CAAC,CAAC,CAAC;EACtJ,CAAC;EAED,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxE,iBAAiB,CAAC+C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAClC9C,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IACzBxD,YAAY,CAAC8B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC7B7B,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,EAAE;MACpBnB,QAAQ,EAAE,EAAE;MACZoB,gBAAgB,EAAE,EAAE;MACpBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgD,YAAY,GAAIC,IAAY,IAAK;IACrC,MAAMC,MAAM,GAAG,CACb,EAAE,EACF,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,kBAAkB,CACnB;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAI,EAAE;EAC3B,CAAC;EAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEpD,WAAW,EAAE,cAAc,EAAEE,UAAU,CAAC;IACtF,IAAIF,WAAW,GAAGE,UAAU,EAAE;MAC5BD,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;MAC/BmD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEpD,WAAW,GAAG,CAAC,CAAC;IACjD,CAAC,MAAM;MACLmD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIrD,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMsD,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACnC,MAAMC,cAAc,GAAG,EAAAP,qBAAA,GACrB7J,eAAe,CAACE,SAAS,cAAA2J,qBAAA,uBAAzBA,qBAAA,CAA2BQ,IAAI,CAAC,CAAC,GAAAP,qBAAA,GACjC9J,eAAe,CAACI,QAAQ,cAAA0J,qBAAA,uBAAxBA,qBAAA,CAA0BO,IAAI,CAAC,CAAC,GAAAN,qBAAA,GAChC/J,eAAe,CAACK,WAAW,cAAA0J,qBAAA,uBAA3BA,qBAAA,CAA6BM,IAAI,CAAC,CAAC,GAAAL,qBAAA,GACnChK,eAAe,CAACM,MAAM,cAAA0J,qBAAA,uBAAtBA,qBAAA,CAAwBK,IAAI,CAAC,CAAC,GAAAJ,qBAAA,GAC9BjK,eAAe,CAACW,WAAW,cAAAsJ,qBAAA,uBAA3BA,qBAAA,CAA6BI,IAAI,CAAC,CAAC,GAAAH,qBAAA,GACnClK,eAAe,CAACc,aAAa,cAAAoJ,qBAAA,uBAA7BA,qBAAA,CAA+BG,IAAI,CAAC,CAAC,GAAAF,qBAAA,GACrCnK,eAAe,CAACe,kBAAkB,cAAAoJ,qBAAA,uBAAlCA,qBAAA,CAAoCE,IAAI,CAAC,CAAC,CAC3C;IACD,OAAOD,cAAc,CAACE,KAAK,CAACC,OAAO,CAAC;EACtC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,KAAsB,IAAK;IACrDA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBlL,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAI,CAACkK,sBAAsB,CAAC,CAAC,EAAE;MAC7BlK,eAAe,CAAC,2EAA2E,CAAC;MAC5F;IACF;IACA,IAAI4G,WAAW,KAAKE,UAAU,EAAE;MAC9B9G,eAAe,CAAC,uDAAuD,CAAC;MACxE;IACF;IAEA,IAAI;MACF;MACA,MAAMiL,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACA,MAAMC,QAAQ,GAAG;QACf7K,eAAe,EAAE;UACf,GAAGA,eAAe;UAClBsB,aAAa,EAAE,IAAI,CAAC;QACtB,CAAC;QACDC,cAAc;QACdM,eAAe,EAAE;UACf,GAAGA,eAAe;UAClBS,YAAY,EAAE,IAAI;UAClBC,OAAO,EAAE,IAAI;UACbC,UAAU,EAAE;QACd,CAAC;QACDC,aAAa;QACbO,eAAe;QACfS,kBAAkB;QAClBM,UAAU;QACVW,cAAc;QACdiB,SAAS,EAAEA,SAAS,CAACmF,GAAG,CAACC,GAAG,KAAK;UAC/B,GAAGA,GAAG;UACN1E,eAAe,EAAE;QACnB,CAAC,CAAC,CAAC;QACHI,eAAe;QACfM,WAAW;QACXiE,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7CC,WAAW,EAAE;MACf,CAAC;;MAED;MACAR,QAAQ,CAACS,MAAM,CAAC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAACT,QAAQ,CAAC,CAAC;;MAEtD;MACA,IAAI7K,eAAe,CAACsB,aAAa,EAAE;QACjCqJ,QAAQ,CAACS,MAAM,CAAC,gBAAgB,EAAEpL,eAAe,CAACsB,aAAa,EAAE,oBAAoB,CAAC;MACxF;MAEA,IAAIO,eAAe,CAACS,YAAY,EAAE;QAChCqI,QAAQ,CAACS,MAAM,CAAC,mBAAmB,EAAEvJ,eAAe,CAACS,YAAY,EAAE,uBAAuB,CAAC;MAC7F;MAEA,IAAIT,eAAe,CAACU,OAAO,EAAE;QAC3BoI,QAAQ,CAACS,MAAM,CAAC,cAAc,EAAEvJ,eAAe,CAACU,OAAO,EAAE,kBAAkB,CAAC;MAC9E;MAEA,IAAIV,eAAe,CAACW,UAAU,EAAE;QAC9BmI,QAAQ,CAACS,MAAM,CAAC,iBAAiB,EAAEvJ,eAAe,CAACW,UAAU,EAAE,qBAAqB,CAAC;MACvF;;MAEA;MACAmD,SAAS,CAAC4F,OAAO,CAAC,CAACR,GAAG,EAAEpC,KAAK,KAAK;QAChC,IAAIoC,GAAG,CAAC1E,eAAe,EAAE;UACvBsE,QAAQ,CAACS,MAAM,CAAC,yBAAyBzC,KAAK,EAAE,EAAEoC,GAAG,CAAC1E,eAAe,EAAE,yBAAyBsC,KAAK,MAAM,CAAC;QAC9G;MACF,CAAC,CAAC;;MAEF;MACAc,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClCD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2B,IAAI,CAACC,SAAS,CAACT,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE5D;MACA,IAAIW,SAAS,GAAG,CAAC;MACjB,IAAIxL,eAAe,CAACsB,aAAa,EAAEkK,SAAS,EAAE;MAC9C,IAAI3J,eAAe,CAACS,YAAY,EAAEkJ,SAAS,EAAE;MAC7C,IAAI3J,eAAe,CAACU,OAAO,EAAEiJ,SAAS,EAAE;MACxC,IAAI3J,eAAe,CAACW,UAAU,EAAEgJ,SAAS,EAAE;MAC3C7F,SAAS,CAAC4F,OAAO,CAACR,GAAG,IAAI;QAAE,IAAIA,GAAG,CAAC1E,eAAe,EAAEmF,SAAS,EAAE;MAAE,CAAC,CAAC;MACnE/B,OAAO,CAACC,GAAG,CAAC,0BAA0B8B,SAAS,EAAE,CAAC;;MAElD;MACA,MAAMC,GAAG,GAAG,wDAAwD1L,KAAK,GAAG;MAE5E0J,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE+B,GAAG,CAAC;MAC5ChC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE3J,KAAK,CAAC;MAE5B,MAAM2L,QAAQ,GAAG,MAAM3M,KAAK,CAAC4M,IAAI,CAACF,GAAG,EAAEd,QAAQ,EAAE;QAC/CiB,OAAO,EAAE;UACP;QAAA,CACD;QACDC,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MAEFpC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgC,QAAQ,CAACI,IAAI,CAAC;MAC3CrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgC,QAAQ,CAACK,MAAM,CAAC;MAEhD,IAAIL,QAAQ,CAACK,MAAM,KAAK,GAAG,IAAIL,QAAQ,CAACK,MAAM,KAAK,GAAG,EAAE;QACtDvM,iBAAiB,CAAC,8BAA8B,CAAC;QACjDE,eAAe,CAAC,IAAI,CAAC;;QAErB;QACAsM,gBAAgB,CAACnB,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACL,MAAM,IAAIoB,KAAK,CAAC,+BAA+BP,QAAQ,CAACK,MAAM,EAAE,CAAC;MACnE;IAEF,CAAC,CAAC,OAAOvD,KAAU,EAAE;MACnBiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,IAAIA,KAAK,CAACkD,QAAQ,EAAE;QAAA,IAAAQ,oBAAA;QAClBzC,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACkD,QAAQ,CAACI,IAAI,CAAC;QAC1DrC,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACkD,QAAQ,CAACK,MAAM,CAAC;QAC9DtC,OAAO,CAACjB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAACkD,QAAQ,CAACE,OAAO,CAAC;QAChElM,eAAe,CAAC,iBAAiB8I,KAAK,CAACkD,QAAQ,CAACK,MAAM,MAAM,EAAAG,oBAAA,GAAA1D,KAAK,CAACkD,QAAQ,CAACI,IAAI,cAAAI,oBAAA,uBAAnBA,oBAAA,CAAqBC,OAAO,KAAI,eAAe,EAAE,CAAC;MAChH,CAAC,MAAM,IAAI3D,KAAK,CAAC4D,OAAO,EAAE;QACxB3C,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC4D,OAAO,CAAC;QACrD1M,eAAe,CAAC,+EAA+E,CAAC;MAClG,CAAC,MAAM;QACL+J,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC2D,OAAO,CAAC;QACpDzM,eAAe,CAAC,kBAAkB8I,KAAK,CAAC2D,OAAO,EAAE,CAAC;MACpD;MAEA3M,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMwM,gBAAgB,GAAIF,IAAS,IAAK;IACtC,MAAMO,UAAU,GAAGhB,IAAI,CAACC,SAAS,CAACQ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAChD,MAAMQ,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAmB,CAAC,CAAC;IACjE,MAAMf,GAAG,GAAGgB,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGrB,GAAG;IACfkB,IAAI,CAACI,QAAQ,GAAG,mBAAmB,IAAI9B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAChFJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;IACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAC/BF,GAAG,CAACY,eAAe,CAAC5B,GAAG,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAM7B,GAAG,GAAG,wDAAwD1L,KAAK,GAAG;MAC5E0J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE+B,GAAG,CAAC;;MAE9C;MACA,MAAM8B,YAAY,GAAG,IAAI3C,QAAQ,CAAC,CAAC;MACnC2C,YAAY,CAACnC,MAAM,CAAC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAAC;QAC9CkC,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,IAAIxC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCiB,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;MAEH,MAAMT,QAAQ,GAAG,MAAM3M,KAAK,CAAC4M,IAAI,CAACF,GAAG,EAAE8B,YAAY,EAAE;QACnD1B,OAAO,EAAE;MACX,CAAC,CAAC;MAEFpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgC,QAAQ,CAAC;MAC3C5C,KAAK,CAAC,uCAAuC4C,QAAQ,CAACK,MAAM,eAAeV,IAAI,CAACC,SAAS,CAACI,QAAQ,CAACI,IAAI,CAAC,EAAE,CAAC;IAE7G,CAAC,CAAC,OAAOtD,KAAU,EAAE;MACnBiB,OAAO,CAACjB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MAEvC,IAAIA,KAAK,CAACkD,QAAQ,EAAE;QAClB5C,KAAK,CAAC,6BAA6BN,KAAK,CAACkD,QAAQ,CAACK,MAAM,YAAYV,IAAI,CAACC,SAAS,CAAC9C,KAAK,CAACkD,QAAQ,CAACI,IAAI,CAAC,EAAE,CAAC;MAC5G,CAAC,MAAM,IAAItD,KAAK,CAAC4D,OAAO,EAAE;QACxBtD,KAAK,CAAC,4FAA4F,CAAC;MACrG,CAAC,MAAM;QACLA,KAAK,CAAC,4BAA4BN,KAAK,CAAC2D,OAAO,EAAE,CAAC;MACpD;IACF;EACF,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3BzN,kBAAkB,CAAC;MACjBC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE,SAAS;MACrBC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,YAAY;MACzBC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,UAAU;MACtBC,GAAG,EAAE,IAAI;MACTC,YAAY,EAAE,YAAY;MAC1BC,aAAa,EAAE,SAAS;MACxBC,WAAW,EAAE,UAAU;MACvBC,QAAQ,EAAE,WAAW;MACrBC,WAAW,EAAE,UAAU;MACvBC,aAAa,EAAE,aAAa;MAC5BC,kBAAkB,EAAE,aAAa;MACjCC,eAAe,EAAE,UAAU;MAC3BC,KAAK,EAAE,sBAAsB;MAC7BC,cAAc,EAAE,mDAAmD;MACnEC,gBAAgB,EAAE,oCAAoC;MACtDC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,OAAO;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC;IAEFE,iBAAiB,CAAC,CAChB;MAAEC,QAAQ,EAAE,SAAS;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC7D;MAAEH,QAAQ,EAAE,SAAS;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,CAChE,CAAC;IAEFE,kBAAkB,CAAC;MACjBC,UAAU,EAAE,WAAW;MACvBC,iBAAiB,EAAE,YAAY;MAC/BC,aAAa,EAAE,YAAY;MAC3BC,cAAc,EAAE,KAAK;MACrBC,gBAAgB,EAAE,kBAAkB;MACpCC,SAAS,EAAE,YAAY;MACvBC,YAAY,EAAE,gBAAgB;MAC9BC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;IACd,CAAC,CAAC;IAEFsG,KAAK,CAAC,6DAA6D,CAAC;EACtE,CAAC;;EAED;EACA,MAAM6E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG;QAClB5N,eAAe,EAAE;UACf,GAAGA,eAAe;UAClBsB,aAAa,EAAEtB,eAAe,CAACsB,aAAa,GAC1C,UAAUtB,eAAe,CAACsB,aAAa,CAACqB,IAAI,KAAK3C,eAAe,CAACsB,aAAa,CAACuH,IAAI,UAAU,GAAG;QACpG,CAAC;QACDtH,cAAc;QACdM,eAAe,EAAE;UACf,GAAGA,eAAe;UAClBS,YAAY,EAAET,eAAe,CAACS,YAAY,GACxC,UAAUT,eAAe,CAACS,YAAY,CAACK,IAAI,KAAKd,eAAe,CAACS,YAAY,CAACuG,IAAI,UAAU,GAAG,IAAI;UACpGtG,OAAO,EAAEV,eAAe,CAACU,OAAO,GAC9B,UAAUV,eAAe,CAACU,OAAO,CAACI,IAAI,KAAKd,eAAe,CAACU,OAAO,CAACsG,IAAI,UAAU,GAAG,IAAI;UAC1FrG,UAAU,EAAEX,eAAe,CAACW,UAAU,GACpC,UAAUX,eAAe,CAACW,UAAU,CAACG,IAAI,KAAKd,eAAe,CAACW,UAAU,CAACqG,IAAI,UAAU,GAAG;QAC9F,CAAC;QACDpG,aAAa;QACbO,eAAe;QACfS,kBAAkB;QAClBM,UAAU;QACVW,cAAc;QACdiB,SAAS,EAAEA,SAAS,CAACmF,GAAG,CAAEC,GAAG,KAAM;UACjC,GAAGA,GAAG;UACN1E,eAAe,EAAE0E,GAAG,CAAC1E,eAAe,GAClC,UAAU0E,GAAG,CAAC1E,eAAe,CAAC1D,IAAI,KAAKoI,GAAG,CAAC1E,eAAe,CAACwC,IAAI,UAAU,GAAG;QAChF,CAAC,CAAC,CAAC;QACHpC,eAAe;QACfM,WAAW;QACXiE,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7CC,WAAW,EAAE,KAAK;QAClB0C,cAAc,EAAE;UACdC,UAAU,EAAE,CACV9N,eAAe,CAACsB,aAAa,EAC7BO,eAAe,CAACS,YAAY,EAC5BT,eAAe,CAACU,OAAO,EACvBV,eAAe,CAACW,UAAU,EAC1B,GAAGmD,SAAS,CAACmF,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC1E,eAAe,CAAC,CAC7C,CAAC0H,MAAM,CAACxD,OAAO,CAAC,CAACyD,MAAM;UACxBC,YAAY,EAAE;QAChB;MACF,CAAC;;MAED;MACA,MAAM5B,UAAU,GAAGhB,IAAI,CAACC,SAAS,CAACsC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;MACvDnE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2C,UAAU,CAAC;MAC7CvM,kBAAkB,CAACuM,UAAU,CAAC;MAC9BzM,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACAoM,gBAAgB,CAAC4B,WAAW,CAAC;IAC/B,CAAC,CAAC,OAAOpF,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCM,KAAK,CAAC,0BAA0B,CAAC;IACnC;EACF,CAAC;EAED,MAAMoF,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ5H,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEpH,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGzBtP,OAAA;YAAKiP,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClP,OAAA;cAAKiP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClP,OAAA;gBAAKiP,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBpO,eAAe,CAACsB,aAAa,gBAC5BpC,OAAA;kBACEuP,GAAG,EAAEhC,GAAG,CAACC,eAAe,CAAC1M,eAAe,CAACsB,aAAa,CAAE;kBACxDoN,GAAG,EAAC,gBAAgB;kBACpBP,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,gBAEFtP,OAAA;kBAAKiP,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BlP,OAAA;oBAAKyP,KAAK,EAAC,IAAI;oBAACzL,MAAM,EAAC,IAAI;oBAAC0L,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAAX,QAAA,gBAC/FlP,OAAA;sBAAM8P,CAAC,EAAC;oBAA2C;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3DtP,OAAA;sBAAQ+P,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAG;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtP,OAAA;gBAAMiP,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BlP,OAAA;gBAAKiP,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BlP,OAAA;kBAAGiP,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpEtP,OAAA;kBAAOiP,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,eAE/B,eAAAlP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX4C,MAAM,EAAC,SAAS;oBAChBC,QAAQ;oBACRC,QAAQ,EAAGC,CAAC;sBAAA,IAAAC,eAAA;sBAAA,OAAK/G,gBAAgB,CAAC,EAAA+G,eAAA,GAAAD,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,eAAe,CAAC;oBAAA,CAAC;oBAC5FrB,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EACPxO,eAAe,CAACsB,aAAa,iBAC5BpC,OAAA;kBAAGiP,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEpO,eAAe,CAACsB,aAAa,CAACqB;gBAAI;kBAAA0L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACjE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtP,OAAA;YAAKiP,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX6C,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACE,SAAU;gBACjCoP,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,WAAW,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACG,UAAW;gBAClCmP,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,YAAY,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX6C,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACI,QAAS;gBAChCkP,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,UAAU,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX6C,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACK,WAAY;gBACnCiP,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,aAAa,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBtP,OAAA;gBACEmQ,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACM,MAAO;gBAC9BgP,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,QAAQ,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK,CAAE;gBAAA2G,QAAA,gBAEvElP,OAAA;kBAAQuI,KAAK,EAAC,EAAE;kBAAA2G,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtP,OAAA;kBAAQuI,KAAK,EAAC,MAAM;kBAAA2G,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCtP,OAAA;kBAAQuI,KAAK,EAAC,QAAQ;kBAAA2G,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtP,OAAA;kBAAQuI,KAAK,EAAC,OAAO;kBAAA2G,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACO,UAAW;gBAClC+O,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,YAAY,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClBtP,OAAA;gBACEsN,IAAI,EAAC,QAAQ;gBACb/E,KAAK,EAAEzH,eAAe,CAACQ,GAAI;gBAC3B8O,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,KAAK,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACS,YAAa;gBACpC6O,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,cAAc,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7BtP,OAAA;gBACEuI,KAAK,EAAEzH,eAAe,CAACU,aAAc;gBACrC4O,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,eAAe,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK,CAAE;gBAAA2G,QAAA,gBAE9ElP,OAAA;kBAAQuI,KAAK,EAAC,EAAE;kBAAA2G,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtP,OAAA;kBAAQuI,KAAK,EAAC,QAAQ;kBAAA2G,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtP,OAAA;kBAAQuI,KAAK,EAAC,SAAS;kBAAA2G,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCtP,OAAA;kBAAQuI,KAAK,EAAC,UAAU;kBAAA2G,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CtP,OAAA;kBAAQuI,KAAK,EAAC,SAAS;kBAAA2G,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX6C,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACW,WAAY;gBACnC2O,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,aAAa,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACY,QAAS;gBAChC0O,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,UAAU,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACa,WAAY;gBACnCyO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,aAAa,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BtP,OAAA;gBACEsN,IAAI,EAAC,KAAK;gBACV6C,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACc,aAAc;gBACrCwO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,eAAe,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCtP,OAAA;gBACEsN,IAAI,EAAC,KAAK;gBACV6C,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACe,kBAAmB;gBAC1CuO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,oBAAoB,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACgB,eAAgB;gBACvCsO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,iBAAiB,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBtP,OAAA;gBACEsN,IAAI,EAAC,OAAO;gBACZ6C,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACiB,KAAM;gBAC7BqO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,OAAO,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClP,OAAA;gBAAAkP,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCtP,OAAA;gBACEmQ,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACkB,cAAe;gBACtCoO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,gBAAgB,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClP,OAAA;gBAAAkP,QAAA,EAAO;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCtP,OAAA;gBACEmQ,QAAQ;gBACR5H,KAAK,EAAEzH,eAAe,CAACmB,gBAAiB;gBACxCmO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,kBAAkB,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACoB,UAAW;gBAClCkO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,YAAY,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEzH,eAAe,CAACqB,YAAa;gBACpCiO,QAAQ,EAAGC,CAAC,IAAKhI,2BAA2B,CAAC,cAAc,EAAEgI,CAAC,CAACE,MAAM,CAAChI,KAAK;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtP,OAAA;YAAKiP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlP,OAAA;cAAAkP,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBtP,OAAA;cAAKiP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE5F,eAAe,CAACE,UAAW;kBAClCuN,QAAQ,EAAGC,CAAC,IAAK5H,2BAA2B,CAAC,YAAY,EAAE4H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE5F,eAAe,CAACG,iBAAkB;kBACzCsN,QAAQ,EAAGC,CAAC,IAAK5H,2BAA2B,CAAC,mBAAmB,EAAE4H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE5F,eAAe,CAACI,aAAc;kBACrCqN,QAAQ,EAAGC,CAAC,IAAK5H,2BAA2B,CAAC,eAAe,EAAE4H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE5F,eAAe,CAACK,cAAe;kBACtCoN,QAAQ,EAAGC,CAAC,IAAK5H,2BAA2B,CAAC,gBAAgB,EAAE4H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE5F,eAAe,CAACM,gBAAiB;kBACxCmN,QAAQ,EAAGC,CAAC,IAAK5H,2BAA2B,CAAC,kBAAkB,EAAE4H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX6C,QAAQ;kBACR5H,KAAK,EAAE5F,eAAe,CAACO,SAAU;kBACjCkN,QAAQ,EAAGC,CAAC,IAAK5H,2BAA2B,CAAC,WAAW,EAAE4H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX4C,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAI,gBAAA;oBAAA,OAAKlH,gBAAgB,CAAC,EAAAkH,gBAAA,GAAAJ,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAC,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC;kBAAA;gBAAC;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC,EACD3M,eAAe,CAACU,OAAO,iBACtBrD,OAAA;kBAAKiP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BlP,OAAA;oBAAMiP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEvM,eAAe,CAACU,OAAO,CAACI;kBAAI;oBAAA0L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX6C,QAAQ;kBACR5H,KAAK,EAAE5F,eAAe,CAACQ,YAAa;kBACpCiN,QAAQ,EAAGC,CAAC,IAAK5H,2BAA2B,CAAC,cAAc,EAAE4H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX4C,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAK,gBAAA;oBAAA,OAAKnH,gBAAgB,CAAC,EAAAmH,gBAAA,GAAAL,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAE,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC;kBAAA;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,EACD3M,eAAe,CAACW,UAAU,iBACzBtD,OAAA;kBAAKiP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BlP,OAAA;oBAAMiP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEvM,eAAe,CAACW,UAAU,CAACG;kBAAI;oBAAA0L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX4C,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAM,gBAAA;oBAAA,OAAKpH,gBAAgB,CAAC,EAAAoH,gBAAA,GAAAN,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAG,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC;kBAAA;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,EACD3M,eAAe,CAACS,YAAY,iBAC3BpD,OAAA;kBAAKiP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BlP,OAAA;oBAAMiP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEvM,eAAe,CAACS,YAAY,CAACK;kBAAI;oBAAA0L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtP,OAAA;YAAKiP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlP,OAAA;cAAAkP,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtBjN,cAAc,CAACuJ,GAAG,CAAC,CAACgF,KAAK,EAAEnH,KAAK,kBAC/BzJ,OAAA;cAAiBiP,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBAC7ClP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEqI,KAAK,CAACrO,QAAS;kBACtB6N,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGxO,cAAc,CAAC;oBACrCwO,SAAS,CAACpH,KAAK,CAAC,CAAClH,QAAQ,GAAG8N,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC1CjG,iBAAiB,CAACuO,SAAS,CAAC;kBAC9B;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BlP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,UAAU;oBACfwD,OAAO,EAAEF,KAAK,CAACpO,KAAM;oBACrB4N,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGxO,cAAc,CAAC;sBACrCwO,SAAS,CAACpH,KAAK,CAAC,CAACjH,KAAK,GAAG6N,CAAC,CAACE,MAAM,CAACO,OAAO;sBACzCxO,iBAAiB,CAACuO,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,UAAU;oBACfwD,OAAO,EAAEF,KAAK,CAACnO,IAAK;oBACpB2N,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGxO,cAAc,CAAC;sBACrCwO,SAAS,CAACpH,KAAK,CAAC,CAAChH,IAAI,GAAG4N,CAAC,CAACE,MAAM,CAACO,OAAO;sBACxCxO,iBAAiB,CAACuO,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,QAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,UAAU;oBACfwD,OAAO,EAAEF,KAAK,CAAClO,KAAM;oBACrB0N,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMQ,SAAS,GAAG,CAAC,GAAGxO,cAAc,CAAC;sBACrCwO,SAAS,CAACpH,KAAK,CAAC,CAAC/G,KAAK,GAAG2N,CAAC,CAACE,MAAM,CAACO,OAAO;sBACzCxO,iBAAiB,CAACuO,SAAS,CAAC;oBAC9B;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,SAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAlDE7F,KAAK;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDV,CACN,CAAC,eACFtP,OAAA;cAAQsN,IAAI,EAAC,QAAQ;cAACyD,OAAO,EAAEhH,gBAAiB;cAACkF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,oBACEtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBtP,OAAA;YAAKiP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlP,OAAA;cAAAkP,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtB/L,aAAa,CAACqI,GAAG,CAAC,CAACoF,MAAM,EAAEvH,KAAK,kBAC/BzJ,OAAA;cAAiBiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAChDlP,OAAA;gBAAKiP,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEyI,MAAM,CAACvN,IAAK;oBACnB2M,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAG1N,aAAa,CAAC;sBACrC0N,UAAU,CAACxH,KAAK,CAAC,CAAChG,IAAI,GAAG4M,CAAC,CAACE,MAAM,CAAChI,KAAK;sBACvC/E,gBAAgB,CAACyN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEyI,MAAM,CAACtN,YAAa;oBAC3B0M,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAG1N,aAAa,CAAC;sBACrC0N,UAAU,CAACxH,KAAK,CAAC,CAAC/F,YAAY,GAAG2M,CAAC,CAACE,MAAM,CAAChI,KAAK;sBAC/C/E,gBAAgB,CAACyN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEyI,MAAM,CAAC7P,WAAY;oBAC1BiP,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAG1N,aAAa,CAAC;sBACrC0N,UAAU,CAACxH,KAAK,CAAC,CAACtI,WAAW,GAAGkP,CAAC,CAACE,MAAM,CAAChI,KAAK;sBAC9C/E,gBAAgB,CAACyN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEyI,MAAM,CAACrN,aAAc;oBAC5ByM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAG1N,aAAa,CAAC;sBACrC0N,UAAU,CAACxH,KAAK,CAAC,CAAC9F,aAAa,GAAG0M,CAAC,CAACE,MAAM,CAAChI,KAAK;sBAChD/E,gBAAgB,CAACyN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzBtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEyI,MAAM,CAACpN,UAAW;oBACzBwM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAG1N,aAAa,CAAC;sBACrC0N,UAAU,CAACxH,KAAK,CAAC,CAAC7F,UAAU,GAAGyM,CAAC,CAACE,MAAM,CAAChI,KAAK;sBAC7C/E,gBAAgB,CAACyN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxCtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEyI,MAAM,CAACnN,uBAAwB;oBACtCuM,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMY,UAAU,GAAG,CAAC,GAAG1N,aAAa,CAAC;sBACrC0N,UAAU,CAACxH,KAAK,CAAC,CAAC5F,uBAAuB,GAAGwM,CAAC,CAACE,MAAM,CAAChI,KAAK;sBAC1D/E,gBAAgB,CAACyN,UAAU,CAAC;oBAC9B;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1EE7F,KAAK;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2EV,CACN,CAAC,eACFtP,OAAA;cAAQsN,IAAI,EAAC,QAAQ;cAACyD,OAAO,EAAE/G,eAAgB;cAACiF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBtP,OAAA;YAAKiP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlP,OAAA;cAAAkP,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBtP,OAAA;cAAKiP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEzE,eAAe,CAACE,MAAO;kBAC9BoM,QAAQ,EAAGC,CAAC,IAAK3H,2BAA2B,CAAC,QAAQ,EAAE2H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEzE,eAAe,CAACG,MAAO;kBAC9BmM,QAAQ,EAAGC,CAAC,IAAK3H,2BAA2B,CAAC,QAAQ,EAAE2H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BtP,OAAA;kBACEuI,KAAK,EAAEzE,eAAe,CAACI,UAAW;kBAClCkM,QAAQ,EAAGC,CAAC,IAAK3H,2BAA2B,CAAC,YAAY,EAAE2H,CAAC,CAACE,MAAM,CAAChI,KAAK,CAAE;kBAAA2G,QAAA,gBAE3ElP,OAAA;oBAAQuI,KAAK,EAAC,EAAE;oBAAA2G,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA2G,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BtP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA2G,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BtP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA2G,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BtP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA2G,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BtP,OAAA;oBAAQuI,KAAK,EAAC,KAAK;oBAAA2G,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCtP,OAAA;oBAAQuI,KAAK,EAAC,KAAK;oBAAA2G,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCtP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA2G,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BtP,OAAA;oBAAQuI,KAAK,EAAC,IAAI;oBAAA2G,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEzE,eAAe,CAACK,aAAc;kBACrCiM,QAAQ,EAAGC,CAAC,IAAK3H,2BAA2B,CAAC,eAAe,EAAE2H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEzE,eAAe,CAACM,YAAa;kBACpCgM,QAAQ,EAAGC,CAAC,IAAK3H,2BAA2B,CAAC,cAAc,EAAE2H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEzE,eAAe,CAACO,kBAAmB;kBAC1C+L,QAAQ,EAAGC,CAAC,IAAK3H,2BAA2B,CAAC,oBAAoB,EAAE2H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEzE,eAAe,CAACQ,kBAAmB;kBAC1C8L,QAAQ,EAAGC,CAAC,IAAK3H,2BAA2B,CAAC,oBAAoB,EAAE2H,CAAC,CAACE,MAAM,CAAChI,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzB7I,SAAS,CAACmF,GAAG,CAAC,CAACC,GAAG,EAAEpC,KAAK,kBACxBzJ,OAAA;YAAiBiP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC5ClP,OAAA;cAAAkP,QAAA,GAAI,YAAU,EAACzF,KAAK,GAAG,CAAC;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9BtP,OAAA;cAAKiP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEsD,GAAG,CAAClF,WAAY;kBACvByJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC9C,WAAW,GAAG0J,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAChD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEsD,GAAG,CAACjF,cAAe;kBAC1BwJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC7C,cAAc,GAAGyJ,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACnD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEsD,GAAG,CAAChF,aAAc;kBACzBuJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC5C,aAAa,GAAGwJ,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAClD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEsD,GAAG,CAAC/E,UAAW;kBACtBsJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC3C,UAAU,GAAGuJ,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC/C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDtP,OAAA;kBACEuI,KAAK,EAAEsD,GAAG,CAAC9E,gBAAiB;kBAC5BqJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC1C,gBAAgB,GAAGsJ,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACrD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B,CAAE;kBAAAoF,QAAA,gBAEFlP,OAAA;oBAAQuI,KAAK,EAAC,EAAE;oBAAA2G,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCtP,OAAA;oBAAQuI,KAAK,EAAC,WAAW;oBAAA2G,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtP,OAAA;oBAAQuI,KAAK,EAAC,WAAW;oBAAA2G,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtP,OAAA;oBAAQuI,KAAK,EAAC,gBAAgB;oBAAA2G,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEsD,GAAG,CAACjG,QAAS;kBACpBwK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAAC7D,QAAQ,GAAGyK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC7C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEsD,GAAG,CAAC7E,gBAAiB;kBAC5BoJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACzC,gBAAgB,GAAGqJ,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACrD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAEsD,GAAG,CAAC5E,UAAW;kBACtBmJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACxC,UAAU,GAAGoJ,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC/C7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClP,OAAA;kBAAAkP,QAAA,EAAO;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDtP,OAAA;kBACEuI,KAAK,EAAEsD,GAAG,CAAC3E,YAAa;kBACxBkJ,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMvG,YAAY,GAAG,CAAC,GAAGrD,SAAS,CAAC;oBACnCqD,YAAY,CAACL,KAAK,CAAC,CAACvC,YAAY,GAAGmJ,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACjD7B,YAAY,CAACoD,YAAY,CAAC;kBAC5B;gBAAE;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX4C,MAAM,EAAC,cAAc;kBACrBE,QAAQ,EAAGC,CAAC;oBAAA,IAAAa,gBAAA;oBAAA,OAAK3H,gBAAgB,CAAC,EAAA2H,gBAAA,GAAAb,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAU,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAEzH,KAAK,CAAC;kBAAA;gBAAC;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,EACDzD,GAAG,CAAC1E,eAAe,iBAClBnH,OAAA;kBAAKiP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BlP,OAAA;oBAAMiP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAErD,GAAG,CAAC1E,eAAe,CAAC1D;kBAAI;oBAAA0L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/HE7F,KAAK;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgIV,CACN,CAAC,eACFtP,OAAA;YAAQsN,IAAI,EAAC,QAAQ;YAACyD,OAAO,EAAE7G,YAAa;YAAC+E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEzB9J,cAAc,CAACoG,GAAG,CAAC,CAACuF,UAAU,EAAE1H,KAAK,kBACpCzJ,OAAA;YAAiBiP,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAClDlP,OAAA;cAAAkP,QAAA,GAAI,kBAAgB,EAACzF,KAAK,GAAG,CAAC;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCtP,OAAA;cAAKiP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE4I,UAAU,CAACzL,YAAa;kBAC/B0K,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAAC/D,YAAY,GAAG2K,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAClD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClP,OAAA;kBAAAkP,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBtP,OAAA;kBACEuI,KAAK,EAAE4I,UAAU,CAACxL,OAAQ;kBAC1ByK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAAC9D,OAAO,GAAG0K,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC7C9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE4I,UAAU,CAACtL,QAAS;kBAC3BuK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAAC5D,QAAQ,GAAGwK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC9C9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE4I,UAAU,CAACrL,MAAO;kBACzBsK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAAC3D,MAAM,GAAGuK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC5C9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE4I,UAAU,CAACpL,YAAa;kBAC/BqK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAAC1D,YAAY,GAAGsK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAClD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE4I,UAAU,CAACnL,WAAY;kBAC9BoK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACzD,WAAW,GAAGqK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACjD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClP,OAAA;kBAAAkP,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BtP,OAAA;kBACEuI,KAAK,EAAE4I,UAAU,CAAClL,cAAe;kBACjCmK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACxD,cAAc,GAAGoK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACpD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE4I,UAAU,CAACjL,iBAAkB;kBACpCkK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACvD,iBAAiB,GAAGmK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACvD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DtP,OAAA;kBACEsN,IAAI,EAAC,QAAQ;kBACb/E,KAAK,EAAE4I,UAAU,CAAChL,eAAgB;kBAClCiK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACtD,eAAe,GAAGkK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACrD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBtP,OAAA;kBACEsN,IAAI,EAAC,QAAQ;kBACb/E,KAAK,EAAE4I,UAAU,CAAC/K,SAAU;kBAC5BgK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACrD,SAAS,GAAGiK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBAC/C9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BtP,OAAA;kBACEsN,IAAI,EAAC,QAAQ;kBACb/E,KAAK,EAAE4I,UAAU,CAAC9K,WAAY;kBAC9B+J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACpD,WAAW,GAAGgK,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACjD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BtP,OAAA;kBACEsN,IAAI,EAAC,QAAQ;kBACb/E,KAAK,EAAE4I,UAAU,CAAC7K,WAAY;kBAC9B8J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACnD,WAAW,GAAG+J,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACjD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BtP,OAAA;kBACEsN,IAAI,EAAC,QAAQ;kBACb/E,KAAK,EAAE4I,UAAU,CAAC5K,cAAe;kBACjC6J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAAClD,cAAc,GAAG8J,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACpD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BtP,OAAA;kBACEsN,IAAI,EAAC,QAAQ;kBACb/E,KAAK,EAAE4I,UAAU,CAAC3K,WAAY;kBAC9B4J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMe,aAAa,GAAG,CAAC,GAAG5L,cAAc,CAAC;oBACzC4L,aAAa,CAAC3H,KAAK,CAAC,CAACjD,WAAW,GAAG6J,CAAC,CAACE,MAAM,CAAChI,KAAK;oBACjD9C,iBAAiB,CAAC2L,aAAa,CAAC;kBAClC;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAzKE7F,KAAK;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0KV,CACN,CAAC,eACFtP,OAAA;YAAQsN,IAAI,EAAC,QAAQ;YAACyD,OAAO,EAAE9G,iBAAkB;YAACgF,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAErBtP,OAAA;YAAKiP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlP,OAAA;cAAAkP,QAAA,EAAI;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCtP,OAAA;cAAKiP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAmE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClFtP,OAAA;kBAAKiP,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BlP,OAAA;oBAAAkP,QAAA,gBACElP,OAAA;sBACEsN,IAAI,EAAC,OAAO;sBACZ7J,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,MAAM;sBACZuI,OAAO,EAAEvM,kBAAkB,CAACE,kBAAkB,KAAK,IAAK;sBACxD2L,QAAQ,EAAEA,CAAA,KAAM5L,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAA0K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtP,OAAA;oBAAAkP,QAAA,gBACElP,OAAA;sBACEsN,IAAI,EAAC,OAAO;sBACZ7J,IAAI,EAAC,oBAAoB;sBACzB8E,KAAK,EAAC,OAAO;sBACbuI,OAAO,EAAEvM,kBAAkB,CAACE,kBAAkB,KAAK,KAAM;sBACzD2L,QAAQ,EAAEA,CAAA,KAAM5L,qBAAqB,CAACgE,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE/D,kBAAkB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAA0K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL/K,kBAAkB,CAACE,kBAAkB,iBACpCzE,OAAA,CAAAE,SAAA;gBAAAgP,QAAA,gBACElP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxBtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEhE,kBAAkB,CAACG,aAAc;oBACxC0L,QAAQ,EAAGC,CAAC,IAAK7L,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE9D,aAAa,EAAE2L,CAAC,CAACE,MAAM,CAAChI;oBAAM,CAAC,CAAC;kBAAE;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvBtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEhE,kBAAkB,CAACI,iBAAkB;oBAC5CyL,QAAQ,EAAGC,CAAC,IAAK7L,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE7D,iBAAiB,EAAE0L,CAAC,CAACE,MAAM,CAAChI;oBAAM,CAAC,CAAC;kBAAE;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtP,OAAA;kBAAKiP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlP,OAAA;oBAAAkP,QAAA,EAAO;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtBtP,OAAA;oBACEsN,IAAI,EAAC,MAAM;oBACX/E,KAAK,EAAEhE,kBAAkB,CAACK,gBAAiB;oBAC3CwL,QAAQ,EAAGC,CAAC,IAAK7L,qBAAqB,CAACgE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE5D,gBAAgB,EAAEyL,CAAC,CAACE,MAAM,CAAChI;oBAAM,CAAC,CAAC;kBAAE;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtP,OAAA;YAAKiP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlP,OAAA;cAAAkP,QAAA,EAAI;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CtP,OAAA;cAAKiP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACtB,IAAK;kBAC1C2M,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEtB,IAAI,EAAE4M,CAAC,CAACE,MAAM,CAAChI;oBAAM;kBACzE,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACrB,YAAa;kBAClD0M,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAErB,YAAY,EAAE2M,CAAC,CAACE,MAAM,CAAChI;oBAAM;kBACjF,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACC,QAAS;kBAC9CoL,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEC,QAAQ,EAAEqL,CAAC,CAACE,MAAM,CAAChI;oBAAM;kBAC7E,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE1D,UAAU,CAACE,kBAAkB,CAACE,eAAgB;kBACrDmL,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBACtC,GAAGA,IAAI;oBACPzD,kBAAkB,EAAE;sBAAE,GAAGyD,IAAI,CAACzD,kBAAkB;sBAAEE,eAAe,EAAEoL,CAAC,CAACE,MAAM,CAAChI;oBAAM;kBACpF,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClP,OAAA;kBAAAkP,QAAA,EAAO;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDtP,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACK,aAAc;kBAChCkL,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtD,aAAa,EAAEmL,CAAC,CAACE,MAAM,CAAChI;kBAAM,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDtP,OAAA;kBAAKiP,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BlP,OAAA;oBAAAkP,QAAA,gBACElP,OAAA;sBACEsN,IAAI,EAAC,OAAO;sBACZ7J,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,MAAM;sBACZuI,OAAO,EAAEjM,UAAU,CAACM,gBAAgB,KAAK,IAAK;sBAC9CiL,QAAQ,EAAEA,CAAA,KAAMtL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtP,OAAA;oBAAAkP,QAAA,gBACElP,OAAA;sBACEsN,IAAI,EAAC,OAAO;sBACZ7J,IAAI,EAAC,kBAAkB;sBACvB8E,KAAK,EAAC,OAAO;sBACbuI,OAAO,EAAEjM,UAAU,CAACM,gBAAgB,KAAK,KAAM;sBAC/CiL,QAAQ,EAAEA,CAAA,KAAMtL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAErD,gBAAgB,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLzK,UAAU,CAACM,gBAAgB,iBAC1BnF,OAAA;gBAAKiP,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClP,OAAA;kBAAAkP,QAAA,EAAO;gBAAmC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDtP,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACO,cAAe;kBACjCgL,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEpD,cAAc,EAAEiL,CAAC,CAACE,MAAM,CAAChI;kBAAM,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEtP,OAAA;kBAAKiP,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BlP,OAAA;oBAAAkP,QAAA,gBACElP,OAAA;sBACEsN,IAAI,EAAC,OAAO;sBACZ7J,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,MAAM;sBACZuI,OAAO,EAAEjM,UAAU,CAACQ,4BAA4B,KAAK,IAAK;sBAC1D+K,QAAQ,EAAEA,CAAA,KAAMtL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAK,CAAC,CAAC;oBAAE;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,OAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtP,OAAA;oBAAAkP,QAAA,gBACElP,OAAA;sBACEsN,IAAI,EAAC,OAAO;sBACZ7J,IAAI,EAAC,8BAA8B;sBACnC8E,KAAK,EAAC,OAAO;sBACbuI,OAAO,EAAEjM,UAAU,CAACQ,4BAA4B,KAAK,KAAM;sBAC3D+K,QAAQ,EAAEA,CAAA,KAAMtL,aAAa,CAAC0D,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEnD,4BAA4B,EAAE;sBAAM,CAAC,CAAC;oBAAE;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,MAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLzK,UAAU,CAACQ,4BAA4B,iBACtCrF,OAAA;gBAAKiP,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClP,OAAA;kBAAAkP,QAAA,EAAO;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnCtP,OAAA;kBACEuI,KAAK,EAAE1D,UAAU,CAACS,eAAgB;kBAClC8K,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAElD,eAAe,EAAE+K,CAAC,CAACE,MAAM,CAAChI;kBAAM,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDtP,OAAA;gBAAKiP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlP,OAAA;kBAAAkP,QAAA,EAAO;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CtP,OAAA;kBACEsN,IAAI,EAAC,MAAM;kBACX/E,KAAK,EAAE1D,UAAU,CAACU,WAAY;kBAC9B6K,QAAQ,EAAGC,CAAC,IAAKvL,aAAa,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjD,WAAW,EAAE8K,CAAC,CAACE,MAAM,CAAChI;kBAAM,CAAC,CAAC;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBtP,OAAA;YAAKiP,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAwD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvEtP,OAAA;gBAAKiP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,OAAO;oBACZ7J,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,MAAM;oBACZuI,OAAO,EAAEvJ,eAAe,CAACE,eAAe,KAAK,IAAK;oBAClD2I,QAAQ,EAAEA,CAAA,KAAM5I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,OAAO;oBACZ7J,IAAI,EAAC,iBAAiB;oBACtB8E,KAAK,EAAC,OAAO;oBACbuI,OAAO,EAAEvJ,eAAe,CAACE,eAAe,KAAK,KAAM;oBACnD2I,QAAQ,EAAEA,CAAA,KAAM5I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,eAAe,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL/H,eAAe,CAACE,eAAe,iBAC9BzH,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClP,OAAA;gBAAAkP,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCtP,OAAA;gBACEuI,KAAK,EAAEhB,eAAe,CAACG,OAAQ;gBAC/B0I,QAAQ,EAAGC,CAAC,IAAK7I,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEd,OAAO,EAAE2I,CAAC,CAACE,MAAM,CAAChI;gBAAM,CAAC,CAAC;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DtP,OAAA;gBAAKiP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,OAAO;oBACZ7J,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,MAAM;oBACZuI,OAAO,EAAEvJ,eAAe,CAACI,gBAAgB,KAAK,IAAK;oBACnDyI,QAAQ,EAAEA,CAAA,KAAM5I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,OAAO;oBACZ7J,IAAI,EAAC,kBAAkB;oBACvB8E,KAAK,EAAC,OAAO;oBACbuI,OAAO,EAAEvJ,eAAe,CAACI,gBAAgB,KAAK,KAAM;oBACpDyI,QAAQ,EAAEA,CAAA,KAAM5I,kBAAkB,CAACgB,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,gBAAgB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL/H,eAAe,CAACI,gBAAgB,iBAC/B3H,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClP,OAAA;gBAAAkP,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCtP,OAAA;gBACEuI,KAAK,EAAEhB,eAAe,CAACK,iBAAkB;gBACzCwI,QAAQ,EAAGC,CAAC,IAAK7I,kBAAkB,CAACgB,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEZ,iBAAiB,EAAEyI,CAAC,CAACE,MAAM,CAAChI;gBAAM,CAAC,CAAC;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlP,OAAA;YAAAkP,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBtP,OAAA;YAAKiP,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlP,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpClP,OAAA;gBAAKiP,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BlP,OAAA;kBAAAkP,QAAA,eAAGlP,OAAA;oBAAAkP,QAAA,EAAQ;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtP,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpClP,OAAA;gBAAAkP,QAAA,gBACElP,OAAA;kBACEsN,IAAI,EAAC,UAAU;kBACfwD,OAAO,EAAEjJ,WAAW,CAACE,uBAAwB;kBAC7CqI,QAAQ,EAAGC,CAAC,IAAKvI,cAAc,CAACU,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAET,uBAAuB,EAAEsI,CAAC,CAACE,MAAM,CAACO;kBAAQ,CAAC,CAAC;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,mJAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEtP,OAAA;gBAAKiP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,OAAO;oBACZ7J,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,MAAM;oBACZuI,OAAO,EAAEjJ,WAAW,CAACG,mBAAmB,KAAK,IAAK;oBAClDoI,QAAQ,EAAEA,CAAA,KAAMtI,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAK,CAAC,CAAC;kBAAE;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,OAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtP,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBACEsN,IAAI,EAAC,OAAO;oBACZ7J,IAAI,EAAC,qBAAqB;oBAC1B8E,KAAK,EAAC,OAAO;oBACbuI,OAAO,EAAEjJ,WAAW,CAACG,mBAAmB,KAAK,KAAM;oBACnDoI,QAAQ,EAAEA,CAAA,KAAMtI,cAAc,CAACU,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAER,mBAAmB,EAAE;oBAAM,CAAC,CAAC;kBAAE;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,MAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELzH,WAAW,CAACG,mBAAmB,iBAC9BhI,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClP,OAAA;gBAAAkP,QAAA,EAAO;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCtP,OAAA;gBACEuI,KAAK,EAAEV,WAAW,CAACI,sBAAuB;gBAC1CmI,QAAQ,EAAGC,CAAC,IAAKvI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEP,sBAAsB,EAAEoI,CAAC,CAACE,MAAM,CAAChI;gBAAM,CAAC,CAAC;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpBtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEV,WAAW,CAACK,KAAM;gBACzBkI,QAAQ,EAAGC,CAAC,IAAKvI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEN,KAAK,EAAEmI,CAAC,CAACE,MAAM,CAAChI;gBAAM,CAAC,CAAC;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtP,OAAA;cAAKiP,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlP,OAAA;gBAAAkP,QAAA,EAAO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnBtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX/E,KAAK,EAAEV,WAAW,CAACM,IAAK;gBACxBiI,QAAQ,EAAGC,CAAC,IAAKvI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEL,IAAI,EAAEkI,CAAC,CAACE,MAAM,CAAChI;gBAAM,CAAC,CAAC;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtP,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClP,OAAA;gBAAAkP,QAAA,EAAO;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCtP,OAAA;gBACEsN,IAAI,EAAC,MAAM;gBACX+D,WAAW,EAAC,kCAAkC;gBAC9C9I,KAAK,EAAEV,WAAW,CAACO,kBAAmB;gBACtCgI,QAAQ,EAAGC,CAAC,IAAKvI,cAAc,CAACU,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEJ,kBAAkB,EAAEiI,CAAC,CAACE,MAAM,CAAChI;gBAAM,CAAC,CAAC;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtP,OAAA;cAAKiP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpClP,OAAA;gBAAKiP,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BlP,OAAA;kBAAAkP,QAAA,eAAGlP,OAAA;oBAAAkP,QAAA,EAAI;kBAA0I;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAOtP,OAAA;UAAAkP,QAAA,GAAK,OAAK,EAAC9H,WAAW;QAAA;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAGA,CAAA,KAAM;IACvBhR,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACER,OAAA;IAAKiP,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BlP,OAAA;MAAKiP,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BlP,OAAA;QAAAkP,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCtP,OAAA;QAAKiP,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BlP,OAAA;UACEiP,SAAS,EAAC,eAAe;UACzBsC,KAAK,EAAE;YAAE9B,KAAK,EAAE,GAAIrI,WAAW,GAAGE,UAAU,GAAI,GAAG;UAAI;QAAE;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtP,OAAA;QAAAkP,QAAA,GAAG,OAAK,EAAC9H,WAAW,EAAC,MAAI,EAACE,UAAU,EAAC,IAAE,EAAC6C,YAAY,CAAC/C,WAAW,CAAC;MAAA;QAAA+H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAIL,CAACjP,cAAc,IAAIE,YAAY,kBAC9BP,OAAA;MAAKiP,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BlP,OAAA;QAAKiP,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BlP,OAAA;UAAKiP,SAAS,EAAE5O,cAAc,GAAG,iBAAiB,GAAG,eAAgB;UAAA6O,QAAA,EAClE7O,cAAc,IAAIE;QAAY;UAAA4O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNtP,OAAA;UAAQiP,SAAS,EAAC,aAAa;UAAC8B,OAAO,EAAEO,UAAW;UAAApC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDtP,OAAA;MAAMwR,QAAQ,EAAElG,YAAa;MAAA4D,QAAA,EAC1BF,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEPtP,OAAA;MAAKiP,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BlP,OAAA;QAAKiP,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtB9H,WAAW,GAAG,CAAC,iBACdpH,OAAA;UAAQsN,IAAI,EAAC,QAAQ;UAACyD,OAAO,EAAEtG,QAAS;UAACwE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtP,OAAA;QAAKiP,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlP,OAAA;UAAQsN,IAAI,EAAC,QAAQ;UAACyD,OAAO,EAAE3C,iBAAkB;UAACa,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtP,OAAA;UAAQsN,IAAI,EAAC,QAAQ;UAACyD,OAAO,EAAEvC,cAAe;UAACS,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtP,OAAA;UAAQsN,IAAI,EAAC,QAAQ;UAACyD,OAAO,EAAEtC,eAAgB;UAACQ,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtP,OAAA;QAAKiP,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB9H,WAAW,GAAGE,UAAU,gBACvBtH,OAAA;UAAQsN,IAAI,EAAC,QAAQ;UAACyD,OAAO,EAAEzG,QAAS;UAAC2E,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETtP,OAAA;UAAQsN,IAAI,EAAC,QAAQ;UAACyD,OAAO,EAAEA,CAAA,KAAM;YACnC,MAAMU,IAAI,GAAG/D,QAAQ,CAACgE,aAAa,CAAC,MAAM,CAAC;YAC3C,IAAID,IAAI,EAAE;cACRA,IAAI,CAACE,aAAa,CAAC,CAAC;YACtB;UACF,CAAE;UAAC1C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7O,eAAe,iBACdT,OAAA;MAAKiP,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BlP,OAAA;QAAKiP,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvClP,OAAA;UAAAkP,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BtP,OAAA;UAAKiP,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BvO;QAAe;UAAAwO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNtP,OAAA;UAAKiP,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BlP,OAAA;YACE+Q,OAAO,EAAEA,CAAA,KAAMrQ,kBAAkB,CAAC,KAAK,CAAE;YACzCuO,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtP,OAAA;YACE+Q,OAAO,EAAEA,CAAA,KAAM;cACba,SAAS,CAACC,SAAS,CAACC,SAAS,CAACnR,eAAe,CAAC;cAC9CiJ,KAAK,CAAC,2BAA2B,CAAC;YACpC,CAAE;YACFqF,SAAS,EAAC,UAAU;YAAAC,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClP,EAAA,CAh5DID,cAAwB;EAAA,QAMVL,SAAS;AAAA;AAAAiS,EAAA,GANvB5R,cAAwB;AAk5D9B,eAAeA,cAAc;AAAC,IAAA4R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}