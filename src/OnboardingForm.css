/* Modal styles for popup feedback */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background: #fff;
  padding: 2rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.2);
  min-width: 300px;
  text-align: center;
  position: relative;
}
.modal-close {
  margin-top: 1.5rem;
  padding: 0.5rem 1.5rem;
  background: #33ACF1;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
}
.modal-close:hover {
  background: #2b93d1;
}

/* JSON Preview Modal Styles */
.json-modal {
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.json-modal h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.2rem;
}

.json-preview {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow: auto;
  flex: 1;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 60vh;
}

.modal-actions {
  display: flex;
  gap: 10px;
  margin-top: 1rem;
  justify-content: flex-end;
}

.btn-copy {
  padding: 0.5rem 1.5rem;
  background: #28a745;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
}

.btn-copy:hover {
  background: #218838;
}
.onboarding-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 20px;
  font-family: 'Inter', sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.form-header h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 2rem;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #33ACF1, #2b93d1);
  transition: width 0.3s ease;
}

.form-section {
  background: white;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.form-section h3 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 16px;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-style: normal;
  line-height: 24px;
  letter-spacing: 0px;
  border-bottom: 2px solid #33ACF1;
  padding-bottom: 10px;
}

.subsection {
  margin-bottom: 30px;
}

.subsection h4 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  line-height: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  margin-bottom: 8px;
  color: #495057;
  font-size: 14px;
  line-height: 20px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #33ACF1;
  box-shadow: 0 0 0 3px rgba(51, 172, 241, 0.1);
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.radio-group label {
  display: flex;
  align-items: center;
  font-weight: normal;
  margin-bottom: 0;
  cursor: pointer;
}

.radio-group input[type="radio"] {
  margin-right: 8px;
  width: auto;
}

.checkbox-group {
  display: flex;
  gap: 15px;
  margin-top: 8px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  font-weight: normal;
  margin-bottom: 0;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 8px;
  width: auto;
}

.language-skill-row {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 20px;
  align-items: end;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 15px;
}

.family-member-section,
.work-experience-section,
.education-section {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.family-member-section h4,
.work-experience-section h4,
.education-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #2c3e50;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
}

.btn-add {
  background-color: #33ACF1;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  transition: background-color 0.3s ease;
}

.btn-add:hover {
  background-color: #2b93d1;
}

.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.nav-left, .nav-center, .nav-right {
  display: flex;
  align-items: center;
}

.nav-center {
  flex: 1;
  justify-content: center;
}

.btn-primary,
.btn-secondary {
  padding: 12px 30px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #33ACF1;
  color: white;
}

.btn-primary:hover {
  background-color: #2b93d1;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
  transform: translateY(-1px);
}

.btn-preview {
  padding: 12px 30px;
  border: 2px solid #17a2b8;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #17a2b8;
}

.btn-preview:hover {
  background-color: #17a2b8;
  color: white;
  transform: translateY(-1px);
}

.btn-sample {
  padding: 12px 30px;
  border: 2px solid #ffc107;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #ffc107;
  margin-right: 10px;
}

.btn-sample:hover {
  background-color: #ffc107;
  color: #212529;
  transform: translateY(-1px);
}

.btn-test {
  padding: 12px 30px;
  border: 2px solid #dc3545;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #dc3545;
  margin-right: 10px;
}

.btn-test:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .onboarding-form {
    padding: 10px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .language-skill-row {
    grid-template-columns: 1fr;
  }
  
  .radio-group,
  .checkbox-group {
    flex-direction: column;
    gap: 10px;
  }
  
  .form-navigation {
    flex-direction: column;
    gap: 15px;
  }

  .nav-left, .nav-center, .nav-right {
    width: 100%;
    justify-content: center;
  }

  .btn-primary,
  .btn-secondary,
  .btn-preview,
  .btn-sample,
  .btn-test {
    width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .nav-center {
    flex-direction: column;
  }
}

/* Required field indicator */
.form-group label:has(+ input[required])::after,
.form-group label:has(+ select[required])::after,
.form-group label:has(+ textarea[required])::after {
  content: " *";
  color: #dc3545;
  font-weight: bold;
}

/* Error states */
.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid {
  border-color: #dc3545;
}

.form-group input:invalid:focus,
.form-group select:invalid:focus,
.form-group textarea:invalid:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Success states */
.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
  border-color: #33ACF1;
}

/* Loading states */
.form-section.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Animation for step transitions */
.form-section {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Declaration form specific styles */
.declaration-text {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #33ACF1;
  margin-bottom: 20px;
}

.declaration-text p {
  margin: 0;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  line-height: 20px;
  color: #2c3e50;
}

.declaration-note {
  background-color: #fff3cd;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
  margin-top: 20px;
}

.declaration-note p {
  margin: 0;
  color: #856404;
  font-size: 0.9rem;
}

/* Checkbox labels in declaration */
.form-group label:has(input[type="checkbox"]) {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  cursor: pointer;
  line-height: 1.5;
}

.form-group input[type="checkbox"] {
  margin-top: 2px;
  flex-shrink: 0;
}

/* File upload styles */
.form-group input[type="file"] {
  padding: 8px;
  border: 2px dashed #e9ecef;
  border-radius: 6px;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.form-group input[type="file"]:hover {
  border-color: #33ACF1;
  background-color: #f0f8ff;
}

.form-group input[type="file"]:focus {
  outline: none;
  border-color: #33ACF1;
  box-shadow: 0 0 0 3px rgba(51, 172, 241, 0.1);
}

.file-preview {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-size: 14px;
  color: #2d5a2d;
  font-weight: 500;
}

.file-preview::before {
  content: "📎";
  font-size: 16px;
}

/* Photo Upload Section Styles */
.photo-upload-section {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #f8f9fa;
  align-items: flex-start;
}

.photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.photo-frame {
  width: 120px;
  height: 150px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  position: relative;
  overflow: hidden;
}

.placeholder-icon {
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploaded-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.photo-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-align: center;
}

.upload-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
}

.upload-content {
  text-align: center;
  max-width: 300px;
}

.upload-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  margin-top: 0;
}

.upload-button {
  display: inline-block;
  padding: 10px 20px;
  background-color: #28a745;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
  border: none;
}

.upload-button:hover {
  background-color: #218838;
}

.hidden-file-input {
  display: none;
}

.file-info {
  margin-top: 10px;
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
}

/* Responsive design for photo upload */
@media (max-width: 768px) {
  .photo-upload-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .upload-area {
    min-height: auto;
    margin-top: 10px;
  }
}
