import React, { useState } from 'react';
import axios from 'axios';
import './OnboardingForm.css';
import { useParams } from 'react-router-dom';

interface PersonalDetails {
  firstName: string;
  middleName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  birthPlace: string;
  age: string;
  marriageDate: string;
  maritalStatus: string;
  nationality: string;
  religion: string;
  nativeState: string;
  contactNumber: string;
  emergencyContactNo: string;
  stateOfDomicile: string;
  email: string;
  presentAddress: string;
  permanentAddress: string;
  presentPin: string;
  permanentPin: string;
  passportPhoto: File | null;
}

interface LanguageSkills {
  language: string;
  speak: boolean;
  read: boolean;
  write: boolean;
}

interface DocumentDetails {
  passportNo: string;
  passportIssueDate: string;
  validUptoDate: string;
  countryOfIssue: string;
  validVisaDetails: string;
  panNumber: string;
  aadharNumber: string;
  passportFile: File | null;
  panFile: File | null;
  aadharFile: File | null;
}

interface FamilyMember {
  name: string;
  relationship: string;
  dateOfBirth: string;
  qualification: string;
  occupation: string;
  organisationAndPosition: string;
}

interface PhysicalDetails {
  height: string;
  weight: string;
  bloodGroup: string;
  eyesightRight: string;
  eyesightLeft: string;
  physicalDisability: string;
  identificationMark: string;
}

interface PreviousEmployment {
  hasBeenInterviewed: boolean;
  interviewDate: string;
  interviewPosition: string;
  interviewCompany: string;
}

interface References {
  relativesInCompany: {
    name: string;
    relationship: string;
    position: string;
    companyAndPhone: string;
  };
  howDidYouKnow: string;
  personalBusiness: boolean;
  businessNature: string;
  contractWithPreviousEmployer: boolean;
  contractDetails: string;
  whenCanJoin: string;
}

interface WorkExperience {
  employerName: string;
  address: string;
  duration: string;
  fromDate: string;
  toDate: string;
  lastPosition: string;
  designation: string;
  natureOfDuties: string;
  immediateSuperior: string;
  grossEmoluments: string;
  lastDrawn: string;
  basicSalary: string;
  fixedSalary: string;
  variableSalary: string;
  grossSalary: string;
}

interface Education {
  examination: string;
  specialisation: string;
  schoolCollege: string;
  university: string;
  fullTimePartTime: string;
  duration: string;
  monthYearPassing: string;
  gradeMarks: string;
  distinctions: string;
  certificateFile: File | null;
}

const OnboardingForm: React.FC = () => {
  // State for user feedback
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showJsonPreview, setShowJsonPreview] = useState<boolean>(false);
  const [jsonPreviewData, setJsonPreviewData] = useState<string>('');
  const { token } = useParams<{ token: string }>();
  const [personalDetails, setPersonalDetails] = useState<PersonalDetails>({
    firstName: ' ',
    middleName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    birthPlace: '',
    age: '',
    marriageDate: '',
    maritalStatus: '',
    nationality: '',
    religion: '',
    nativeState: '',
    contactNumber: '',
    emergencyContactNo: '',
    stateOfDomicile: '',
    email: '',
    presentAddress: '',
    permanentAddress: '',
    presentPin: '',
    permanentPin: '',
    passportPhoto: null
  });

  const [languageSkills, setLanguageSkills] = useState<LanguageSkills[]>([
    { language: '', speak: false, read: false, write: false }
  ]);

  const [documentDetails, setDocumentDetails] = useState<DocumentDetails>({
    passportNo: '',
    passportIssueDate: '',
    validUptoDate: '',
    countryOfIssue: '',
    validVisaDetails: '',
    panNumber: '',
    aadharNumber: '',
    passportFile: null,
    panFile: null,
    aadharFile: null
  });

  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([
    { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }
  ]);

  const [physicalDetails, setPhysicalDetails] = useState<PhysicalDetails>({
    height: '',
    weight: '',
    bloodGroup: '',
    eyesightRight: '',
    eyesightLeft: '',
    physicalDisability: '',
    identificationMark: ''
  });

  const [previousEmployment, setPreviousEmployment] = useState<PreviousEmployment>({
    hasBeenInterviewed: false,
    interviewDate: '',
    interviewPosition: '',
    interviewCompany: ''
  });

  const [references, setReferences] = useState<References>({
    relativesInCompany: {
      name: '',
      relationship: '',
      position: '',
      companyAndPhone: ''
    },
    howDidYouKnow: '',
    personalBusiness: false,
    businessNature: '',
    contractWithPreviousEmployer: false,
    contractDetails: '',
    whenCanJoin: ''
  });

  const [workExperience, setWorkExperience] = useState<WorkExperience[]>([
    {
      employerName: '',
      address: '',
      duration: '',
      fromDate: '',
      toDate: '',
      lastPosition: '',
      designation: '',
      natureOfDuties: '',
      immediateSuperior: '',
      grossEmoluments: '',
      lastDrawn: '',
      basicSalary: '',
      fixedSalary: '',
      variableSalary: '',
      grossSalary: ''
    }
  ]);

  const [education, setEducation] = useState<Education[]>([
    {
      examination: '',
      specialisation: '',
      schoolCollege: '',
      university: '',
      fullTimePartTime: '',
      duration: '',
      monthYearPassing: '',
      gradeMarks: '',
      distinctions: '',
      certificateFile: null
    }
  ]);

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 8;

  // Additional state for new sections
  const [criminalRecords, setCriminalRecords] = useState({
    hasBeenInvolved: false,
    details: '',
    hasBeenConvicted: false,
    convictionDetails: ''
  });

  const [declaration, setDeclaration] = useState({
    notConnectedToDirectors: false,
    isPartnerOrRelative: false,
    partnerRelativeDetails: '',
    place: '',
    date: '',
    applicantSignature: ''
  });

  const handlePersonalDetailsChange = (field: keyof PersonalDetails, value: string) => {
    setPersonalDetails(prev => ({ ...prev, [field]: value }));
  };

  const handleDocumentDetailsChange = (field: keyof DocumentDetails, value: string) => {
    setDocumentDetails(prev => ({ ...prev, [field]: value }));
  };

  const handlePhysicalDetailsChange = (field: keyof PhysicalDetails, value: string) => {
    setPhysicalDetails(prev => ({ ...prev, [field]: value }));
  };

  // Helper function to convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const handleFileUpload = (file: File | null, section: 'personal' | 'document' | 'education', field: string, index?: number) => {
    // Check file size for passport photo (100KB limit)
    if (file && section === 'personal' && field === 'passportPhoto') {
      const maxSize = 100 * 1024; // 100KB in bytes
      if (file.size > maxSize) {
        alert('Please upload an image less than 100KB');
        return;
      }
    }

    if (section === 'personal') {
      setPersonalDetails(prev => ({ ...prev, [field]: file }));
    } else if (section === 'document') {
      setDocumentDetails(prev => ({ ...prev, [field]: file }));
    } else if (section === 'education' && index !== undefined) {
      setEducation(prev => {
        const newEducation = [...prev];
        newEducation[index] = { ...newEducation[index], [field]: file };
        return newEducation;
      });
    }
  };

  const addLanguageSkill = () => {
    setLanguageSkills(prev => [...prev, { language: '', speak: false, read: false, write: false }]);
  };

  const addFamilyMember = () => {
    setFamilyMembers(prev => [...prev, { name: '', relationship: '', dateOfBirth: '', qualification: '', occupation: '', organisationAndPosition: '' }]);
  };

  const addWorkExperience = () => {
    setWorkExperience(prev => [...prev, {
      employerName: '',
      address: '',
      duration: '',
      fromDate: '',
      toDate: '',
      lastPosition: '',
      designation: '',
      natureOfDuties: '',
      immediateSuperior: '',
      grossEmoluments: '',
      lastDrawn: '',
      basicSalary: '',
      fixedSalary: '',
      variableSalary: '',
      grossSalary: ''
    }]);
  };

  const addEducation = () => {
    setEducation(prev => [...prev, {
      examination: '',
      specialisation: '',
      schoolCollege: '',
      university: '',
      fullTimePartTime: '',
      duration: '',
      monthYearPassing: '',
      gradeMarks: '',
      distinctions: '',
      certificateFile: null
    }]);
  };

  const getStepTitle = (step: number) => {
    const titles = [
      '',
      'PERSONAL DETAILS',
      'FAMILY DATA',
      'HEALTH DATA',
      'EDUCATION DETAILS',
      'WORK HISTORY DATA',
      'GENERAL DATA',
      'CRIMINAL RECORDS',
      'DECLARATION FORM'
    ];
    return titles[step] || '';
  };

  const nextStep = () => {
    console.log('nextStep called. Current step:', currentStep, 'Total steps:', totalSteps);
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
      console.log('Moving to step:', currentStep + 1);
    } else {
      console.log('Already on final step, not moving forward');
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Helper: Validate required fields (add more as needed)
  const validateRequiredFields = () => {
    const requiredFields = [
      personalDetails.firstName?.trim(),
      personalDetails.lastName?.trim(),
      personalDetails.dateOfBirth?.trim(),
      personalDetails.gender?.trim(),
      personalDetails.nationality?.trim(),
      personalDetails.contactNumber?.trim(),
      personalDetails.emergencyContactNo?.trim(),
    ];
    return requiredFields.every(Boolean);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setSuccessMessage(null);
    setErrorMessage(null);

    // Validate all required fields before submission
    if (!validateRequiredFields()) {
      setErrorMessage("Please fill all required fields marked with * before submitting the form.");
      return;
    }
    if (currentStep !== totalSteps) {
      setErrorMessage("Please complete all steps before submitting the form.");
      return;
    }

    try {
      // Convert all files to base64
      const processedPersonalDetails = { ...personalDetails };
      if (personalDetails.passportPhoto) {
        processedPersonalDetails.passportPhoto = await fileToBase64(personalDetails.passportPhoto) as any;
      }

      const processedDocumentDetails = { ...documentDetails };
      if (documentDetails.passportFile) {
        processedDocumentDetails.passportFile = await fileToBase64(documentDetails.passportFile) as any;
      }
      if (documentDetails.panFile) {
        processedDocumentDetails.panFile = await fileToBase64(documentDetails.panFile) as any;
      }
      if (documentDetails.aadharFile) {
        processedDocumentDetails.aadharFile = await fileToBase64(documentDetails.aadharFile) as any;
      }

      const processedEducation = await Promise.all(
        education.map(async (edu) => {
          const processedEdu = { ...edu };
          if (edu.certificateFile) {
            processedEdu.certificateFile = await fileToBase64(edu.certificateFile) as any;
          }
          return processedEdu;
        })
      );

      // Create the complete JSON object
      const completeFormData = {
        personalDetails: processedPersonalDetails,
        languageSkills,
        documentDetails: processedDocumentDetails,
        familyMembers,
        physicalDetails,
        previousEmployment,
        references,
        workExperience,
        education: processedEducation,
        criminalRecords,
        declaration,
        submissionTimestamp: new Date().toISOString(),
        formVersion: "1.0"
      };

      // Log the complete JSON for debugging
      console.log("Complete Form Data JSON:", JSON.stringify(completeFormData, null, 2));

      // Send to API
      const url = `http://192.168.1.132:8000/api/onboarding/submit-form/${token}/`;
      await axios.post(url, completeFormData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      setSuccessMessage("Form submitted successfully!");
      setErrorMessage(null);

      // Optional: Download JSON file locally
      downloadJsonFile(completeFormData);

    } catch (error) {
      console.error("Form submission error:", error);
      setErrorMessage("Failed to submit the form. Please try again later.");
      setSuccessMessage(null);
    }
  };

  // Function to download JSON file locally
  const downloadJsonFile = (data: any) => {
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `onboarding-form-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Function to fill sample data for testing
  const fillSampleData = () => {
    setPersonalDetails({
      firstName: 'John',
      middleName: 'Michael',
      lastName: 'Doe',
      dateOfBirth: '1990-01-15',
      gender: 'Male',
      birthPlace: 'New York',
      age: '34',
      marriageDate: '2020-06-15',
      maritalStatus: 'Married',
      nationality: 'American',
      religion: 'Christian',
      nativeState: 'New York',
      contactNumber: '+1-555-0123',
      emergencyContactNo: '+1-555-0124',
      stateOfDomicile: 'New York',
      email: '<EMAIL>',
      presentAddress: '123 Main Street, Apartment 4B, New York, NY 10001',
      permanentAddress: '456 Oak Avenue, Hometown, NY 12345',
      presentPin: '10001',
      permanentPin: '12345',
      passportPhoto: null
    });

    setLanguageSkills([
      { language: 'English', speak: true, read: true, write: true },
      { language: 'Spanish', speak: true, read: false, write: false }
    ]);

    setDocumentDetails({
      passportNo: '*********',
      passportIssueDate: '2020-01-01',
      validUptoDate: '2030-01-01',
      countryOfIssue: 'USA',
      validVisaDetails: 'Valid until 2025',
      panNumber: '**********',
      aadharNumber: '1234-5678-9012',
      passportFile: null,
      panFile: null,
      aadharFile: null
    });

    alert('Sample data filled! You can now preview the JSON structure.');
  };

  // Function to preview current form data as JSON
  const previewFormData = async () => {
    try {
      // Convert files to base64 for preview
      const processedPersonalDetails = { ...personalDetails };
      if (personalDetails.passportPhoto) {
        processedPersonalDetails.passportPhoto = `[FILE: ${personalDetails.passportPhoto.name}]` as any;
      }

      const processedDocumentDetails = { ...documentDetails };
      if (documentDetails.passportFile) {
        processedDocumentDetails.passportFile = `[FILE: ${documentDetails.passportFile.name}]` as any;
      }
      if (documentDetails.panFile) {
        processedDocumentDetails.panFile = `[FILE: ${documentDetails.panFile.name}]` as any;
      }
      if (documentDetails.aadharFile) {
        processedDocumentDetails.aadharFile = `[FILE: ${documentDetails.aadharFile.name}]` as any;
      }

      const processedEducation = education.map((edu) => {
        const processedEdu = { ...edu };
        if (edu.certificateFile) {
          processedEdu.certificateFile = `[FILE: ${edu.certificateFile.name}]` as any;
        }
        return processedEdu;
      });

      const previewData = {
        personalDetails: processedPersonalDetails,
        languageSkills,
        documentDetails: processedDocumentDetails,
        familyMembers,
        physicalDetails,
        previousEmployment,
        references,
        workExperience,
        education: processedEducation,
        criminalRecords,
        declaration,
        submissionTimestamp: new Date().toISOString(),
        formVersion: "1.0"
      };

      // Show JSON in console and modal
      const jsonString = JSON.stringify(previewData, null, 2);
      console.log("Form Data Preview:", jsonString);
      setJsonPreviewData(jsonString);
      setShowJsonPreview(true);

      // Optional: Download preview JSON
      downloadJsonFile(previewData);
    } catch (error) {
      console.error("Preview error:", error);
      alert("Error generating preview");
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="form-section">
            <h3>PERSONAL DETAILS</h3>

            {/* Passport Photo Upload Section */}
            <div className="photo-upload-section">
              <div className="photo-placeholder">
                <div className="photo-frame">
                  {personalDetails.passportPhoto ? (
                    <img
                      src={URL.createObjectURL(personalDetails.passportPhoto)}
                      alt="Passport Photo"
                      className="uploaded-photo"
                    />
                  ) : (
                    <div className="placeholder-icon">
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                  )}
                </div>
                <span className="photo-label">Uploaded Photo</span>
              </div>
              <div className="upload-area">
                <div className="upload-content">
                  <p className="upload-text">Please upload a image less than 100kb</p>
                  <label className="upload-button">
                    Choose a file
                    <input
                      type="file"
                      accept="image/*"
                      required
                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'personal', 'passportPhoto')}
                      className="hidden-file-input"
                    />
                  </label>
                  {personalDetails.passportPhoto && (
                    <p className="file-info">{personalDetails.passportPhoto.name}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="form-grid">
              <div className="form-group">
                <label>First Name *</label>
                <input
                  type="text"
                  required
                  value={personalDetails.firstName}
                  onChange={(e) => handlePersonalDetailsChange('firstName', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Middle Name</label>
                <input
                  type="text"
                  value={personalDetails.middleName}
                  onChange={(e) => handlePersonalDetailsChange('middleName', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Last Name *</label>
                <input
                  type="text"
                  required
                  value={personalDetails.lastName}
                  onChange={(e) => handlePersonalDetailsChange('lastName', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Date of Birth *</label>
                <input
                  type="date"
                  required
                  value={personalDetails.dateOfBirth}
                  onChange={(e) => handlePersonalDetailsChange('dateOfBirth', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Gender *</label>
                <select
                  required
                  value={personalDetails.gender}
                  onChange={(e) => handlePersonalDetailsChange('gender', e.target.value)}
                >
                  <option value="">Select Gender</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div className="form-group">
                <label>Birth Place</label>
                <input
                  type="text"
                  value={personalDetails.birthPlace}
                  onChange={(e) => handlePersonalDetailsChange('birthPlace', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Age</label>
                <input
                  type="number"
                  value={personalDetails.age}
                  onChange={(e) => handlePersonalDetailsChange('age', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Marriage Date</label>
                <input
                  type="date"
                  value={personalDetails.marriageDate}
                  onChange={(e) => handlePersonalDetailsChange('marriageDate', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Marital Status</label>
                <select
                  value={personalDetails.maritalStatus}
                  onChange={(e) => handlePersonalDetailsChange('maritalStatus', e.target.value)}
                >
                  <option value="">Select Status</option>
                  <option value="Single">Single</option>
                  <option value="Married">Married</option>
                  <option value="Divorced">Divorced</option>
                  <option value="Widowed">Widowed</option>
                </select>
              </div>
              <div className="form-group">
                <label>Nationality *</label>
                <input
                  type="text"
                  required
                  value={personalDetails.nationality}
                  onChange={(e) => handlePersonalDetailsChange('nationality', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Religion</label>
                <input
                  type="text"
                  value={personalDetails.religion}
                  onChange={(e) => handlePersonalDetailsChange('religion', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Native State</label>
                <input
                  type="text"
                  value={personalDetails.nativeState}
                  onChange={(e) => handlePersonalDetailsChange('nativeState', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Contact Number *</label>
                <input
                  type="tel"
                  required
                  value={personalDetails.contactNumber}
                  onChange={(e) => handlePersonalDetailsChange('contactNumber', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Emergency Contact No. *</label>
                <input
                  type="tel"
                  required
                  value={personalDetails.emergencyContactNo}
                  onChange={(e) => handlePersonalDetailsChange('emergencyContactNo', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>State of Domicile</label>
                <input
                  type="text"
                  value={personalDetails.stateOfDomicile}
                  onChange={(e) => handlePersonalDetailsChange('stateOfDomicile', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Email *</label>
                <input
                  type="email"
                  required
                  value={personalDetails.email}
                  onChange={(e) => handlePersonalDetailsChange('email', e.target.value)}
                />
              </div>
              <div className="form-group full-width">
                <label>Present Address *</label>
                <textarea
                  required
                  value={personalDetails.presentAddress}
                  onChange={(e) => handlePersonalDetailsChange('presentAddress', e.target.value)}
                />
              </div>
              <div className="form-group full-width">
                <label>Permanent Address *</label>
                <textarea
                  required
                  value={personalDetails.permanentAddress}
                  onChange={(e) => handlePersonalDetailsChange('permanentAddress', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Present PIN</label>
                <input
                  type="text"
                  value={personalDetails.presentPin}
                  onChange={(e) => handlePersonalDetailsChange('presentPin', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>Permanent PIN</label>
                <input
                  type="text"
                  value={personalDetails.permanentPin}
                  onChange={(e) => handlePersonalDetailsChange('permanentPin', e.target.value)}
                />
              </div>
            </div>

            <div className="subsection">
              <h4>Document Details</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Passport No.</label>
                  <input
                    type="text"
                    value={documentDetails.passportNo}
                    onChange={(e) => handleDocumentDetailsChange('passportNo', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Passport Issue Date</label>
                  <input
                    type="date"
                    value={documentDetails.passportIssueDate}
                    onChange={(e) => handleDocumentDetailsChange('passportIssueDate', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Valid Upto Date</label>
                  <input
                    type="date"
                    value={documentDetails.validUptoDate}
                    onChange={(e) => handleDocumentDetailsChange('validUptoDate', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Country of Issue</label>
                  <input
                    type="text"
                    value={documentDetails.countryOfIssue}
                    onChange={(e) => handleDocumentDetailsChange('countryOfIssue', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Valid Visa Details</label>
                  <input
                    type="text"
                    value={documentDetails.validVisaDetails}
                    onChange={(e) => handleDocumentDetailsChange('validVisaDetails', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>PAN Number *</label>
                  <input
                    type="text"
                    required
                    value={documentDetails.panNumber}
                    onChange={(e) => handleDocumentDetailsChange('panNumber', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>PAN Card Upload</label>
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'panFile')}
                  />
                  {documentDetails.panFile && (
                    <div className="file-preview">
                      <span className="file-name">{documentDetails.panFile.name}</span>
                    </div>
                  )}
                </div>
                <div className="form-group">
                  <label>Aadhar Number *</label>
                  <input
                    type="text"
                    required
                    value={documentDetails.aadharNumber}
                    onChange={(e) => handleDocumentDetailsChange('aadharNumber', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Aadhar Card Upload</label>
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'aadharFile')}
                  />
                  {documentDetails.aadharFile && (
                    <div className="file-preview">
                      <span className="file-name">{documentDetails.aadharFile.name}</span>
                    </div>
                  )}
                </div>
                <div className="form-group">
                  <label>Passport Upload</label>
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'document', 'passportFile')}
                  />
                  {documentDetails.passportFile && (
                    <div className="file-preview">
                      <span className="file-name">{documentDetails.passportFile.name}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="subsection">
              <h4>Language Known</h4>
              {languageSkills.map((skill, index) => (
                <div key={index} className="language-skill-row">
                  <div className="form-group">
                    <label>Language</label>
                    <input
                      type="text"
                      value={skill.language}
                      onChange={(e) => {
                        const newSkills = [...languageSkills];
                        newSkills[index].language = e.target.value;
                        setLanguageSkills(newSkills);
                      }}
                    />
                  </div>
                  <div className="checkbox-group">
                    <label>
                      <input
                        type="checkbox"
                        checked={skill.speak}
                        onChange={(e) => {
                          const newSkills = [...languageSkills];
                          newSkills[index].speak = e.target.checked;
                          setLanguageSkills(newSkills);
                        }}
                      />
                      Speak
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={skill.read}
                        onChange={(e) => {
                          const newSkills = [...languageSkills];
                          newSkills[index].read = e.target.checked;
                          setLanguageSkills(newSkills);
                        }}
                      />
                      Read
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={skill.write}
                        onChange={(e) => {
                          const newSkills = [...languageSkills];
                          newSkills[index].write = e.target.checked;
                          setLanguageSkills(newSkills);
                        }}
                      />
                      Write
                    </label>
                  </div>
                </div>
              ))}
              <button type="button" onClick={addLanguageSkill} className="btn-add">
                Add Language
              </button>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="form-section">
            <h3>FAMILY DATA</h3>

            <div className="subsection">
              <h4>Family Members</h4>
              {familyMembers.map((member, index) => (
                <div key={index} className="family-member-section">
                  <div className="form-grid">
                    <div className="form-group">
                      <label>Name</label>
                      <input
                        type="text"
                        value={member.name}
                        onChange={(e) => {
                          const newMembers = [...familyMembers];
                          newMembers[index].name = e.target.value;
                          setFamilyMembers(newMembers);
                        }}
                      />
                    </div>
                    <div className="form-group">
                      <label>Relationship</label>
                      <input
                        type="text"
                        value={member.relationship}
                        onChange={(e) => {
                          const newMembers = [...familyMembers];
                          newMembers[index].relationship = e.target.value;
                          setFamilyMembers(newMembers);
                        }}
                      />
                    </div>
                    <div className="form-group">
                      <label>Date of Birth</label>
                      <input
                        type="date"
                        value={member.dateOfBirth}
                        onChange={(e) => {
                          const newMembers = [...familyMembers];
                          newMembers[index].dateOfBirth = e.target.value;
                          setFamilyMembers(newMembers);
                        }}
                      />
                    </div>
                    <div className="form-group">
                      <label>Qualification</label>
                      <input
                        type="text"
                        value={member.qualification}
                        onChange={(e) => {
                          const newMembers = [...familyMembers];
                          newMembers[index].qualification = e.target.value;
                          setFamilyMembers(newMembers);
                        }}
                      />
                    </div>
                    <div className="form-group">
                      <label>Occupation</label>
                      <input
                        type="text"
                        value={member.occupation}
                        onChange={(e) => {
                          const newMembers = [...familyMembers];
                          newMembers[index].occupation = e.target.value;
                          setFamilyMembers(newMembers);
                        }}
                      />
                    </div>
                    <div className="form-group">
                      <label>Organisation and Position</label>
                      <input
                        type="text"
                        value={member.organisationAndPosition}
                        onChange={(e) => {
                          const newMembers = [...familyMembers];
                          newMembers[index].organisationAndPosition = e.target.value;
                          setFamilyMembers(newMembers);
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
              <button type="button" onClick={addFamilyMember} className="btn-add">
                Add Family Member
              </button>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="form-section">
            <h3>HEALTH DATA</h3>

            <div className="subsection">
              <h4>Physical Details</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Height</label>
                  <input
                    type="text"
                    value={physicalDetails.height}
                    onChange={(e) => handlePhysicalDetailsChange('height', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Weight</label>
                  <input
                    type="text"
                    value={physicalDetails.weight}
                    onChange={(e) => handlePhysicalDetailsChange('weight', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Blood Group</label>
                  <select
                    value={physicalDetails.bloodGroup}
                    onChange={(e) => handlePhysicalDetailsChange('bloodGroup', e.target.value)}
                  >
                    <option value="">Select Blood Group</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>Eyesight Right</label>
                  <input
                    type="text"
                    value={physicalDetails.eyesightRight}
                    onChange={(e) => handlePhysicalDetailsChange('eyesightRight', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Eyesight Left</label>
                  <input
                    type="text"
                    value={physicalDetails.eyesightLeft}
                    onChange={(e) => handlePhysicalDetailsChange('eyesightLeft', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Physical Disability</label>
                  <input
                    type="text"
                    value={physicalDetails.physicalDisability}
                    onChange={(e) => handlePhysicalDetailsChange('physicalDisability', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label>Identification Mark</label>
                  <input
                    type="text"
                    value={physicalDetails.identificationMark}
                    onChange={(e) => handlePhysicalDetailsChange('identificationMark', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="form-section">
            <h3>EDUCATION DETAILS</h3>

            {education.map((edu, index) => (
              <div key={index} className="education-section">
                <h4>Education {index + 1}</h4>
                <div className="form-grid">
                  <div className="form-group">
                    <label>Examination Passed</label>
                    <input
                      type="text"
                      value={edu.examination}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].examination = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Specialisation</label>
                    <input
                      type="text"
                      value={edu.specialisation}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].specialisation = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>School/College/Institution</label>
                    <input
                      type="text"
                      value={edu.schoolCollege}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].schoolCollege = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>University/Board</label>
                    <input
                      type="text"
                      value={edu.university}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].university = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Full Time/Part Time/Correspondence</label>
                    <select
                      value={edu.fullTimePartTime}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].fullTimePartTime = e.target.value;
                        setEducation(newEducation);
                      }}
                    >
                      <option value="">Select Type</option>
                      <option value="Full Time">Full Time</option>
                      <option value="Part Time">Part Time</option>
                      <option value="Correspondence">Correspondence</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Duration of Course</label>
                    <input
                      type="text"
                      value={edu.duration}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].duration = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Month & Year of Passing</label>
                    <input
                      type="text"
                      value={edu.monthYearPassing}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].monthYearPassing = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Grade/% Marks</label>
                    <input
                      type="text"
                      value={edu.gradeMarks}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].gradeMarks = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group full-width">
                    <label>Distinctions/Scholarships/Prizes Won</label>
                    <textarea
                      value={edu.distinctions}
                      onChange={(e) => {
                        const newEducation = [...education];
                        newEducation[index].distinctions = e.target.value;
                        setEducation(newEducation);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Certificate/Document Upload</label>
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) => handleFileUpload(e.target.files?.[0] || null, 'education', 'certificateFile', index)}
                    />
                    {edu.certificateFile && (
                      <div className="file-preview">
                        <span className="file-name">{edu.certificateFile.name}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            <button type="button" onClick={addEducation} className="btn-add">
              Add Education
            </button>
          </div>
        );

      case 5:
        return (
          <div className="form-section">
            <h3>WORK HISTORY DATA</h3>

            {workExperience.map((experience, index) => (
              <div key={index} className="work-experience-section">
                <h4>Work Experience {index + 1}</h4>
                <div className="form-grid">
                  <div className="form-group">
                    <label>Employer's Name</label>
                    <input
                      type="text"
                      value={experience.employerName}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].employerName = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group full-width">
                    <label>Address</label>
                    <textarea
                      value={experience.address}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].address = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>From Date</label>
                    <input
                      type="date"
                      value={experience.fromDate}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].fromDate = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>To Date</label>
                    <input
                      type="date"
                      value={experience.toDate}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].toDate = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Last Position Held</label>
                    <input
                      type="text"
                      value={experience.lastPosition}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].lastPosition = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Designation</label>
                    <input
                      type="text"
                      value={experience.designation}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].designation = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group full-width">
                    <label>Nature of Duties</label>
                    <textarea
                      value={experience.natureOfDuties}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].natureOfDuties = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Name & Designation of Immediate Superior</label>
                    <input
                      type="text"
                      value={experience.immediateSuperior}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].immediateSuperior = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Gross Emoluments (Rs. Per month at joining)</label>
                    <input
                      type="number"
                      value={experience.grossEmoluments}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].grossEmoluments = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Last Drawn</label>
                    <input
                      type="number"
                      value={experience.lastDrawn}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].lastDrawn = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Basic Salary</label>
                    <input
                      type="number"
                      value={experience.basicSalary}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].basicSalary = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Fixed Salary</label>
                    <input
                      type="number"
                      value={experience.fixedSalary}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].fixedSalary = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Variable Salary</label>
                    <input
                      type="number"
                      value={experience.variableSalary}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].variableSalary = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>Gross Salary</label>
                    <input
                      type="number"
                      value={experience.grossSalary}
                      onChange={(e) => {
                        const newExperience = [...workExperience];
                        newExperience[index].grossSalary = e.target.value;
                        setWorkExperience(newExperience);
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
            <button type="button" onClick={addWorkExperience} className="btn-add">
              Add Work Experience
            </button>
          </div>
        );

      case 6:
        return (
          <div className="form-section">
            <h3>GENERAL DATA</h3>

            <div className="subsection">
              <h4>Previous Interview with MH Group</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Have you ever been interviewed by any of the MH Group of Companies?</label>
                  <div className="radio-group">
                    <label>
                      <input
                        type="radio"
                        name="hasBeenInterviewed"
                        value="true"
                        checked={previousEmployment.hasBeenInterviewed === true}
                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: true }))}
                      />
                      Yes
                    </label>
                    <label>
                      <input
                        type="radio"
                        name="hasBeenInterviewed"
                        value="false"
                        checked={previousEmployment.hasBeenInterviewed === false}
                        onChange={() => setPreviousEmployment(prev => ({ ...prev, hasBeenInterviewed: false }))}
                      />
                      No
                    </label>
                  </div>
                </div>
                {previousEmployment.hasBeenInterviewed && (
                  <>
                    <div className="form-group">
                      <label>Date/Year</label>
                      <input
                        type="text"
                        value={previousEmployment.interviewDate}
                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewDate: e.target.value }))}
                      />
                    </div>
                    <div className="form-group">
                      <label>Position</label>
                      <input
                        type="text"
                        value={previousEmployment.interviewPosition}
                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewPosition: e.target.value }))}
                      />
                    </div>
                    <div className="form-group">
                      <label>Company</label>
                      <input
                        type="text"
                        value={previousEmployment.interviewCompany}
                        onChange={(e) => setPreviousEmployment(prev => ({ ...prev, interviewCompany: e.target.value }))}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="subsection">
              <h4>References & Additional Information</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Relatives/Acquaintance in MH Group - Name</label>
                  <input
                    type="text"
                    value={references.relativesInCompany.name}
                    onChange={(e) => setReferences(prev => ({
                      ...prev,
                      relativesInCompany: { ...prev.relativesInCompany, name: e.target.value }
                    }))}
                  />
                </div>
                <div className="form-group">
                  <label>Relationship</label>
                  <input
                    type="text"
                    value={references.relativesInCompany.relationship}
                    onChange={(e) => setReferences(prev => ({
                      ...prev,
                      relativesInCompany: { ...prev.relativesInCompany, relationship: e.target.value }
                    }))}
                  />
                </div>
                <div className="form-group">
                  <label>Position</label>
                  <input
                    type="text"
                    value={references.relativesInCompany.position}
                    onChange={(e) => setReferences(prev => ({
                      ...prev,
                      relativesInCompany: { ...prev.relativesInCompany, position: e.target.value }
                    }))}
                  />
                </div>
                <div className="form-group">
                  <label>Company & Phone No.</label>
                  <input
                    type="text"
                    value={references.relativesInCompany.companyAndPhone}
                    onChange={(e) => setReferences(prev => ({
                      ...prev,
                      relativesInCompany: { ...prev.relativesInCompany, companyAndPhone: e.target.value }
                    }))}
                  />
                </div>
                <div className="form-group full-width">
                  <label>How did you come to know of this position?</label>
                  <textarea
                    value={references.howDidYouKnow}
                    onChange={(e) => setReferences(prev => ({ ...prev, howDidYouKnow: e.target.value }))}
                  />
                </div>
                <div className="form-group">
                  <label>Are you engaged in any personal business?</label>
                  <div className="radio-group">
                    <label>
                      <input
                        type="radio"
                        name="personalBusiness"
                        value="true"
                        checked={references.personalBusiness === true}
                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: true }))}
                      />
                      Yes
                    </label>
                    <label>
                      <input
                        type="radio"
                        name="personalBusiness"
                        value="false"
                        checked={references.personalBusiness === false}
                        onChange={() => setReferences(prev => ({ ...prev, personalBusiness: false }))}
                      />
                      No
                    </label>
                  </div>
                </div>
                {references.personalBusiness && (
                  <div className="form-group full-width">
                    <label>If YES, indicate nature of business</label>
                    <textarea
                      value={references.businessNature}
                      onChange={(e) => setReferences(prev => ({ ...prev, businessNature: e.target.value }))}
                    />
                  </div>
                )}

                <div className="form-group">
                  <label>Do you have any contract/bond with your previous employer?</label>
                  <div className="radio-group">
                    <label>
                      <input
                        type="radio"
                        name="contractWithPreviousEmployer"
                        value="true"
                        checked={references.contractWithPreviousEmployer === true}
                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: true }))}
                      />
                      Yes
                    </label>
                    <label>
                      <input
                        type="radio"
                        name="contractWithPreviousEmployer"
                        value="false"
                        checked={references.contractWithPreviousEmployer === false}
                        onChange={() => setReferences(prev => ({ ...prev, contractWithPreviousEmployer: false }))}
                      />
                      No
                    </label>
                  </div>
                </div>
                {references.contractWithPreviousEmployer && (
                  <div className="form-group full-width">
                    <label>If YES, Give Details</label>
                    <textarea
                      value={references.contractDetails}
                      onChange={(e) => setReferences(prev => ({ ...prev, contractDetails: e.target.value }))}
                    />
                  </div>
                )}

                <div className="form-group">
                  <label>If selected, when can you join?</label>
                  <input
                    type="date"
                    value={references.whenCanJoin}
                    onChange={(e) => setReferences(prev => ({ ...prev, whenCanJoin: e.target.value }))}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 7:
        return (
          <div className="form-section">
            <h3>CRIMINAL RECORDS</h3>

            <div className="form-grid">
              <div className="form-group">
                <label>Have you ever been involved in any criminal proceedings?</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      name="hasBeenInvolved"
                      value="true"
                      checked={criminalRecords.hasBeenInvolved === true}
                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: true }))}
                    />
                    Yes
                  </label>
                  <label>
                    <input
                      type="radio"
                      name="hasBeenInvolved"
                      value="false"
                      checked={criminalRecords.hasBeenInvolved === false}
                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenInvolved: false }))}
                    />
                    No
                  </label>
                </div>
              </div>
              {criminalRecords.hasBeenInvolved && (
                <div className="form-group full-width">
                  <label>If YES, give details</label>
                  <textarea
                    value={criminalRecords.details}
                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, details: e.target.value }))}
                  />
                </div>
              )}

              <div className="form-group">
                <label>Have you ever been convicted of any offence?</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      name="hasBeenConvicted"
                      value="true"
                      checked={criminalRecords.hasBeenConvicted === true}
                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: true }))}
                    />
                    Yes
                  </label>
                  <label>
                    <input
                      type="radio"
                      name="hasBeenConvicted"
                      value="false"
                      checked={criminalRecords.hasBeenConvicted === false}
                      onChange={() => setCriminalRecords(prev => ({ ...prev, hasBeenConvicted: false }))}
                    />
                    No
                  </label>
                </div>
              </div>
              {criminalRecords.hasBeenConvicted && (
                <div className="form-group full-width">
                  <label>If YES, give details</label>
                  <textarea
                    value={criminalRecords.convictionDetails}
                    onChange={(e) => setCriminalRecords(prev => ({ ...prev, convictionDetails: e.target.value }))}
                  />
                </div>
              )}
            </div>
          </div>
        );

      case 8:
        return (
          <div className="form-section">
            <h3>DECLARATION FORM</h3>

            <div className="form-grid">
              <div className="form-group full-width">
                <div className="declaration-text">
                  <p><strong>I hereby declare that:</strong></p>
                </div>
              </div>

              <div className="form-group full-width">
                <label>
                  <input
                    type="checkbox"
                    checked={declaration.notConnectedToDirectors}
                    onChange={(e) => setDeclaration(prev => ({ ...prev, notConnectedToDirectors: e.target.checked }))}
                  />
                  I am not connected with any of the Directors of the Company as his partner or his relative as defined under section 6 of the Companies Act 1956
                </label>
              </div>

              <div className="form-group">
                <label>OR I am a partner or relative of a Director of the Company</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      name="isPartnerOrRelative"
                      value="true"
                      checked={declaration.isPartnerOrRelative === true}
                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: true }))}
                    />
                    Yes
                  </label>
                  <label>
                    <input
                      type="radio"
                      name="isPartnerOrRelative"
                      value="false"
                      checked={declaration.isPartnerOrRelative === false}
                      onChange={() => setDeclaration(prev => ({ ...prev, isPartnerOrRelative: false }))}
                    />
                    No
                  </label>
                </div>
              </div>

              {declaration.isPartnerOrRelative && (
                <div className="form-group full-width">
                  <label>If YES, provide details</label>
                  <textarea
                    value={declaration.partnerRelativeDetails}
                    onChange={(e) => setDeclaration(prev => ({ ...prev, partnerRelativeDetails: e.target.value }))}
                  />
                </div>
              )}

              <div className="form-group">
                <label>Place</label>
                <input
                  type="text"
                  value={declaration.place}
                  onChange={(e) => setDeclaration(prev => ({ ...prev, place: e.target.value }))}
                />
              </div>

              <div className="form-group">
                <label>Date</label>
                <input
                  type="date"
                  value={declaration.date}
                  onChange={(e) => setDeclaration(prev => ({ ...prev, date: e.target.value }))}
                />
              </div>

              <div className="form-group full-width">
                <label>Applicant's Signature</label>
                <input
                  type="text"
                  placeholder="Type your full name as signature"
                  value={declaration.applicantSignature}
                  onChange={(e) => setDeclaration(prev => ({ ...prev, applicantSignature: e.target.value }))}
                />
              </div>

              <div className="form-group full-width">
                <div className="declaration-note">
                  <p><em>Note: By typing your name above, you are providing your electronic signature and agreeing to the terms and conditions of this declaration.</em></p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Step {currentStep}</div>;
    }
  };

  // Modal close handler (must be after useState hooks)
  const closeModal = () => {
    setSuccessMessage(null);
    setErrorMessage(null);
  };

  return (
    <div className="onboarding-form">
      <div className="form-header">
        <h2>Employee Onboarding Form</h2>
        <div className="progress-bar">
          <div
            className="progress-fill"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
        <p>Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}</p>
      </div>


      {/* Popup Modal for feedback messages */}
      {(successMessage || errorMessage) && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className={successMessage ? "success-message" : "error-message"}>
              {successMessage || errorMessage}
            </div>
            <button className="modal-close" onClick={closeModal}>Close</button>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {renderStep()}
      </form>

      <div className="form-navigation">
        <div className="nav-left">
          {currentStep > 1 && (
            <button type="button" onClick={prevStep} className="btn-secondary">
              Previous
            </button>
          )}
        </div>

        <div className="nav-center">
          <button type="button" onClick={fillSampleData} className="btn-sample">
            Fill Sample Data
          </button>
          <button type="button" onClick={previewFormData} className="btn-preview">
            Preview JSON
          </button>
        </div>

        <div className="nav-right">
          {currentStep < totalSteps ? (
            <button type="button" onClick={nextStep} className="btn-primary">
              Next
            </button>
          ) : (
            <button type="button" onClick={() => {
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }} className="btn-primary">
              Submit Form
            </button>
          )}
        </div>
      </div>

      {/* JSON Preview Modal */}
      {showJsonPreview && (
        <div className="modal-overlay">
          <div className="modal-content json-modal">
            <h3>Form Data JSON Preview</h3>
            <pre className="json-preview">
              {jsonPreviewData}
            </pre>
            <div className="modal-actions">
              <button
                onClick={() => setShowJsonPreview(false)}
                className="modal-close"
              >
                Close
              </button>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(jsonPreviewData);
                  alert('JSON copied to clipboard!');
                }}
                className="btn-copy"
              >
                Copy JSON
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OnboardingForm;