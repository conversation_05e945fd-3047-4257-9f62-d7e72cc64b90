---
description: Repository Information Overview
alwaysApply: true
---

# Onboarding Form App Information

## Summary
A React-based web application for employee onboarding forms. The application provides a multi-step form interface for collecting comprehensive employee information including personal details, family data, health information, education, work history, and other relevant information. The form data is submitted to a backend API endpoint.

## Structure
- **public/**: Contains the HTML entry point for the React application
- **src/**: Contains the React components, styles, and application logic
- **node_modules/**: Contains project dependencies (managed by npm)

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: TypeScript 4.9.x
**Build System**: React Scripts (Create React App)
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- React 18.2.0
- React DOM 18.2.0
- React Router DOM 7.8.2
- Axios 1.11.0
- TypeScript 4.9.0

**Development Dependencies**:
- React Scripts 5.0.1
- Various TypeScript type definitions (@types/*)

## Build & Installation
```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test
```

## Main Files & Resources
**Application Entry Point**: src/index.tsx
**Main Component**: src/App.tsx
**Form Component**: src/OnboardingForm.tsx
**Routing Configuration**: Defined in App.tsx using React Router

## Project Structure
The application follows a standard React application structure with TypeScript support:

- **App.tsx**: Main application component with routing configuration
- **OnboardingForm.tsx**: Complex multi-step form component with various sections
- **index.tsx**: Application entry point that renders the App component
- **CSS files**: Styling for the components

The form is designed with multiple steps:
1. Personal Details
2. Family Data
3. Health Data
4. Education Details
5. Work History Data
6. General Data
7. Criminal Records
8. Declaration Form

The application uses React Router for navigation and Axios for API communication. Form data is submitted to a backend API endpoint when the form is completed.